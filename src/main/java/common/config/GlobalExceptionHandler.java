package common.config;

import common.utils.ComputerInfo;
import common.utils.NetworkUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.swing.*;
import java.awt.*;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.LinkedList;
import java.util.List;

// 1. 自定义异常类
@Getter
class ApplicationException extends Exception {
    private final ErrorType errorType;

    public ApplicationException(String message) {
        super(message);
        this.errorType = ErrorType.ERROR;
    }

    public ApplicationException(String message, ErrorType errorType) {
        super(message);
        this.errorType = errorType;
    }

    public ApplicationException(String message, Throwable cause) {
        super(message, cause);
        this.errorType = ErrorType.ERROR;
    }

}

// 2. 错误类型枚举
@Getter
enum ErrorType {
    INFO("提示", "information.png"),
    WARNING("警告", "warning.png"),
    ERROR("错误", "error.png");

    private final String title;
    private final String iconPath;

    ErrorType(String title, String iconPath) {
        this.title = title;
        this.iconPath = iconPath;
    }

}

@Slf4j
// 3. 全局异常处理器
@Setter
public class GlobalExceptionHandler {
    private volatile static GlobalExceptionHandler instance;
    private Component parentComponent;
    private ErrorDialog errorDialog; // 持有ErrorDialog实例

    private GlobalExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler((t, e) -> handleException(e));
    }

    public static GlobalExceptionHandler getInstance() {
        if (instance == null) {
            instance = new GlobalExceptionHandler();
        }
        return instance;
    }

    public void handleException(Throwable throwable) {
        log.error("未显式捕获的异常:", throwable);

        SwingUtilities.invokeLater(() -> {
            if (errorDialog == null) {
                errorDialog = new ErrorDialog(parentComponent);
            }
            errorDialog.addException(throwable);
            errorDialog.setVisible(true); //FIXME：启动时阻塞了UI
        });
    }
}

// 4. 错误显示对话框
class ErrorDialog extends JDialog {
    public static final int DIALOG_WIDTH = 400;
    public static final int DIALOG_HEIGHT = 300;

    private JLabel messageLabel;
    private JTextArea detailsArea;
    private JScrollPane scrollPane;
    private JButton detailsButton;
    private JButton nextButton;
    private JButton prevButton;
    private final List<Throwable> exceptions = new LinkedList<>();
    private int currentIndex = 0;

    public ErrorDialog(Component parent) {
        super(SwingUtilities.getWindowAncestor(parent), "", ModalityType.APPLICATION_MODAL);
        initComponents();
    }

    private void initComponents() {
        setSize(DIALOG_WIDTH, DIALOG_HEIGHT);
        setLocationRelativeTo(getOwner());
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        messageLabel = new JLabel();
        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        scrollPane = new JScrollPane(detailsArea);

        JPanel messagePanel = new JPanel(new BorderLayout(10, 0));
        JLabel iconLabel = new JLabel(); // 可以设置图标
        messagePanel.add(iconLabel, BorderLayout.WEST);
        messagePanel.add(messageLabel, BorderLayout.CENTER);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        detailsButton = new JButton("详情");
        nextButton = new JButton("下一条");
        prevButton = new JButton("上一条");
        JButton closeButton = new JButton("关闭");
        buttonPanel.add(detailsButton);
        buttonPanel.add(prevButton);
        buttonPanel.add(nextButton);
        buttonPanel.add(closeButton);

        mainPanel.add(messagePanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        scrollPane.setVisible(false);

        detailsButton.addActionListener(e -> {
            boolean show = !scrollPane.isVisible();
            scrollPane.setVisible(show);
            detailsButton.setText(show ? "隐藏详情" : "详情");
            pack();
            setLocationRelativeTo(getOwner());
        });

        nextButton.addActionListener(e -> showException(currentIndex + 1));
        prevButton.addActionListener(e -> showException(currentIndex - 1));
        closeButton.addActionListener(e -> dispose());

        setContentPane(mainPanel);
    }

    public void addException(Throwable e) {
        exceptions.add(e);
        // 修改这里，总是显示最新的异常
        showException(exceptions.size() - 1);
    }

    private void showException(int index) {
        if (index >= 0 && index < exceptions.size()) {
            currentIndex = index;
            Throwable e = exceptions.get(index);
            ComputerInfo computerInfo = NetworkUtils.getComputerInfo();
            if (e instanceof ApplicationException) {
                setTitle(String.format("%s(%s)", ((ApplicationException) e).getErrorType().getTitle(), computerInfo));
                messageLabel.setText("<html><body style='width: 250px'>" + e.getMessage() + "</body></html>");
            } else {
                setTitle(String.format("%s(%s)", ErrorType.ERROR.getTitle(), computerInfo));
                messageLabel.setText("<html><body style='width: 250px'>发生未预期的错误</body></html>");
            }
            detailsArea.setText(getStackTraceString(e));
            updateButtonStates();
        }
    }

    private String getStackTraceString(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    private void updateButtonStates() {
        prevButton.setEnabled(currentIndex > 0);
        nextButton.setEnabled(currentIndex < exceptions.size() - 1);
    }
}
