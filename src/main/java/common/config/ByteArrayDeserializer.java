package common.config;

import com.alibaba.fastjson2.J<PERSON><PERSON>eader;
import com.alibaba.fastjson2.reader.ObjectReader;

import java.lang.reflect.Type;
import java.util.Base64;

public class ByteArrayDeserializer implements ObjectReader<byte[]> {
    @Override
    public byte[] readObject(JSONReader jsonReader, Type type, Object fieldName, long features) {
        if (jsonReader.isString()) {
            String base64String = jsonReader.readString();
            return Base64.getDecoder().decode(base64String);
        }
        // 如果不是字符串类型，则跳过该值
        jsonReader.skipValue();
        return null;
    }
}