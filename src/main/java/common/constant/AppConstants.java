package common.constant;

import common.utils.CompileUtils;

import java.util.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-8 11:23
 * @description :
 * @modified By :
 * @since : 2022-4-8
 */
public class AppConstants {
    public static final String APP_NAME = "IC_ST FlyTest";

    public static final String PUBLIC_PROJECT = "__PUBLIC_PROJECT__";
    public static final int DEFAULT_PORT = 8083;
    public static String BASE_COMPUTER_IP = "127.0.0.1";
    public static final int PLATFORM_CODE = 8;
    public static final int WEBSOCKET_TIMEOUT = 0;  //禁用心跳超时，防止后端阻塞时候被重置
    //    private static String BASE_COMPUTER_IP = "************";
    public static final String COMPILE_DATE = CompileUtils.getCompileDate();
    public static final String SERVER_NAME = "AITestX";

    public static final String APP_DESCRIPTION = "";

    public static final int SERVER_PORT = 12399;
    public static final int APP_DEFAULT_WIDTH = 1760;
    public static final int APP_DEFAULT_HEIGHT = 940;

    public static final int defaultBaudRate = 115200;
    public static final List<Integer> baudRates = new ArrayList<>(Arrays.asList(300, 1200, 2400, 4800, 9600, 19200, 38400, 57600,
            defaultBaudRate, 230400, 460800, 921600));

    public static final int defaultSampleRate = 44100;
    public static final List<Integer> sampleRates = new ArrayList<>(Arrays.asList(8000, 11025, 16000, 22050, 32000, 48000, defaultSampleRate, 88200,
            96000, 176400, 192000, 384000));

    public static final String rs232Protocol = "RS232";
    public static final String usbProtocol = "USB";

    public static final String DEVICE_MANAGER = "设备管理";
    public static final String ACTION_MANAGER = "动作列表";
//    public static final List<String> serialProtocols = new ArrayList<>(Collections.singletonList(usbProtocol));
    public static final List<String> serialProtocols = new ArrayList<>(Arrays.asList(rs232Protocol, usbProtocol));


    public static void setBaseComputerIp(String url) {
        BASE_COMPUTER_IP = url;
    }

    public static String getBaseComputerIp() {
        return BASE_COMPUTER_IP;
    }

}
