package common.constant;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-2 11:21
 * @description :
 * @modified By :
 * @since : 2022-6-2
 */
public class ResourceConstant {
    private static final String basePictureResource = "pictures";
    private static final String baseConfigResource = "config";

    /**
     * 组合路径
     *
     * @param paths 路径...
     * @return 组合后的路径
     */
    public static String combinePath(String... paths) {
        StringBuilder combinedPath = new StringBuilder();
        for (String path : paths) {
            combinedPath.append(path).append("/");
        }
        return combinedPath.substring(0, combinedPath.length() - 1);
    }

    /**
     * 配置文件资源
     */
    public static final class Config {
        public static final String baseDevicesPath = combinePath(baseConfigResource, "devices");

        public static final class Excel {
            public static final String caseDefineConfig = combinePath(baseConfigResource, "excel", "config.excel/自动化脚本序列定义表.xlsx");
        }

        public static final class Devices {
            public static final String wavePulsesPath = combinePath(baseDevicesPath, "wavePulses");
            public static final String kikusuiPulsePath = combinePath(wavePulsesPath, "kikusui");

            public static final String headUnitKikusuiPulseConfig = combinePath(kikusuiPulsePath, "headUnit", "车载主机菊水标准脉冲.json");
            public static final String displayKikusuiPulseConfig = combinePath(kikusuiPulsePath, "display", "显示屏菊水标准脉冲.json");

        }

    }

    /**
     * 设备图片资源
     */
    public static final class DevicePictures {
        public static final String basePath = combinePath(basePictureResource, "devices");

        public static final String adsPulsePictures = combinePath(basePath, "adsPulse");

        public static final String adsStartPulsePicture = combinePath(adsPulsePictures, "StartPulse.png");
        public static final String adsTimeLevelResetPulsePicture = combinePath(adsPulsePictures, "TimeLevelResetPulse.jpeg");
        public static final String adsVoltageLevelResetPulsePicture = combinePath(adsPulsePictures, "VoltageLevelResetPulse.jpeg");
        public static final String adsShortInterruptionOfPowerSupplyPicture = combinePath(adsPulsePictures, "ShortInterruptionOfPowerSupply.jpeg");

    }


    public static final class IconLayout {
        public static final String basePath = combinePath(basePictureResource, "icons");
        public static final String faviconIconPath = combinePath(basePath, "favicon.png");

    }

    public static final class MainFrontLayout {
        public static final String basePath = combinePath(basePictureResource, "main");
        public static final String casePath = combinePath(basePath, "testcase.png");
        public static final String devicePath = combinePath(basePath, "device.png");
        public static final String reportPath = combinePath(basePath, "report.png");
        public static final String globalConfigPath = combinePath(basePath, "globalConfig.png");
        public static final String runLogPath = combinePath(basePath, "runLog.png");
    }

    /**
     * 通用布局资源
     */
    public static final class CommonLayout {
        public static final String basePath = combinePath(basePictureResource, "common");

        public static final String addToScriptIconPath = combinePath(basePath, "addToScript.png");
        public static final String toggleOnIconPath = combinePath(basePath, "toggleOn.png");
        public static final String toggleOffIconPath = combinePath(basePath, "toggleOff.png");

        public static final String pauseIconPath = combinePath(basePath, "pause.png");
        public static final String pauseOffIconPath = combinePath(basePath, "pausePressed.png");

        public static final String newToggleOnIconPath = combinePath(basePath, "toggle_on.png");
        public static final String newToggleOffIconPath = combinePath(basePath, "toggle_off.png");

        public static final String connectedIconPath = combinePath(basePath, "connected.png");

        public static final String disconnectIconPath = combinePath(basePath, "disconnect.png");
        public static final String deviceLoading0IconPath = combinePath(basePath, "deviceLoading_step_0.png");
        public static final String deviceLoading1IconPath = combinePath(basePath, "deviceLoading_step_1.png");
        public static final String deviceLoading2IconPath = combinePath(basePath, "deviceLoading_step_2.png");
        public static final String deviceLoading3IconPath = combinePath(basePath, "deviceLoading_step_3.png");

        public static final String exportIconPath = combinePath(basePath, "export.png");
        public static final String loadingIconPath = combinePath(basePath, "loading.gif");

        public static final String failIconPath = combinePath(basePath, "failDir.png");
        public static final String templateIconPath = combinePath(basePath, "templateDir.png");
        public static final String failVideoIconPath = combinePath(basePath, "failVideo.png");
    }


    /**
     * 左边布局资源
     */
    public static final class LeftLayout {
        public static final String basePath = combinePath(basePictureResource, "left");
        public static final String androidIconPath = combinePath(basePath, "android.png");
        public static final String cameraIconPath = combinePath(basePath, "camera.png");
        public static final String cameraSettingIconPath = combinePath(basePath, "cameraSetting.png");
        public static final String cameraSettingActiveIconPath = combinePath(basePath, "cameraSettingActive.png");
        public static final String takePhotoIconPath = combinePath(basePath, "takePhoto.png");
        public static final String streamPlayIconPath = combinePath(basePath, "stream_play.png");
        public static final String streamPauseIconPath = combinePath(basePath, "stream_pause.png");

        public static final String newScriptFileIconPath = combinePath(basePath, "new_script.png");
        public static final String associateScriptFileIconPath = combinePath(basePath, "associate_script.png");

        public static final String openRightPanelViewIconPath = combinePath(basePath, "right_collapse.png");

        public static final String closeRightPanelViewIconPath = combinePath(basePath, "left_collapse.png");

        public static final String debugIconPath = combinePath(basePath, "debug.png");
        public static final String addToScriptIconPath = combinePath(basePath, "addToScript.png");

        public static final String clockIconPath = combinePath(basePath, "clock.png");
        public static final String messageIconPath = combinePath(basePath, "message.png");
        public static final String fileIconPath = combinePath(basePath, "file.png");
        public static final String copyIconPath = combinePath(basePath, "copy.png");
        public static final String detectIconPath = combinePath(basePath, "delect.png");
        public static final String setIconPath = combinePath(basePath, "set.png");
        public static final String triangleIconPath = combinePath(basePath, "triangle.png");
        public static final String succeedIconPath = combinePath(basePath, "succeed.png");
        public static final String warningIconPath = combinePath(basePath, "warning.png");
        public static final String wrongIconPath = combinePath(basePath, "wrong.png");
        public static final String pauseIconPath = combinePath(basePath, "pause.png");
    }

    /**
     * 右边布局资源
     */
    public static final class RightLayout {
        public static final String basePath = combinePath(basePictureResource, "right");

        public static final String runScriptIconPath = combinePath(basePath, "runScript.png");
        public static final String stopScriptIconPath = combinePath(basePath, "stopScript.png");

        public static final String pauseScriptIconPath = combinePath(basePath, "pauseScript.png");

        public static final String powerIconPath = combinePath(basePath, "power.png");
        public static final String traceIconPath = combinePath(basePath, "trace.png");

        public static final String collapseIconPath = combinePath(basePath, "up_collapse.png");
        public static final String openCardCollapseIconPath = combinePath(basePath, "up_collapse.png");
        public static final String closeCardCollapseIconPath = combinePath(basePath, "down_collapse.png");

        public static final String configDeviceIconPath = combinePath(basePath, "deviceConfig.png");
        public static final String connectDeviceIconPath = combinePath(basePath, "connectDevice.png");
        public static final String disconnectDeviceIconPath = combinePath(basePath, "disconnectDevice.png");
        public static final String addDeviceIconPath = combinePath(basePath, "addDevice.png");
    }

    public static final class TestCase {
        public static final String basePath = combinePath(basePictureResource, "testcase");
        public static final String addTestCaseIconPath = combinePath(basePath, "addTestCase.png");
        public static final String exportTestCaseIconPath = combinePath(basePath, "exportTestCase.png");
        public static final String importTestCaseIconPath = combinePath(basePath, "importTestCase.png");
        public static final String startTestCaseIconPath = combinePath(basePath, "startTestCase.png");
        public static final String endTestCaseIconPath = combinePath(basePath, "endTestCase.png");
        public static final String startTestLoopIconPath = combinePath(basePath, "startTestLoop.png");
        public static final String testLogIconPath = combinePath(basePath, "testLog.png");

    }

    public static final class TestScript {
        public static final String basePath = combinePath(basePictureResource, "script");
        public static final String waitingTimeIconPath = combinePath(basePath, "waitingTime.png");
        public static final String executeCmdIconPath = combinePath(basePath, "executeCmd.png");
        public static final String newLoopIconPath = combinePath(basePath, "newLoop.png");
        public static final String executingExpressionsIconPath = combinePath(basePath, "executingExpressions.png");
        public static final String showDialogIconPath = combinePath(basePath, "showDialog.png");
        public static final String randomOperationIconPath = combinePath(basePath, "randomOperations.png");

    }

    public static final class DevicesDisplay {
        public static final String basePath = combinePath(basePictureResource, "device");
        public static final String videoCaptureNoSinglePath = combinePath(basePath, "noSignal.png");
    }

    public static final class ActionSequence {
        public static final String basePath = combinePath(basePictureResource, "action");
        public static final String startTestCaseIconPath = combinePath(basePath, "startTestCase.png");
        public static final String endTestCaseIconPath = combinePath(basePath, "endTestCase.png");
        public static final String pauseTestCaseIconPath = combinePath(basePath, "pauseTestCase.png");
        public static final String importTestCaseIconPath = combinePath(basePath, "importTestCase.png");
        public static final String refreshTestCaseIconPath = combinePath(basePath, "refreshTestCase.png");
        public static final String saveTestCaseIconPath = combinePath(basePath, "saveTestCase.png");
        public static final String headSettingsIconPath = combinePath(basePath, "headSettings.png");
        public static final String testLogIconPath = combinePath(basePath, "testLog.png");
        public static final String excelToolsIconPath = combinePath(basePath, "excelTools.png");
        public static final String openFolderIconPath = combinePath(basePath, "openFolder.png");
        public static final String clearLogIconPath = combinePath(basePath, "clearLog.png");
        public static final String searchLogIconPath = combinePath(basePath, "searchLog.png");
        public static final String logManagementIconPath = combinePath(basePath, "logManagement.png");

    }

    /**
     * 获取设备图片路径
     *
     * @param deviceType 设备类型
     * @return 图片路径
     */
    public static String getDeviceImagePath(String deviceType) {
        return combinePath(combinePath(basePictureResource, "device"), deviceType + ".png");
    }


}
