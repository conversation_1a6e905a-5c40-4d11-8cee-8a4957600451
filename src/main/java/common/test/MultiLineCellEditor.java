package common.test;

import javax.swing.*;
import javax.swing.table.TableCellRenderer;
import java.awt.*;
import java.awt.event.*;
import java.util.HashMap;
import java.util.Map;

public class MultiLineCellEditor extends DefaultCellEditor {
    private final JTextArea textArea;
    private int editRow = -1;
    private int editColumn = -1;
    private Object previousValue; // 存储之前的值以供撤销操作使用
    private final JTable table;

    // 添加一个HashMap来跟踪每行的原始高度
    private final Map<Integer, Integer> originalRowHeights = new HashMap<>();

    public MultiLineCellEditor(JTable table, CustomTableCellEditor tableCellEditor) {
        super(new JTextField());
        this.table = table;
        textArea = new JTextArea();
        textArea.setLineWrap(true);
        textArea.setWrapStyleWord(true);
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_NEVER);
        editorComponent = scrollPane;

        // 在构造函数中初始化原始行高
        for (int i = 0; i < table.getRowCount(); i++) {
            originalRowHeights.put(i, table.getRowHeight(i));
        }

        textArea.addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                super.focusGained(e);
                textArea.selectAll();
            }

            @Override
            public void focusLost(FocusEvent e) {
                super.focusLost(e);
                stopCellEditing();
                tableCellEditor.updateCell(editRow);
            }
        });

        textArea.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                super.keyPressed(e);
                if (e.getKeyCode() == KeyEvent.VK_ESCAPE) {
                    stopCellEditing();
                    tableCellEditor.updateCell(editRow);
                } else if (e.getKeyCode() == KeyEvent.VK_Z && (e.getModifiers() & InputEvent.CTRL_MASK) != 0) {
                    // Ctrl + Z 撤销操作
                    if (previousValue != null) {
                        textArea.setText(previousValue.toString());
                        // 撤销后，重新计算并设置行高以适应文本区域的新高度
                        updateRowHeight();
//                        tableCellEditor.updateCell(editRow);
                    }
                } else if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    // 按下Enter键时，在文本区域中添加换行符
                    int caretPosition = textArea.getCaretPosition();
                    textArea.insert("\n", caretPosition); // 使用正确的换行符
                    e.consume(); // 阻止JTable默认的结束编辑行为

                    // 更新单元格的高度以适应文本区域的新高度
                    updateRowHeight();
                }
            }

            private void updateRowHeight() {
                int lineCount = textArea.getLineCount(); // 获取文本区域的实际行数
                int fontHeight = textArea.getFontMetrics(textArea.getFont()).getHeight(); // 获取字体高度
                int rowHeight = fontHeight * lineCount; // 计算行高
                // 添加文本区域的边框空间
                Insets insets = textArea.getInsets();
                rowHeight += insets.top + insets.bottom;
                // 如果新行高大于当前行高，则更新行高
                if (rowHeight > table.getRowHeight(editRow)) {
                    table.setRowHeight(editRow, rowHeight);
                }
            }
        });
    }

    @Override
    public Component getTableCellEditorComponent(JTable table, Object value,
                                                 boolean isSelected, int row, int column) {
        editRow = row;
        editColumn = column;
        previousValue = value; // 保存当前单元格的值
        textArea.setText(value != null ? value.toString() : "");

        // 当开始编辑时，保存原始行高
        if (!originalRowHeights.containsKey(row)) {
            originalRowHeights.put(row, table.getRowHeight(row));
        }

        return editorComponent;
    }

    @Override
    public boolean stopCellEditing() {
        String text = textArea.getText();
        table.getModel().setValueAt(text, editRow, editColumn);
        // 重置行高为原始高度
        table.setRowHeight(editRow, originalRowHeights.get(editRow));
        fireEditingStopped();
        return true;
    }
}

// 自定义的单元格渲染器
class MultiLineCellRenderer extends JTextArea implements TableCellRenderer {
    public MultiLineCellRenderer() {
        setLineWrap(true);
        setWrapStyleWord(true);
        setOpaque(true);
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value,
                                                   boolean isSelected, boolean hasFocus, int row, int column) {
        if (isSelected) {
            setForeground(table.getSelectionForeground());
            setBackground(table.getSelectionBackground());
        } else {
            setForeground(table.getForeground());
            setBackground(table.getBackground());
        }
        setFont(table.getFont());
        setText((value != null) ? value.toString() : "");
        adjustRowHeight(table, row, this);
        return this;
    }

    private void adjustRowHeight(JTable table, int row, JTextArea textArea) {
        int lineCount = textArea.getLineCount();
        int rowHeight = textArea.getFontMetrics(textArea.getFont()).getHeight() * lineCount;
        rowHeight += textArea.getInsets().top + textArea.getInsets().bottom;
        if (rowHeight > table.getRowHeight(row)) {
            table.setRowHeight(row, rowHeight);
        }
    }
}

interface CustomTableCellEditor {
    void updateCell(int editRow);
}