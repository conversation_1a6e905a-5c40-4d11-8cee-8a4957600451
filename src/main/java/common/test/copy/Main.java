package common.test.copy;

import javax.swing.*;
import javax.swing.event.CellEditorListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellEditor;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableColumnModel;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EventObject;
import java.util.List;

class TableFrame extends JFrame {
    private final JTable table;
    private final DefaultTableModel tableModel;

    public TableFrame() {
        super("JTable with Records");

        // 初始化数据模型
        tableModel = new DefaultTableModel(new Object[]{"动作序列", "复制按钮"}, 0);

        // 创建表格
        table = new JTable(tableModel);

        // 创建按钮编辑器
        TableCellEditor buttonEditor = new TableCellEditor() {
            private final JButton button = new JButton("复制");

            @Override
            public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {

                // 创建一个匿名内部类来保存当前行和列的值
                ActionListener listener = e -> {
                    // 获取当前行的前一列的动作序列
                    int prevColumn = column - 1; // 前一列的索引
                    if (prevColumn >= 0 && prevColumn < tableModel.getColumnCount()) {
                        Object sequenceObj = tableModel.getValueAt(row, prevColumn);
                        String sequence = sequenceObj != null ? sequenceObj.toString() : "";
                        // 将前一列的动作序列复制到剪切板
                        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
                        clipboard.setContents(new StringSelection(sequence), null);
                    }
                    // 结束编辑
                    table.getCellEditor().stopCellEditing();
                };
                button.addActionListener(listener);
                return button;
            }

            @Override
            public Object getCellEditorValue() {
                return "";
            }

            @Override
            public boolean isCellEditable(EventObject anEvent) {
                return true;
            }

            @Override
            public boolean shouldSelectCell(EventObject anEvent) {
                return true;
            }

            @Override
            public boolean stopCellEditing() {
                return true;
            }

            @Override
            public void cancelCellEditing() {
            }

            @Override
            public void addCellEditorListener(CellEditorListener l) {
                // 空实现
            }

            @Override
            public void removeCellEditorListener(CellEditorListener l) {
                // 空实现
            }
        };

        // 创建按钮渲染器
        TableCellRenderer buttonRenderer = (table, value, isSelected, hasFocus, row, column) -> {
            JButton button = new JButton("复制");
            button.setHorizontalAlignment(SwingConstants.CENTER); // 设置按钮水平居中
            return button;
        };

        // 设置第二列的单元格编辑器和渲染器
        TableColumnModel columnModel = table.getColumnModel();
        columnModel.getColumn(1).setCellRenderer(buttonRenderer);
        columnModel.getColumn(1).setCellEditor(buttonEditor);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(table);

        // 添加滚动面板到主窗口
        this.add(scrollPane, BorderLayout.CENTER);

        // 设置窗口属性
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setSize(400, 300);
        this.setVisible(true);

        // 设置单元格高度为25像素
        this.table.setRowHeight(20);
    }

    public void populateTable(List<String> records) {
        //遍历传入的list对象
        records.forEach(record -> tableModel.addRow(new Object[]{record, ""}));
    }
}

public class Main {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 设置Nimbus UI
//                UIManager.setLookAndFeel(new NimbusLookAndFeel());
            TableFrame frame = new TableFrame();
            try {
//                List<String> records = BackendCommunicator.fetchRecords();
                List<String> records = new ArrayList<>(Arrays.asList("123", "456", "789"));
                frame.populateTable(records);
            } catch (RuntimeException e) {
                System.err.println("Failed to fetch records: " + e.getMessage());
            }
        });
    }
}
