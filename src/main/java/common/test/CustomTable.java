package common.test;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class CustomTable extends JTable {
    public CustomTable(Object[][] data, Object[] columnNames) {
        super(new DefaultTableModel(data, columnNames) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                return String.class; // 确保返回 String 类型
            }
        });

        // 设置多行文本编辑器
        setDefaultEditor(String.class, new MultiLineCellEditor(this, editRow -> {
        }));

        // 设置多行文本渲染器
        setDefaultRenderer(String.class, new MultiLineCellRenderer());
        // 设置选择模式为单个单元格选择
        setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        setCellSelectionEnabled(true);
    }
}
