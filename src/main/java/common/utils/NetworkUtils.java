package common.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;

@Slf4j
public class NetworkUtils {

    public static String getIpAddress() {
        try {
            InetAddress ip4 = Inet4Address.getLocalHost();
            return ip4.getHostAddress();
        } catch (UnknownHostException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static ComputerInfo getComputerInfo() {
        Map<String, String> map = System.getenv();
        String userName = map.get("USERNAME");// 获取用户名
//        String computerName = map.get("COMPUTERNAME");// 获取计算机名
        String computerName = getComputerName();// 获取计算机名
        String userDomain = map.get("USERDOMAIN");// 获取计算机域名
        ComputerInfo computerInfo = new ComputerInfo();
        computerInfo.setUserName(userName);
        computerInfo.setComputerName(computerName);
        computerInfo.setUserDomain(userDomain);
        return computerInfo;
    }


    public static String getComputerName() {
        String address = "";// 计算机名称
        try {
            InetAddress addr = InetAddress.getLocalHost();
            address = addr.getHostName();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return address;
    }

    public static void main(String[] args) {
        System.out.println(NetworkUtils.getComputerInfo());
    }
}
