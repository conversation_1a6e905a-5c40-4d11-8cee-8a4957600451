package common.utils;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

public class XMLUtils {

    public static Document getDocument(String xmlPath) throws DocumentException, IOException {
        return getDocument(new File(xmlPath));
    }

    public static Document getDocument(File xmlFile) throws DocumentException, IOException {
        Reader reader = new InputStreamReader(Files.newInputStream(xmlFile.toPath()), StandardCharsets.UTF_8);
        SAXReader saxReader = new SAXReader();
        //将XML文件路径传给Document对象并返回其实例dom
        return saxReader.read(reader);
    }

    public static void writeToXML(Document dom, String xmlPath) throws Exception {
        writeToXML(dom, new File(xmlPath));
    }

    public static void writeToXML(Document dom, File xmlFile) throws Exception {
        //首先创建样式和输出流
        OutputFormat format = OutputFormat.createPrettyPrint();
        OutputStreamWriter out = new OutputStreamWriter(Files.newOutputStream(xmlFile.toPath()), StandardCharsets.UTF_8);
        XMLWriter writer = new XMLWriter(out, format);
        //写入之后关闭流
        writer.write(dom);
        writer.close();
    }
}
