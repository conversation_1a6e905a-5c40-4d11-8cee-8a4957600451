package common.utils;

import java.util.Comparator;
import java.util.List;

public class ListSorter implements Comparator<Object> {
    private final List<Object> orderRegulation;

    public ListSorter(List<Object> orderRegulation) {
        this.orderRegulation = orderRegulation;
    }

    @Override
    public int compare(Object o1, Object o2) {
        int index1 = orderRegulation.indexOf(o1);
        int index2 = orderRegulation.indexOf(o2);
        if (index1 == -1) {
            return 1;
        }
        if (index2 == -1) {
            return -1;
        }
        return index1 - index2;
    }
}