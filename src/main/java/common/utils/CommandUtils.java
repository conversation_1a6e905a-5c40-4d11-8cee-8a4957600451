package common.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-6 18:11
 * @description :
 * @modified By :
 * @since : 2022-4-6
 */
@Slf4j
public class CommandUtils {
    public static String getAppPID() {
        return ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
    }

    @Data
    private static class CommandResponse {
        private String output;
        private String error;
    }

    public static String killProcess(String processName) throws IOException {
        return executeCommand("taskkill /F /IM " + processName);
    }

    public static boolean findProcess(String processName) {
        BufferedReader reader = null;
        try {
            Process process = Runtime.getRuntime().exec("tasklist -fi " + '"' + "imagename eq " + processName + '"');
            reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(processName)) {
                    return true;
                }
            }
            return false;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }


    private static StringBuilder getStringBuilder(BufferedReader reader) throws IOException {
        StringBuilder builder = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            if (line.trim().isEmpty()) {
                continue;
            }
            builder.append(line.trim()).append(System.lineSeparator());
//            log.info(line);
        }
        return builder;
    }

    private static List<String> getStringArray(BufferedReader reader) {
        return reader.lines().filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
    }

    public static BufferedReader getProcessBufferReader(String command, String[] env, File dir) throws IOException {
        Process process = Runtime.getRuntime().exec(new String[]{"cmd", "/c", command}, env, dir);
        // 采用字符流读取缓冲池内容，腾出空间
        return new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
    }

    private static String basicExecuteCommandToString(String command, String[] env, File dir) throws IOException {
        BufferedReader bufferedReader = getProcessBufferReader(command, env, dir);
        return getStringBuilder(bufferedReader).toString();
    }

    private static List<String> basicExecuteCommandToArray(String command, String[] env, File dir) throws IOException {
        BufferedReader bufferedReader = getProcessBufferReader(command, env, dir);
        return getStringArray(bufferedReader);
    }

    //String
    public static String executeCommand(String command) throws IOException {
        return basicExecuteCommandToString(command, null, null);
    }

    public static String executeCommand(String command, String[] env) throws IOException {
        return basicExecuteCommandToString(command, env, null);
    }

    public static String executeCommand(String command, String[] env, File dir) throws IOException {
        return basicExecuteCommandToString(command, env, dir);
    }

    //Array
    public static List<String> executeCommandToArray(String command) throws IOException {
        return basicExecuteCommandToArray(command, null, null);
    }

    public static List<String> executeCommandToArray(String command, String[] env) throws IOException {
        return basicExecuteCommandToArray(command, env, null);
    }

    public static List<String> executeCommandToArray(String command, String[] env, File dir) throws IOException {
        return basicExecuteCommandToArray(command, env, dir);
    }


    public static void sendCommand(String command) {
        log.debug("执行开始:{}", command);
        BufferedReader br = null;
        try {
            File tmpFile = File.createTempFile("temp_for_command", ".txt");//新建一个用来存储结果的缓存文件
//            if (!tmpFile.exists()) {
//                tmpFile.createNewFile();
//            }
            ProcessBuilder pb = new ProcessBuilder().command("cmd.exe", "/c", command).inheritIO();
            pb.redirectErrorStream(true);//这里是把控制台中的红字变成了黑字，用通常的方法其实获取不到，控制台的结果是pb.start()方法内部输出的。
            pb.redirectOutput(tmpFile);//把执行结果输出
            pb.start().waitFor();//等待语句执行完成，否则可能会读不到结果。
            InputStream in = Files.newInputStream(tmpFile.toPath());
            br = new BufferedReader(new InputStreamReader(in));
            String line;
            while ((line = br.readLine()) != null) {
                log.debug(line);
            }
            br.close();
            br = null;
//            tmpFile.delete();
            log.debug("执行完成");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }
}
