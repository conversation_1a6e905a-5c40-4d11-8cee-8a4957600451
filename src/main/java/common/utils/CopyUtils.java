package common.utils;

import java.io.*;
import java.util.List;

public class CopyUtils {

    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T deepCopy(T originalList) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(originalList);
        oos.close();

        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        T cloneList = (T) ois.readObject();
        ois.close();

        return cloneList;
    }

    @SuppressWarnings("unchecked")
    public static <T> List<T> deepCopyList(List<T> originalList) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(originalList);
        oos.close();

        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        List<T> cloneList = (List<T>) ois.readObject();
        ois.close();

        return cloneList;
    }


}
