package common.utils;

import sdk.base.operation.Operation;

import java.util.List;

public class SdkUtils {

    public static String summaryOperationList(List<Operation> operations) {
        StringBuilder sb = new StringBuilder();
        int row = 1;
        for (Operation operation : operations) {
            if (operation.isAnnotated()) {
                continue;
            }
            sb.append(row++).append(".").append(operation.summary()).append("\n");
        }
        return sb.toString();
    }

    public static String compressOperationList(List<Operation> operations) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (Operation operation : operations) {
            sb.append(operation.fetchSimpleStringExpression()).append(",");
        }
        sb.append("]");
        return sb.toString();
    }
}
