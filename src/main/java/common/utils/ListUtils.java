package common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/4/26 14:27
 * @description :
 * @modified By :
 * @since : 2023/4/26
 **/
@Slf4j
public class ListUtils {
    public static List<String> splitCellData(String string) {
        List<String> list = new ArrayList<>();
        if (!StringUtils.isEmpty(string)) {
            String[] dataArr = string.split("\n");
            Collections.addAll(list, dataArr);
        }
        return list;
    }


    public static String cellContentWrap(List<String> cellContentList) {
        String content = "";
        if (cellContentList != null && !cellContentList.isEmpty()) {
            content = String.join("\n", cellContentList);
        }
        return content;
    }


    public static <T> void listToModel(List<?> list, T t) throws Exception {
        Field[] fields = t.getClass().getDeclaredFields();
        if (list.size() != fields.length) {
            return;
        }
        for (int k = 0, len = fields.length; k < len; k++) {
            // 根据属性名称,找寻合适的set方法
            String fieldName = fields[k].getName();
            String setMethodName = "set" + fieldName.substring(0, 1).toUpperCase()
                    + fieldName.substring(1);
            Method method;
            Class<?> clazz = t.getClass();
            try {
                method = clazz.getMethod(setMethodName, fields[k].getType());
            } catch (SecurityException ex) {
                log.error(ex.getMessage(), ex);
                return;
            } catch (NoSuchMethodException e1) {
                String newMethodName = "set" + fieldName.substring(0, 1).toLowerCase()
                        + fieldName.substring(1);
                try {
                    method = clazz.getMethod(newMethodName, fields[k].getType());
                } catch (SecurityException | NoSuchMethodException e) {
                    log.error(e.getMessage(), e);
                    return;
                }
            }
            method.invoke(t, list.get(k));
        }
    }


    public static Object[] compareDifferent(Object[] arr1, Object[] arr2) {
        Set<Object> set1 = new HashSet<>(Arrays.asList(arr1));
        Set<Object> set2 = new HashSet<>(Arrays.asList(arr2));
        Set<Object> set3 = new HashSet<>(set2);
        set3.addAll(set1);//set3 集合有去重特性
        set1.retainAll(set2);//retainAll():保留包含在指定 collection 中的元素；
        set3.removeAll(set1);//	removeAll(); 移除 set 中那些包含在指定 collection 中的元素 ;
        return set3.toArray();
    }

    public static List<Object> setListOrder(List<Object> orderRegulation, List<Object> targetList) {
        List<Object> sortedList = new ArrayList<>(targetList);
        sortedList.sort(new ListSorter(orderRegulation));
        // sortedList即为按照orderRegulation排序后的结果
        return sortedList;
    }

    public static String findCommonElement(List<String> list1, List<String> list2) {
        // 将两个列表转换为Set以消除重复项并计算交集
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = list2 == null ? new HashSet<>() : new HashSet<>(list2);
        Set<String> commonElements = new HashSet<>(set1);
        commonElements.retainAll(set2);
        // 输出共同元素
        if (!commonElements.isEmpty()) {
            for (String element : commonElements) {
                return element;
            }
        }
        return null;
    }

}
