package common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Properties;

import static common.utils.DateUtils.timeZoneTransfer;

@Slf4j
public class CompileUtils {

    public static String getCompileDate() {
        String compileDate = "";
        try {
            Properties properties = new Properties();
            InputStream in = CompileUtils.class.getClassLoader().getResourceAsStream("env.properties");
            properties.load(in);
            // 处理东八区时间
            compileDate = timeZoneTransfer(properties.getProperty("maven.build.time"));
            assert in != null;
            in.close();
        } catch (Exception e) {
            log.error("getCompileDate Exception: ", e);
        }
        return compileDate;
    }

}