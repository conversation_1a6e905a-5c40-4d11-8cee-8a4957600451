package common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class DateUtils {

    public static String simpleDateFileName() {
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return simpleDateFormat.format(date);
    }

    public static String getNowForFile() {
        Date dNow = new Date();
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return ft.format(dNow);
    }

    public static String getNow() {
        Date dNow = new Date();
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return ft.format(dNow);
    }

    /**
     * 时区转换(UTC转GMT+8:00)
     *
     * @param utcTime 时间字符串
     * @return 时间字符串
     */
    public static String timeZoneTransfer(String utcTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = sdf.parse(utcTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        Date locatlDate = null;
        assert utcDate != null;
        String localTime = sdf.format(utcDate.getTime());
        try {
            locatlDate = sdf.parse(localTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        return sdf.format(locatlDate);
    }

    /**
     * 计算给定日期字符串与当前时间的时间差。
     *
     * @param dateString 时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
     * @return 时间差，单位为毫秒
     */
    public static long calculateTimeDifference(String dateString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // 设置为UTC时区，根据实际情况调整

        try {
            // 将输入的日期字符串转换为Date对象
            Date targetDate = sdf.parse(dateString);
            // 获取当前时间的时间戳
            long currentTimeMillis = System.currentTimeMillis();
            // 计算目标日期与当前时间的时间差
            return Math.abs(currentTimeMillis - targetDate.getTime());
        } catch (Exception e) {
            // 如果解析失败，抛出运行时异常
            throw new RuntimeException("Failed to calculate time difference due to parsing error", e);
        }
    }
}
