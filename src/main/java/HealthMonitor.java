import lombok.extern.slf4j.Slf4j;
import ui.base.AppInfo;

import java.util.Timer;
import java.util.TimerTask;

@Slf4j
public class HealthMonitor {
    private final static int MAX_RETRIES = 5;
    private final Timer timer;
    private final String serverPid;
    private volatile TimerTask monitorTask;  // 添加TimerTask引用

    public HealthMonitor(AppInfo appInfo) {
        serverPid = appInfo.getServerPid();
        log.info("检测到服务端PID:{}", serverPid);
        timer = new Timer(true);
    }

    public void start() {
        if (serverPid == null) {
            return;
        }
        monitorTask = new TimerTask() {
            String message;
            int retryCount = 0;


            @Override
            public void run() {
                try {
                    boolean pidExists = checkProcessExists();

                    if (!pidExists) {
                        retryCount++;
                        log.warn("未检测到服务端PID: {}，第{}次重试", serverPid, retryCount);

                        if (retryCount >= MAX_RETRIES) {
                            message = "服务端PID " + serverPid + " 经过" + MAX_RETRIES + "次确认后确实不存在，准备退出";
                            log.warn(message);
                            // 取消当前任务
                            cancel();
                            // 停止定时器
                            timer.cancel();
                            // 清理定时器队列
                            timer.purge();
                            // 最后退出程序
                            System.exit(0);
                        }
                    } else {
                        retryCount = 0;
                    }
                } catch (Exception e) {
                    log.error("检测服务端PID错误: ", e);
                }
            }

            private boolean checkProcessExists() {
                try {
                    int pid = Integer.parseInt(serverPid);
                    Process proc = new ProcessBuilder("cmd.exe", "/c", "tasklist /fi \"PID eq " + pid + "\"").start();
                    try (java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(proc.getInputStream(), java.nio.charset.Charset.forName("GBK")))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.contains(String.valueOf(pid))) {
                                log.debug("通过tasklist找到进程 {}", pid);
                                return true;
                            }
                        }
                    }
                    log.debug("通过tasklist未找到进程 {}", pid);
                    return false;
                } catch (Exception e) {
                    log.warn("tasklist命令调用异常，不视为进程退出", e);
                    return true;
                }
            }
        };
        timer.schedule(monitorTask, 0, 30000);
    }

    // 添加停止方法
    public void stop() {
        if (monitorTask != null) {
            monitorTask.cancel();
        }
        timer.cancel();
        timer.purge();
        log.info("flytest pid检查已停止");
    }

    // 添加资源清理
    @Override
    protected void finalize() throws Throwable {
        stop();
        super.finalize();
    }
}
