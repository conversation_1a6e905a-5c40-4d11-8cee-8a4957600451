package llm;

import com.alibaba.fastjson2.JSON;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class QaLLM extends LLMProvider {

    /**
     * 发送问答请求到指定接口
     *
     * @param query 用户输入的问题
     * @return 接口返回的结果
     */
    public static String sendQARequest(String query) throws Exception {
        // 创建URL连接
        // URL参数编码
        String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8.toString());
        String urlString = LLM_QA_URL + "?user_input=" + encodedQuery;

        // 创建URL连接
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置POST方法
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Accept", "application/json");

        // 检查响应码
        if (connection.getResponseCode() != 200) {
            // 读取错误信息
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    connection.getErrorStream(), StandardCharsets.UTF_8))) {
                StringBuilder errorMsg = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    errorMsg.append(line);
                }
                throw new RuntimeException("HTTP错误码: " + connection.getResponseCode()
                        + ", 错误信息: " + errorMsg);
            }
        }

        // 读取响应
        StringBuilder responseBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(
                connection.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                responseBuilder.append(line);
            }
        }

        connection.disconnect();
        return JSON.parseObject(responseBuilder.toString(), LLMResponse.class).getResult();
    }
}