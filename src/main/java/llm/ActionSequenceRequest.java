package llm;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ActionSequenceRequest {

    private String department;
    private String condition = "";
    private String action = "";
    private String expect = "";

    @JSONField(name = "user_prompt")
    private String userPrompt = "";

    @JSONField(name = "system_prompt")
    private String systemPrompt = "";

    private String user;

}