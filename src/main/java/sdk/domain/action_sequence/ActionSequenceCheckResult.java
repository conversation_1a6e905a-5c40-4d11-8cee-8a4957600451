package sdk.domain.action_sequence;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ActionSequenceCheckResult {

    private List<ActionSequence> actionSequenceList;

    private boolean checkOk;

    private boolean executed;

    private boolean executeOk;


    public Map<String, String> errors() {
        Map<String, String> errorMap = new LinkedHashMap<>();
        for (ActionSequence actionSequence : actionSequenceList) {
            if (actionSequence.getActionSequenceError() == null) {
                continue;
            }
            errorMap.put(actionSequence.getRawExpression(), actionSequence.getActionSequenceError().getErrorMessage());
        }
        return errorMap;
    }
}
