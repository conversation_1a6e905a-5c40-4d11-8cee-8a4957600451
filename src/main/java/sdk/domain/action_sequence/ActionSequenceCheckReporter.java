package sdk.domain.action_sequence;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ActionSequenceCheckReporter {

    private ActionSequenceCheckResult preconditionCheckResult;

    private ActionSequenceCheckResult operationStepCheckResult;

    private ActionSequenceCheckResult expectResultCheckResult;

    private boolean checkOk;

    private boolean executed;

    private boolean executeOk;

    private boolean terminated;

    private String errorMessage;

    private ActionSequenceActualResult actualResult;

    private ActionSequenceContext actionSequenceContext;



    public Map<String, String> errors() {
        Map<String, String> map = new LinkedHashMap<>();
        if (preconditionCheckResult != null) {
            map.putAll(preconditionCheckResult.errors());
        }
        if (operationStepCheckResult != null) {
            map.putAll(operationStepCheckResult.errors());
        }
        if (expectResultCheckResult != null) {
            map.putAll(expectResultCheckResult.errors());
        }
        return map;
    }

}