package sdk.domain.action_sequence;

import lombok.Data;
import sdk.domain.SmokingTestConfigModel;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseModel;
import ui.layout.right.components.testcase.TestStep;

import java.util.List;

@Data
public class ActionSequenceContext {
    private String uuid;
    public static final int ALL = 0;
    public static final int PRECONDITION = 1;
    public static final int OPERATION_STEP = 2;
    public static final int EXPECT_RESULT = 3;
    private List<TestStep> precondition;
    private List<TestStep> operationStep;
    private List<TestStep> expectResult;
    private int sequenceType;
    private boolean pressureMode;
    private boolean simulated;
    private boolean pauseWhenTestFailed; //是否在测试失败时暂停
    private double testNo;
    private String tcId;
    private String testKey;
    private String tableName;
    private int row;
    private int totalCycleTimes;
    private int currentCycleTimes;
    private int rowCurrentTestTimes;
    private int rowTotalTestTimes;
    private String userLogPath;
    private String projectName;
    private boolean singleCase;
    private boolean cancelSingleTestCheckAll;
    private boolean cancelSingleTestCheckAny;
    private ExcelCaseModel excelCaseModel;
    private ActionSequenceTestConfig testConfig;
    private SmokingTestConfigModel smokingTestConfigModel;
}