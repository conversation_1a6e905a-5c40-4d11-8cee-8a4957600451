package sdk.domain.robot;

import com.alibaba.fastjson2.annotation.JSONType;
import lombok.Getter;
import lombok.Setter;

/**
 * 坐标信息
 */
@Getter
@Setter
@JSONType(orders = {"x", "y", "z", "r", "slideRail"})
public class MoveEntity {
    private double x;
    private double y;
    private double z;
    private double r;
    private double slideRail;
    private Integer user;
    private Integer tool;
    private Integer speed;
    private Integer acc;

    private boolean joint = false;

    @Override
    public String toString() {
        return String.format("(%f,%f,%f,%f,%f)", x, y, z, r, slideRail);
    }
}
