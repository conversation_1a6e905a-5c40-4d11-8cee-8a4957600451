package sdk.domain.robot;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import sdk.domain.complex.CoordinatesRoi;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-24 17:42
 * @description :
 * @modified By :
 * @since : 2022-7-24
 */
@Data
public class RobotCoordinates implements Serializable {

    private Long id;

    private String uuid;

    private String name;

    private double x;

    private double y;

    private double z;

    private double r;

    private double slideRail;

    private Integer projectId;

    private Integer deviceId;

    private String projectName;

    private String deviceUniqueCode;

    @JsonIgnore
    @JSONField(serialize = false)
    private CoordinatesRoi coordinatesRoi;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RobotCoordinates that = (RobotCoordinates) o;
        return Double.compare(that.x, x) == 0 && Double.compare(that.y, y) == 0 && Double.compare(that.z, z) == 0 && Double.compare(that.r, r) == 0 && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, x, y, z, r);
    }
}
