package sdk.domain.robot;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 带速度的机械臂坐标
 * @date: 2024/8/26 17:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpeedCoordinates extends OperationObject {
    private String coordinateName;
    private int speed;
    private int acceleration;

    @Override
    public String getFriendlyString() {
        return String.format("坐标:%s%s%s", coordinateName,
                speed != 0 ? String.format(" 速度:%d", speed) : "",
                acceleration != 0 ? String.format(" 加速度:%d", acceleration) : "");
    }
}
