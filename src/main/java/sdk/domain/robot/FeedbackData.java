package sdk.domain.robot;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-21 9:37
 * @description :
 * @modified By :
 * @since : 2022-7-21
 */
@Data
public class FeedbackData {

    private String name;

    private double j1;
    private double j2;
    private double j3;
    private double j4;

    private double x;
    private double y;
    private double z;
    private double r;

    private Double slideRail;

    private Integer xyzSpeedFactor;

    private Integer xyzAccelerationSpeedFactor;
    private Integer modeCode;
    private String mode;

    private String digitalInputs;
    private String digitalOutputs;

    private byte user;

    private String errorMessage;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FeedbackData that = (FeedbackData) o;
        return Objects.equals(x, that.x) && Objects.equals(y, that.y) && Objects.equals(z, that.z);
    }

    @Override
    public int hashCode() {
        return Objects.hash(x, y, z);
    }

    public String friendlyCoordinates() {
        return String.format("(%s,%s,%s,%s)", x, y, z, r);
    }
}
