package sdk.domain.bus;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/10/16 19:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CanPtsComparison extends CanPts {
    private String canType;

    private String sendMessageId;
    private String sendMessage;
    private String comparisonMessageId;
    private String comparisonMessage;



    @Override
    public String getFriendlyString() {
        return String.format("发送:%s->%s / 检测:%s->%s", getHexId(sendMessageId), sendMessage, getHexId(comparisonMessageId), comparisonMessage);
    }
}
