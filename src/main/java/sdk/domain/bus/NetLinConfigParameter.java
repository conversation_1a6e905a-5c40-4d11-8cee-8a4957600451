package sdk.domain.bus;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/5/13 9:58
 * @Version: 1.0
 * @Desc : netCan配置信息
 */
@Data
public class NetLinConfigParameter extends BusConfigParameter{

    private String localPort = "4001";//本地端口
    private String workMode = "客户端";//工作模式
    private String ip = "*************";//ip地址
    private String workPort = "8000";//工作端口
    private int linMode;//LIN 工作模式，从站为 0，主站为 1。
    private int linBaud;//LIN 波特率，取值 1000~20000
    public int chkSumMode;  //校验方式，1-经典校验 2-增强校验 3-自动(即经典校验跟增强校验都会进行轮询)
    private boolean terminalResistanceEnabled;//终端电阻 "使能"/"禁能"


}
