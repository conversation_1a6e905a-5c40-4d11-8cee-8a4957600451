package sdk.domain.bus;

import common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import sdk.base.operation.OperationObject;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CanMessageRealTimeSave extends OperationObject {
    private String filePath;
    private String filterType;
    private String filterText;
    private String saveType;
    private Integer fileSizeMB = 300;
    private Integer recordCount = 200000;

    @Override
    public String getFriendlyString() {
        if (StringUtils.isEmpty(filterText)) {
            return String.format("保存路径：%s", filePath);
        } else {
            return String.format("保存路径：%s，过滤条件:%s+%s", filePath, filterType, filterText);

        }
    }
}
