package sdk.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.BaseHttpClient;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 13:57
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Project extends BaseHttpClient {

    //测试项目id
    private Integer id;

    //测试项目名称
    private String name;

    //测试项目Model名
    private String model;

    private boolean communal;

    //项目客户id
    private Integer customerId;

    //项目部门id
    private Integer departmentId;

    //项目信息id
    private Integer projectInfoId;

//    //项目客户名
//    private String customerName;
//
//    //项目部门名
//    private String departmentName;

}
