package sdk.domain.image;

import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.ImageAlgorithms;
import sdk.base.operation.OperationObject;
import sdk.base.operation.OperationTarget;
import sdk.domain.screen.Rect;
import ui.layout.left.display.components.container.picture.RoiRect;

import java.io.*;


@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class TemplateImageConfig extends OperationObject implements Serializable {

    private String templateName;

    private String algorithm;

    private Double threshold = 0.8;

    private RoiRect roi;

    private double timeout; //超时时间/s

    private Rect imageSize;  //原始图片大小

    private boolean colorMatchEnabled; //启用颜色匹配叠加

    private double recognizedDuration; //识别持续时间/s

    private int recognizedInterval = 1;//对比帧间隔/s

    private String matchedText; //识别文字

    private boolean distanceEnabled; //启用距离检测

    @JsonIgnore
    @JSONField(serialize = false)
    private OperationTarget guideDevice; //引导设备

    public void setRoi(RoiRect roi) {
        if (roi == null) {
            roi = new RoiRect();
        }
        this.roi = roi;
    }

    public static TemplateImageConfig buildByOperationObject(Object object) {
        if (object == null) {
            return null;
        }
        if (object instanceof String) {
            return null;
        }
        //Android滑动和点击会进入这里 检查必要字段是否存在，不存在不转换
        if (object instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) object;
            if (jsonObject.containsKey("templateName")) {
                try {
                    return jsonObject.to(TemplateImageConfig.class);
                } catch (JSONException e) {
                    log.error(e.getMessage(), e);
                    return null;
                }
            } else {
                return null;
            }
        }
        return null;
    }

    public TemplateImageConfig copy() {
        TemplateImageConfig templateImageConfig;
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(this);

            // 将流序列化成对象
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            templateImageConfig = (TemplateImageConfig) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        return templateImageConfig;
    }

    @Override
    public String toString() {
        if (algorithm.equals(ImageAlgorithms.ocrMatching.getAlgorithmName())) {
            return String.format("(文字识别)%s / %s%%", matchedText, threshold * 100);
        } else {
            return String.format(
                    "%s / %s / %.2f%%%s%s%s%s%s",
                    templateName,
                    ImageAlgorithms.getImageAlgorithmFriendlyName(algorithm),
                    threshold * 100,
                    timeout > 0 ? String.format(" / %.2fs", timeout) : "",
                    recognizedDuration <= 0 ? " / 检测1次" : String.format(" / 持续检测%.2fs", recognizedDuration),
                    roi == null ? "" : String.format("/(%s,%s,%s,%s)",
                            roi.getPointStart() == null ? "NA" : roi.getPointStart().getX(),
                            roi.getPointStart() == null ? "NA" : roi.getPointStart().getY(),
                            roi.getPointEnd() == null ? "NA" : roi.getPointEnd().getX(),
                            roi.getPointEnd() == null ? "NA" : roi.getPointEnd().getY()),
                    guideDevice != null ? String.format("/%s", guideDevice.getAliasName()) : "",
                    distanceEnabled ? " / 启用距离检测" : ""
            );
        }
    }

}
