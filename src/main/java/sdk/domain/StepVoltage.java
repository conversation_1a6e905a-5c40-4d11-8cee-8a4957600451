package sdk.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

@EqualsAndHashCode(callSuper = true)
@Data
public class StepVoltage extends OperationObject {

    private float startVoltage; //起始电压
    private float endVoltage; //终止电压
    private float stepVoltage;//步进电压
    private int stepInterval;//步进间隔(s)

    @Override
    public String getFriendlyString() {
        return String.format("步进电压:%.2fV~%.2fV", startVoltage, endVoltage);
    }
}
