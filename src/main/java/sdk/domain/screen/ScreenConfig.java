package sdk.domain.screen;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ui.config.json.devices.DeviceConfig;

@EqualsAndHashCode(callSuper = true)
@Data
public class ScreenConfig extends DeviceConfig {

    private String serialAliasName;

    private DisplayConfig display;

    private TouchPointMatrixConfig touchPointMatrix;

    public ScreenConfig() {
        display = new DisplayConfig();
        touchPointMatrix = new TouchPointMatrixConfig();
    }

}