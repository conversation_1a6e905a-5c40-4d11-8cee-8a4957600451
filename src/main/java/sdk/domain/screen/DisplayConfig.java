package sdk.domain.screen;

import lombok.Data;

@Data
public class DisplayConfig {

    private PreciousPoint leftTop;

    private PreciousPoint leftBottom;

    private PreciousPoint rightTop;

    private PreciousPoint rightBottom;

    private PreciousPoint center;

    private Rect screen;

    private ScreenCheckParameters screenCheckParameters;


    public DisplayConfig() {
        leftTop = new PreciousPoint();
        leftBottom = new PreciousPoint();
        rightTop = new PreciousPoint();
        rightBottom = new PreciousPoint();
        center = new PreciousPoint();
        screen = new Rect();
        screenCheckParameters = new ScreenCheckParameters();
    }

}
