package sdk.domain.screen;

import lombok.AllArgsConstructor;
import lombok.Data;
import sdk.domain.robot.MoveEntity;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class PreciousPoint {

    private BigDecimal x;
    private BigDecimal y;
    private BigDecimal z;
    private BigDecimal r;

    public PreciousPoint() {

    }


    public PreciousPoint(double x, double y, double z, double r) {
        this.x = BigDecimal.valueOf(x);
        this.y = BigDecimal.valueOf(y);
        this.z = BigDecimal.valueOf(z);
        this.r = BigDecimal.valueOf(r);
    }

    public boolean isEmpty() {
        return x == null || y == null || z == null || r == null;
    }

    public MoveEntity toMoveEntity() {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(x.doubleValue());
        moveEntity.setY(y.doubleValue());
        moveEntity.setZ(z.doubleValue());
        moveEntity.setR(r.doubleValue());
        return moveEntity;
    }

    public static PreciousPoint fromMoveEntity(MoveEntity moveEntity) {
        return new PreciousPoint(moveEntity.getX(), moveEntity.getY(), moveEntity.getZ(), moveEntity.getR());
    }
}
