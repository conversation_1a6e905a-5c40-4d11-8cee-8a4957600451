package sdk.domain.screen;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
public class AutoClickerDataPackage extends OperationObject {


    //通道号
    private int num;

    //持续时间（毫秒)
    private int hold;

    //间隔时间（毫秒)
    private int interval;

    //点击次数
    private int times;

    @Override
    public String getFriendlyString() {
        return String.format("通道:%d / 点击次数:%d / 持续时间:%d / 间隔:%d ", num, times, hold, interval);
    }

}
