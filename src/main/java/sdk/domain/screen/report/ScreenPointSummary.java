package sdk.domain.screen.report;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 报点测试总结
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScreenPointSummary extends TouchSummary implements Serializable {

    private TestPoint testPoint; //实际测试点

    private BackTracePoint reportPoint; //失败测试报点

    private Double distanceDeviation; //报点和实际测试距离偏差

}
