package sdk.domain;

import lombok.Data;
import sdk.base.operation.Operation;

import java.util.ArrayList;
import java.util.List;

@Data
public class TestScriptFileContent implements Cloneable {

    private long id;

    private String uuid;

    private int testCycle;

    private List<Operation> operationList;

    private String fileContent;

    @Override
    public TestScriptFileContent clone() throws CloneNotSupportedException {
        TestScriptFileContent testScriptFileContent = (TestScriptFileContent) super.clone();
        if (operationList != null) {
            List<Operation> cloneOperationList = new ArrayList<>();
            for (Operation operation : operationList) {
                Operation cloneOperation = operation.clone();
                cloneOperationList.add(cloneOperation);
            }
            testScriptFileContent.setOperationList(cloneOperationList);
        }
        return testScriptFileContent;
    }


    public static void main(String[] args) {
        Operation operation = new Operation();
        System.out.println(System.identityHashCode(operation) == System.identityHashCode(operation));

        Operation clone = operation.clone();
        System.out.println(operation.hashCode() == clone.hashCode());
        System.out.println(System.identityHashCode(operation) == System.identityHashCode(clone));

    }
}