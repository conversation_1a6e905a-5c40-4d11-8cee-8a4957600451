package sdk.domain;

import lombok.Data;

import java.awt.*;
import java.io.Serializable;

@Data
public class OperationLabel implements Serializable {
    private String color;
    private String name;

    public OperationLabel(Color color, String name) {
        setSystemColor(color);
        this.name = name;
    }

    // 转换方法
    public void setSystemColor(Color color) {
        this.color = String.format("#%02x%02x%02x",
                color.getRed(), color.getGreen(), color.getBlue());
    }

    public Color getSystemColor() {
        return Color.decode(color);
    }

    @Override
    public String toString() {
        return name;
    }
}
