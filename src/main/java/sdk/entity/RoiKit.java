package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.complex.PercentTemplateRoi;
import sdk.domain.image.TemplateRoiQuery;
import sdk.entity.interfaces.IRoi;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 14:05
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoiKit extends BaseHttpClient implements IRoi {

    //Roi类型id
    private Integer id;

    //Roi类型名
    private String name;

    private static Integer rectTypeId;

    public static Integer getRectTypeId() {
        if (rectTypeId == null) {
            rectTypeId = new RectRoiType().getId();
        }
        return rectTypeId;
    }

    public static class RectRoiType extends RoiKit {

        public RectRoiType() {
            JsonResponse<RoiKit> response = defaultGetJsonResponse(UrlConstants.TemplateRoiUrls.GET_RECT_ROI_TYPE,
                    new TypeReference<JsonResponse<RoiKit>>() {
                    });
            if (response.isOk()) {
                setId(response.getData().getId());
                setName(response.getData().getName());
            }
        }
    }

    public static class CircleRoiType extends RoiKit {

        public CircleRoiType() {
            JsonResponse<RoiKit> response = defaultGetJsonResponse(UrlConstants.TemplateRoiUrls.GET_RECT_CIRCLE_TYPE,
                    new TypeReference<JsonResponse<RoiKit>>() {
                    });
            if (response.isOk()) {
                setId(response.getData().getId());
                setName(response.getData().getName());
            }
        }
    }

    @Override
    public PercentTemplateRoi fetchPercentTemplateRoi(TemplateRoiQuery templateRoiQuery) {
        JsonResponse<PercentTemplateRoi> response = defaultPostJsonResponse(UrlConstants.TemplateRoiUrls.GET_ROI,
                templateRoiQuery,
                new TypeReference<JsonResponse<PercentTemplateRoi>>() {
                });
        if (response.isOk()) {
            return response.getData();
        }
        return null;
    }

    @Override
    public List<PercentTemplateRoi> fetchAllPercentTemplateRoiList(TemplateRoiQuery templateRoiQuery) {
        JsonResponse<List<PercentTemplateRoi>> response = defaultPostJsonResponse(UrlConstants.TemplateRoiUrls.GET_ALL_ROI,
                templateRoiQuery,
                new TypeReference<JsonResponse<List<PercentTemplateRoi>>>() {
                });
        return response.isOk() ? response.getData() : new ArrayList<>();

    }


}
