package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.interfaces.ICapture;

import java.util.List;

/**
 * @author: QinHao
 * @description:
 * @date: 2024/12/23 17:54
 */
public class VideoCaptureDevice extends DefaultVisionDevice implements ICapture {
    public VideoCaptureDevice() {
        super(DeviceModel.VideoCapture.VIDEO_CAPTURE);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.VIDEO_CAPTURE.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_VIDEO_CAPTURE;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<VideoCaptureDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<VideoCaptureDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<VideoCaptureDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVideoCaptureDevices(getDeviceModel()),
                new TypeReference<JsonResponse<List<VideoCaptureDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult screenshot() {
        return callOperationMethod(DeviceMethods.videoCaptureScreenShoot);
    }

    public void pauseScreenshot() {
        callOperationMethod(DeviceMethods.pauseScheduledVideoCaptureScreenShoot);
    }

    public void resumeScreenshot() {
        callOperationMethod(DeviceMethods.resumeScheduledVideoCaptureScreenShoot);
    }

    public void startScreenshot() {
        callOperationMethod(DeviceMethods.startScheduledVideoCaptureScreenShoot);
    }

}
