package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.entity.interfaces.IConfig;
import ui.layout.left.display.components.tappane.config_mgmt.GlobalConfig;

public class ConfigKit extends BaseHttpClient implements IConfig {
    @Override
    public JsonResponse<String> updateGlobalConfig(GlobalConfig globalConfig) {
        return defaultPostJsonResponse(UrlConstants.ConfigUrls.UPDATE_GLOBAL_CONFIG, globalConfig, new TypeReference<JsonResponse<String>>() {
        });
    }

    @Override
    public GlobalConfig loadGlobalConfig() {
        JsonResponse<GlobalConfig> resp = defaultGetJsonResponse(UrlConstants.ConfigUrls.LOAD_GLOBAL_CONFIG, new TypeReference<JsonResponse<GlobalConfig>>() {
        });
        GlobalConfig globalConfig = null;
        if (resp.isOk()) {
            globalConfig = resp.getData();
        }
        return globalConfig == null ? new GlobalConfig() : globalConfig;
    }

}
