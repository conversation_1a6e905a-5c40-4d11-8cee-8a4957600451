package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: QinHao
 * @description:
 * @date: 2025/2/26 15:51
 */
public class ResistanceQRDevice extends Device {
    private static final Map<String, ResistanceQRDevice> deviceMap = new HashMap<>();

    public static ResistanceQRDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            ResistanceQRDevice device = new ResistanceQRDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }
    public ResistanceQRDevice() {
        super(DeviceModel.Resistance.QR10X);
    }

    public ResistanceQRDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.RESISTANCE_DEVICE.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_RESISTANCE;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<ResistanceQRDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<ResistanceQRDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<ResistanceQRDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel()),
                new TypeReference<JsonResponse<List<ResistanceQRDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

}
