package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.execution.FailLog;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.CriticalVoltage;
import sdk.domain.CustomizePulse;
import sdk.domain.Device;
import sdk.domain.StepVoltage;
import sdk.domain.monitor.MonitorDataPackage;
import sdk.entity.interfaces.IPower;
import ui.model.testScript.TestFailObserver;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 16:41
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
public class PowerDevice extends Device implements IPower, TestFailObserver {

    private static final Map<String, PowerDevice> deviceMap = new HashMap<>();

    public static PowerDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            PowerDevice device = new PowerDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public PowerDevice() {
        super(DeviceModel.Power.IT68xx);
    }

    public PowerDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.POWER.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_POWER;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<PowerDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<PowerDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        String commProtocol = getCommProtocol();
        String url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel());
        if (commProtocol != null) {
            //存在通讯协议
            if (commProtocol.equals(AppConstants.usbProtocol)) {
                url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel());
            }
        }
        JsonResponse<List<PowerDevice>> resp = defaultGetJsonResponse(url, new TypeReference<JsonResponse<List<PowerDevice>>>() {
        });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult outputOn() {
        return callOperationMethod(DeviceMethods.outputOn);
    }

    @Override
    public OperationResult outputOn(int channel) {
        return callOperationMethod(channel, DeviceMethods.outputOn);
    }

    @Override
    public OperationResult outputOff() {
        return callOperationMethod(DeviceMethods.outputOff);
    }

    @Override
    public OperationResult outputOff(int channel) {
        return callOperationMethod(channel, DeviceMethods.outputOff);

    }

    @Override
    public OperationResult setVoltage(float voltage) {
        return callOperationMethod(DeviceMethods.setVoltage, voltage);
    }

    @Override
    public OperationResult setVoltage(int channel, float voltage) {
        return callOperationMethod(channel, DeviceMethods.setVoltage, voltage);
    }

    @Override
    public OperationResult setVoltageWithUnit(int channel, String voltage) {
        return callOperationMethod(channel, DeviceMethods.setVoltageWithUnit, voltage);
    }

    @Override
    public OperationResult setStepVoltage(Integer channel, StepVoltage stepVoltage) {
        return callOperationMethod(channel, DeviceMethods.setStepVoltage, stepVoltage);
    }

    @Override
    public OperationResult monitorCurrent(Integer channel, MonitorDataPackage monitorDataPackage) {
        return callOperationMethod(channel, DeviceMethods.monitorCurrent, monitorDataPackage);
    }

    @Override
    public OperationResult monitorVoltage(Integer channel, MonitorDataPackage monitorDataPackage) {
        return callOperationMethod(channel, DeviceMethods.monitorVoltage, monitorDataPackage);
    }
    @Override
    public OperationResult setCurrentWithUnit(int channel, String current) {
        return callOperationMethod(channel, DeviceMethods.setCurrent, current);
    }


//    @Override
//    public OperationResult compareVoltage(Integer channel, String condition) {
//        return callOperationMethod(channel, DeviceMethods.compareVoltage, condition);
//    }
//
//    @Override
//    public OperationResult compareCurrent(Integer channel, String condition) {
//        return callOperationMethod(channel, DeviceMethods.compareCurrent, condition);
//    }

    @Override
    public OperationResult loadWavePulse(int memory) {
        return callOperationMethod(DeviceMethods.loadWavePulse, memory);
    }

    @Override
    public OperationResult stopWavePulse() {
        return callOperationMethod(DeviceMethods.stopWavePulse);
    }

    @Override
    public OperationResult loadCustomizePulse(CustomizePulse customizePulse) {
        return callOperationMethod(DeviceMethods.loadCustomizePulse, customizePulse);
    }

    @Override
    public OperationResult searchMinimumCriticalVoltage(CriticalVoltage criticalVoltage) {
        return callOperationMethod(DeviceMethods.searchMinimumCriticalVoltage, criticalVoltage);

    }

    @Override
    public OperationResult fetchVoltage() {
        return callOperationMethod(DeviceMethods.fetchVoltage);
    }

    @Override
    public OperationResult fetchOutput() {
        return callOperationMethod(DeviceMethods.fetchOutput);
    }

    @Override
    public void saveFailLog(FailLog failLog) {
        //用于获取当前电压
    }

}
