package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.TestScriptFile;
import sdk.domain.TestScriptFileContent;
import sdk.domain.TestScriptFileSelector;
import sdk.entity.interfaces.ITestScript;

import java.util.List;

/**
 * Excel测试脚本
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestScriptKit extends BaseHttpClient implements ITestScript {

    private Integer id;

    private String preCondition;

    private String operationalStep;

    private String expectResult;

    private boolean passed;

    private String comment;

    @Override
    public JsonResponse<String> clearAllTestScriptFiles(TestScriptFile testScriptFile) {
        return defaultDeleteJsonResponse(UrlConstants.TestScriptUrls.CLEAR_ALL_TEST_SCRIPT_FILE,
                testScriptFile,
                new TypeReference<JsonResponse<String>>() {
                });
    }

    @Override
    public JsonResponse<List<TestScriptFile>> fetchAllTestScriptFiles(TestScriptFile testScriptFile) {
        return defaultPostJsonResponse(UrlConstants.TestScriptUrls.GET_ALL_TEST_SCRIPT_FILE,
                testScriptFile,
                new TypeReference<JsonResponse<List<TestScriptFile>>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFile> addTestScriptFile(TestScriptFile testScriptFile) {
        return defaultPostJsonResponse(UrlConstants.TestScriptUrls.ADD_TEST_SCRIPT_FILE,
                testScriptFile,
                new TypeReference<JsonResponse<TestScriptFile>>() {
                });
    }

    @Override
    public JsonResponse<String> deleteTestScriptFile(String testScriptFileUUID) {
        return defaultDeleteJsonResponse(UrlConstants.TestScriptUrls.get_delete_testScript_url(testScriptFileUUID),
                new TypeReference<JsonResponse<String>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFile> renameTestScriptFile(TestScriptFile testScriptFile) {
        return defaultUpdate(UrlConstants.TestScriptUrls.RENAME_TEST_SCRIPT_FILE,
                testScriptFile,
                new TypeReference<JsonResponse<TestScriptFile>>() {
                });
    }

    public JsonResponse<TestScriptFile> updateTestScriptContent(TestScriptFileContent testScriptFileContent) {
        return defaultUpdate(UrlConstants.TestScriptUrls.UPDATE_TEST_SCRIPT_FILE_CONTENT,
                testScriptFileContent,
                new TypeReference<JsonResponse<TestScriptFile>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFile> updateTestScriptTestCycle(TestScriptFileContent testScriptFileContent) {
        return defaultUpdate(UrlConstants.TestScriptUrls.UPDATE_TEST_SCRIPT_TEST_CYCLE,
                testScriptFileContent,
                new TypeReference<JsonResponse<TestScriptFile>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFile> updateTestScriptFile(TestScriptFile testScriptFile) {
        return defaultUpdate(UrlConstants.TestScriptUrls.UPDATE_TEST_SCRIPT_FILE,
                testScriptFile,
                new TypeReference<JsonResponse<TestScriptFile>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFileSelector> selectAll(TestScriptFileSelector testScriptFileSelector) {
        return defaultUpdate(UrlConstants.TestScriptUrls.SELECT_TEST_SCRIPT_FILE,
                testScriptFileSelector,
                new TypeReference<JsonResponse<TestScriptFileSelector>>() {
                });
    }

    @Override
    public JsonResponse<TestScriptFileContent> loadTestScriptFileContent(String testScriptFileUUID) {
        return defaultGetJsonResponse(UrlConstants.TestScriptUrls.get_load_testScript_url(testScriptFileUUID),
                new TypeReference<JsonResponse<TestScriptFileContent>>() {
                });
    }
}
