package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.entity.interfaces.IUdpConfig;

import java.util.Map;

public class UdpConfigKit extends BaseHttpClient implements IUdpConfig {

    @Override
    public JsonResponse<String> load(String projectName) {
        return defaultPostJsonResponse(UrlConstants.UdpUrls.LOAD_A2L_CONFIG_FILE,
                projectName,
                new TypeReference<JsonResponse<String>>() {
                });
    }

    @Override
    public JsonResponse<String> parse(Map<String, String> map) {
        return defaultPostJsonResponse(UrlConstants.UdpUrls.PARSE_A2L_CONFIG_FILE, map, new TypeReference<JsonResponse<String>>() {
        });
    }
}
