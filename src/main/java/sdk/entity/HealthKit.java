package sdk.entity;

import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.constants.UrlConstants;

import java.io.IOException;

@Slf4j
public class HealthKit extends BaseHttpClient {

    public static String getServerPid() {
        try {
            return getForString(UrlConstants.ServerUrls.GET_SERVER_PID, false);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }
}
