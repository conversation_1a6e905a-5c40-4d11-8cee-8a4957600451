package sdk.entity;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.MonitorType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.MessageText;
import sdk.domain.monitor.MonitorAction;
import ui.config.json.devices.serial.SerialConfig;
import ui.layout.left.display.components.container.usbI2c.IICConfig;
import ui.layout.left.display.components.container.usbI2c.IICMessage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class I2CDevice extends SerialDevice {


    private static final Map<String, I2CDevice> deviceMap = new HashMap<>();

    public static I2CDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            I2CDevice device = new I2CDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public I2CDevice() {
        super(DeviceModel.UsbI2C.USB_I2C);
    }

    public I2CDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SERIAL;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<I2CDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<I2CDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<I2CDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllUsbI2c(getDeviceModel()),
                new TypeReference<JsonResponse<List<I2CDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    public OperationResult send(MessageText messageText) {
        return callOperationMethod(DeviceMethods.sendToDevice, messageText);
    }


    @Override
    public SerialConfig loadConfig(String projectName) {
        OperationResult operationResult = callOperationMethod(DeviceMethods.loadConfig, projectName);
        SerialConfig config;
        JSONObject object = ((JSONObject) operationResult.getData());
        if (operationResult.isOk()) {
            if (object == null) {
                config = new SerialConfig();
            } else {
                config = object.to(SerialConfig.class);
            }
        } else {
            config = new SerialConfig();
        }
        config.setProject(projectName);
        return config;
    }

    @Override
    public JsonResponse<OperationResult> monitorLog() {
        MonitorAction monitorAction = MonitorAction.of(getAliasName(), MonitorType.LOG_DATA);
        return defaultPostJsonResponse(UrlConstants.MonitorUrls.SERVER_URL_OF_MONITOR_MGMT,
                monitorAction,
                new TypeReference<JsonResponse<OperationResult>>() {
                });
    }

    public OperationResult setI2CParameter(OperationMethod operationMethod, IICConfig iicConfig) {
        return callOperationMethod(operationMethod, iicConfig);
    }

    public OperationResult setI2CIO(OperationMethod operationMethod, IICConfig iicConfig) {
        return callOperationMethod(operationMethod, iicConfig);
    }

    public OperationResult adcGather(OperationMethod adcGather, IICConfig iicConfig) {
        return callOperationMethod(adcGather, iicConfig);
    }

    public OperationResult pwmOutputBegin(OperationMethod pwmOutputBegin, IICConfig iicConfig) {
        return callOperationMethod(pwmOutputBegin, iicConfig);
    }

    public OperationResult pwmOutputStop(OperationMethod pwmOutputStop, IICConfig iicConfig) {
        return callOperationMethod(pwmOutputStop, iicConfig);
    }

    public OperationResult send(OperationMethod send, IICMessage iicMessage) {
        return callOperationMethod(send, iicMessage);
    }

    public OperationResult sendAndMatch(OperationMethod sendAndMatch, IICMessage iicMessage) {
        return callOperationMethod(sendAndMatch, iicMessage);
    }
}
