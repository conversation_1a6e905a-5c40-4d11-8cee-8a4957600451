package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.entity.interfaces.IFdxConfig;

import java.util.List;

public class FdxConfigKit extends BaseHttpClient implements IFdxConfig {

    @Override
    public JsonResponse<List<String>> readFdxFile() {
        return defaultPostJsonResponse(UrlConstants.FdxUrls.READ_CONFIG_FILE, null, new TypeReference<JsonResponse<List<String>>>() {
        });
    }

    @Override
    public JsonResponse<String> parse(List<String> filePaths) {
        return defaultPostJsonResponse(UrlConstants.FdxUrls.PARSE_CONFIG, filePaths, new TypeReference<JsonResponse<String>>() {
        });
    }
}
