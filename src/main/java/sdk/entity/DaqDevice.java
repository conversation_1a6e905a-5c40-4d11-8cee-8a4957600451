package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.monitor.MonitorDataPackage;
import sdk.domain.Device;
import sdk.entity.interfaces.IDaqDevice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DaqDevice extends Device implements IDaqDevice {

    private static final Map<String, DaqDevice> deviceMap = new HashMap<>();

    public static DaqDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            DaqDevice device = new DaqDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public DaqDevice() {
        super(DeviceModel.Daq.KEYSIGHT_34461A);
    }

    public DaqDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.DAQ.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_DAQ;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<DaqDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<DaqDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<DaqDevice>> resp;
        if (getDeviceModel().equals(DeviceModel.Daq.USB3200N)) {
            resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllDaqPorts(getDeviceModel()),
                    new TypeReference<JsonResponse<List<DaqDevice>>>() {
                    });
        } else {
            resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel()),
                    new TypeReference<JsonResponse<List<DaqDevice>>>() {
                    });
        }
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult fetchCurrent() {
        return callOperationMethod(DeviceMethods.fetchCurrent);
    }

    @Override
    public OperationResult fetchVoltage() {
        return callOperationMethod(DeviceMethods.fetchVoltage);
    }

    @Override
    public OperationResult monitorCurrent(Integer channel, MonitorDataPackage monitorDataPackage) {
        return callOperationMethod(DeviceMethods.monitorCurrent, monitorDataPackage);
    }

    @Override
    public OperationResult monitorVoltage(Integer channel, MonitorDataPackage monitorDataPackage) {
        return callOperationMethod(DeviceMethods.monitorVoltage, monitorDataPackage);
    }

}
