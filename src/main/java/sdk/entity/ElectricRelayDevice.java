package sdk.entity;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.ChannelSwitch;
import sdk.domain.Device;
import sdk.entity.interfaces.IConfigurable;
import ui.config.json.devices.electric_relay.ElectricRelayConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 继电器
 */
public class ElectricRelayDevice extends Device implements IConfigurable<ElectricRelayConfig> {

    private static final Map<String, ElectricRelayDevice> deviceMap = new HashMap<>();

    public static ElectricRelayDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            ElectricRelayDevice device = new ElectricRelayDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public ElectricRelayDevice(String deviceModel) {
        super(deviceModel);
    }

    public ElectricRelayDevice() {
        super(DeviceModel.ElectricRelay.JYDAM1600C);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ELECTRIC_RELAY_DEVICE.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ELECTRIC_RELAY;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<ElectricRelayDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<ElectricRelayDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<ElectricRelayDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel()),
                new TypeReference<JsonResponse<List<ElectricRelayDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    public OperationResult switchRelay(ChannelSwitch channelSwitch) {
        return callOperationMethod(DeviceMethods.switchRelay, channelSwitch);
    }

    @Override
    public ElectricRelayConfig loadConfig(String projectName) {
        OperationResult operationResult = callOperationMethod(DeviceMethods.loadConfig, projectName);
        ElectricRelayConfig config;
        JSONObject object = ((JSONObject) operationResult.getData());
        if (operationResult.isOk()) {
            if (object == null) {
                config = new ElectricRelayConfig();
            } else {
                config = object.to(ElectricRelayConfig.class);
            }
        } else {
            config = new ElectricRelayConfig();
        }
        config.setProject(projectName);
        return config;
    }
}
