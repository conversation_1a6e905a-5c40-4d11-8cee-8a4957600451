package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DcCollectorDevice extends Device {

    private static final Map<String, DcCollectorDevice> deviceMap = new HashMap<>();
    public static DcCollectorDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            DcCollectorDevice device = new DcCollectorDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public DcCollectorDevice() {
        super(DeviceModel.DcCollector.DC_COLLECTOR_24_DEVICE);
    }

    public DcCollectorDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.DC_COLLECTOR.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_DC_COLLECTOR;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<DcCollectorDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<DcCollectorDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        String commProtocol = getCommProtocol();
        String url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel());
        if (commProtocol != null) {
            //存在通讯协议
            if (commProtocol.equals(AppConstants.usbProtocol)) {
                url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel());
            }
        }
        JsonResponse<List<DcCollectorDevice>> resp = defaultGetJsonResponse(url, new TypeReference<JsonResponse<List<DcCollectorDevice>>>() {
        });
        return JsonResponse.retrieve(resp);
    }

}
