package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationGroup;
import sdk.constants.UrlConstants;

import java.util.ArrayList;
import java.util.List;

public class OperationGroupKit extends BaseHttpClient {

    public JsonResponse<Object> addOperationGroup(OperationGroup operationGroup) {
        return defaultPostJsonResponse(UrlConstants.OperationGroupUrls.ADD_OPERATION_GROUP,
                operationGroup,
                new TypeReference<JsonResponse<Object>>() {
                });
    }

    public JsonResponse<String> deleteOperationGroup(String groupName) {
        return defaultDeleteJsonResponse(UrlConstants.OperationGroupUrls.get_delete_operationGroup_url(groupName),
                new TypeReference<JsonResponse<String>>() {
                });
    }

    public List<OperationGroup> loadOperationGroups(String projectName) {
        JsonResponse<List<OperationGroup>> response = defaultPostJsonResponse(UrlConstants.OperationGroupUrls.LOAD_ALL_OPERATION_GROUPS,
                projectName,
                new TypeReference<JsonResponse<List<OperationGroup>>>() {
                });
        return response.isOk() ? response.getData() : new ArrayList<>();
    }

    public OperationGroup loadOperationGroup(OperationGroup operationGroup) {
        JsonResponse<OperationGroup> response = defaultPostJsonResponse(UrlConstants.OperationGroupUrls.LOAD_OPERATION_GROUP,
                operationGroup,
                new TypeReference<JsonResponse<OperationGroup>>() {
                });
        return response.isOk() ? response.getData() : null;
    }

}
