package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OscilloscopeDevice extends Device {

    private static final Map<String, OscilloscopeDevice> deviceMap = new HashMap<>();

    public static OscilloscopeDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            OscilloscopeDevice device = new OscilloscopeDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public OscilloscopeDevice() {
//        super(DeviceModel.Oscilloscope.RIGOL_800);
    }

    public OscilloscopeDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.OSCILLOSCOPE.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_OSCILLOSCOPE;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<OscilloscopeDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<OscilloscopeDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<OscilloscopeDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel()),
                new TypeReference<JsonResponse<List<OscilloscopeDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

}
