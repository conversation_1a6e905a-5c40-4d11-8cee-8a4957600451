package sdk.entity;

import common.utils.CommandUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.constants.UrlConstants;

import java.io.IOException;
import java.util.List;
import java.util.Scanner;

import static cn.hutool.crypto.digest.DigestUtil.md5Hex;
import static sdk.base.BaseHttpClient.postForString;

@Slf4j
public class VerifyKit {
    private static final String UUID_CMD = "C:\\Windows\\System32\\wbem\\wmic path win32_computersystemproduct get uuid";

    private static String getUUID() {
        List<String> uuidInfo;
        try {
            uuidInfo = CommandUtils.executeCommandToArray(UUID_CMD);
        } catch (IOException e) {
            return "";
        }
        return uuidInfo.get(1);
    }

    private static boolean verify(String password, String md5) {
        if (md5 == null || md5.isEmpty()) {
            return false;
        }
        //先从MD5码中取出之前加的“盐”和加“盐”后生成的MD5码
        char[] cs1 = new char[32];
        char[] cs2 = new char[16];
        for (int i = 0; i < 48; i += 3) {
            cs1[i / 3 * 2] = md5.charAt(i);
            cs1[i / 3 * 2 + 1] = md5.charAt(i + 2);
            cs2[i / 3] = md5.charAt(i + 1);
        }
        String salt = new String(cs2);
        //比较二者是否相同
        return md5Hex(password + salt).equals(new String(cs1));
    }

    public static void verifyFromServer() {
        String password = getUUID();
        while (true) {
            try {
                String secretKey = postForString(UrlConstants.ServerUrls.GENERATE_SECRET_KEY_FROM_SERVER, password, false);
                if (verify(password, secretKey)) {
                    log.info("身份验证通过");
                    break;
                } else {
                    log.info("身份验证失败");
                    new Scanner(System.in).nextLine();
                    System.exit(0);
                }
            } catch (IOException e) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ex) {
                    log.error(e.getMessage(), e);
                }
                continue;
            }
            break;
        }
    }

    public static void main(String[] args) {
        System.out.println(VerifyKit.getUUID());
    }

}
