package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.TestResult;
import sdk.entity.interfaces.ITestCase;

public class TestCaseKit extends BaseHttpClient implements ITestCase {

    @Override
    public TestResult queryTestResult(String testCaseUUID) {
        JsonResponse<TestResult> resp = defaultGetJsonResponse(UrlConstants.TestCaseUrls.getQueryTestResultUrl(testCaseUUID), new TypeReference<JsonResponse<TestResult>>() {
        });
        if (resp.isOk()) {
            return resp.getData();
        } else {
            return new TestResult();
        }
    }

}
