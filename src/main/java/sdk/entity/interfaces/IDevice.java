package sdk.entity.interfaces;

import sdk.base.JsonResponse;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import ui.config.json.devices.DeviceConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Device接口，方法不能出现Get开头，如必须使用，请加注解@JSONField(serialize = false)
 */
public interface IDevice {

    JsonResponse<Device> registerDevice();

    JsonResponse<Device> registerAndOpenDevice();

    JsonResponse<Device> registerDevice(Map<String, Object> operationParameter);

    JsonResponse<String> disconnectDevice();

    JsonResponse<String> unregisterDevice();

    JsonResponse<Device> registerDevice(Device device);

    JsonResponse<Device> registerDevice(Device device, Map<String, Object> operationParameter);

    JsonResponse<Device> registerAndOpenDevice(Device device);

    JsonResponse<List<? extends Device>> queryDevices();

    default List<String> queryPortNames() {
        return new ArrayList<>();
    }

    OperationResult autoOpenDevice();

    OperationResult openDevice();

    OperationResult dummyOpenDevice();

    OperationResult closeDevice();

    OperationResult sendToDevice(String message);

    OperationResult callOperationMethod(OperationMethod operationMethod);

    <T> OperationResult callOperationMethod(OperationMethod operationMethod, T operationObject);

//    <T extends Device> JsonResponse<List<T>> getDevicesByType(String deviceType, TypeReference<JsonResponse<List<T>>> typeReference);

    Device fetchDeviceByName(String deviceName);

    <T extends DeviceConfig> T loadConfig(String projectName, Class<T> clazz);

    default String loadConfigByKey(String projectName, String configKey) {
        return null;
    }

    OperationResult updateConfig(DeviceConfig deviceConfig);

    OperationResult updateConfigByKey(String project, String key, String value);
}
