package sdk.entity.interfaces;

import sdk.base.operation.OperationResult;
import sdk.domain.robot.*;
import sdk.domain.screen.events.ArcTouchEvent;
import sdk.domain.screen.events.ClickEvent;
import sdk.domain.screen.events.LineTouchEvent;

import java.awt.*;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-19 15:54
 * @description :
 * @modified By :
 * @since : 2022-7-19
 */
public interface IRobot {

    OperationResult moveJog(String command);

    OperationResult stopMoveJog();

    FeedbackData fetchFeedbackData();

    void enablePoseMonitor();

    void resumePoseMonitor();

    void pausePoseMonitor();

    OperationResult moveLine(MoveEntity moveEntity);

    OperationResult moveJoint(MoveEntity moveEntity);

    OperationResult arc(ArcEntity arcEntity);

    OperationResult returnHome();

    OperationResult autoScreenCalibration(String serialAliasName);

    OperationResult touch(MoveEntity moveEntity);

    OperationResult visionTouch(Point pixelPoint);

    OperationResult setVisionGuideZ(double visionGuideZ);

    OperationResult setVisionGuideCalibrationData(VisionGuideCalibrationData visionGuideCalibrationData);

    OperationResult fetchVisionGuideConfig();

    //    设置速度和加速度
    OperationResult setSpeedFactor(int ratio);

    OperationResult setPayLoad(int weight);

    OperationResult slide(List<MoveEntity> moveEntityList);

    OperationResult move(List<MoveEntity> moveEntityList);

    OperationResult randomTouch(List<MoveEntity> moveEntityList);

    OperationResult enableRobot();

    OperationResult disableRobot();

    OperationResult clearError();

    OperationResult searchError();

    OperationResult slideRail(MovJExtEntity movJExtEntity);

    OperationResult setUserCoordinate(int userIndex);

    OperationResult touchAndCheckTouchPoints(ClickEvent clickEvent);

    OperationResult swipeAndCheckTouchPoints(LineTouchEvent lineTouchPoint);

    OperationResult arcAndCheckTouchPoints(ArcTouchEvent arcTouchPoint);

    OperationResult longTouchCoordinate(LongTouchRobotCoordinate longTouchRobotCoordinate);

    OperationResult circle(MoveEntity moveEntity);

    OperationResult air(boolean pressure);

}
