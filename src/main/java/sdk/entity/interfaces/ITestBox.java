package sdk.entity.interfaces;

import sdk.base.operation.OperationResult;
import ui.layout.left.display.components.container.testbox.test.PWMEntity;

public interface ITestBox extends IDevice {
    OperationResult fetchResistanceBoardCard();

    OperationResult writeResistanceBoardCard(int channel, int value);

    OperationResult writeResistanceInitData(int value);

    OperationResult fetchRelayBoardCard();

    OperationResult writeRelayBoardCard(int channel, int value);

    OperationResult fetchPWMOutputBoardCard();

    OperationResult writePWMOutputBoardCard(int channel, PWMEntity pwmEntity);

    OperationResult fetchTriStateOutputBoardCard();

    OperationResult writeTriStateOutputBoardCard(int channel, int value);

    OperationResult fetchAcquisitionBoardCard();

    OperationResult fetchPWMInputBoardCard();
}
