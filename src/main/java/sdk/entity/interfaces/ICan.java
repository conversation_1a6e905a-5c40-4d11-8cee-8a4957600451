package sdk.entity.interfaces;

import common.utils.CanReplyInfo;
import sdk.base.operation.OperationResult;
import sdk.domain.bus.CanLogRealTimeSaveParameter;
import sdk.domain.bus.CanMessage;
import sdk.domain.bus.CanMessageRealTimeSave;
import sdk.domain.bus.DbcConfig;

public interface ICan {

    OperationResult sendCanMessage(Integer channel, CanMessage canMessage);

    OperationResult canReplay(CanReplyInfo canReplyInfo);

    OperationResult fetchRunningCanMessage(Integer channel);

    OperationResult stopCanMessage(Integer channel, int messageId);

    OperationResult stopAllCanMessage(Integer channel);

    OperationResult startFrameReceiver(Integer channel);

    OperationResult startDbcReceiver(Integer channel, DbcConfig dbcConfig);

    OperationResult stopFrameReceiver(Integer channel);

    OperationResult stopDbcReceiver(Integer channel);

    OperationResult closeOpenButNotConfigDevice();

    OperationResult startRealTimeData(Integer channel, CanMessageRealTimeSave canMessageRealTimeSave);

    OperationResult stopRealTimeData(Integer channel);

    OperationResult startCaptureFrameCanLog(Integer channel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter);

    OperationResult stopCaptureFrameCanLog(Integer channel);

    OperationResult startCaptureDbcCanLog(Integer channel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter);

    OperationResult stopCaptureDbcCanLog(Integer channel);

    OperationResult saveLog(Integer channel,CanMessageRealTimeSave canMessageRealTimeSave);

}
