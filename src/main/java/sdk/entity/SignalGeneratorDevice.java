package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SignalGeneratorDevice extends Device {

    private static final Map<String, SignalGeneratorDevice> deviceMap = new HashMap<>();

    public static SignalGeneratorDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            SignalGeneratorDevice device = new SignalGeneratorDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public SignalGeneratorDevice() {
//        super(DeviceModel.SignalGenerator.TK_AFG_1022);
    }

    public SignalGeneratorDevice(String deviceModel) {
        super(deviceModel);
    }
    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.SIGNAL_GENERATOR.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SIGNAL_GENERATOR;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<SignalGeneratorDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<SignalGeneratorDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<SignalGeneratorDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel()),
                new TypeReference<JsonResponse<List<SignalGeneratorDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

}
