package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.bus.LinMessage;
import sdk.entity.interfaces.ILin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LinDevice extends BusDevice implements ILin {

    private static final Map<String, LinDevice> deviceMap = new HashMap<>();

    public static LinDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            deviceMap.put(deviceModel, new LinDevice(deviceModel));
        }
        return deviceMap.get(deviceModel);
    }

    public LinDevice() {
    }

    public LinDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public OperationResult sendLinMessage(Integer channel, LinMessage linMessage) {
        return callOperationMethod(channel, DeviceMethods.sendLinMessage, linMessage);
    }

    @Override
    public OperationResult stopLinMessage(Integer channel, String idHex) {
        return callOperationMethod(channel, DeviceMethods.stopLinMessage, idHex);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.LIN.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_LIN;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<LinDevice>>() {
        });
    }


    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<LinDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<LinDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllCanPorts(getDeviceModel(), getChannel()),
                new TypeReference<JsonResponse<List<LinDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

}
