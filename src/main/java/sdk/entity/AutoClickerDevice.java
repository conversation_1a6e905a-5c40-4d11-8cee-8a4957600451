package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.screen.AutoClickerDataPackage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AutoClickerDevice extends Device {

    private static final Map<String, AutoClickerDevice> deviceMap = new HashMap<>();

    public static AutoClickerDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            AutoClickerDevice device = new AutoClickerDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public AutoClickerDevice() {
        super(DeviceModel.AutoClicker.AUTO_CLICKER);
    }

    public AutoClickerDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.AUTO_CLICKER.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_AUTO_CLICKER;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<AutoClickerDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<AutoClickerDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<AutoClickerDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllAutoClicker(getDeviceModel()),
                new TypeReference<JsonResponse<List<AutoClickerDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    public OperationResult startClick(int channel, AutoClickerDataPackage autoClickerDataPackage) {
        return callOperationMethod(channel, DeviceMethods.clickOneChannelStart, autoClickerDataPackage);
    }

}
