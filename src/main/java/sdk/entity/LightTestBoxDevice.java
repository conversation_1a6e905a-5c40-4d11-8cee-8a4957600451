package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.List;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 13:34
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
public class LightTestBoxDevice extends TestBoxDevice {

    public LightTestBoxDevice() {
        super(DeviceModel.TestBox.LIGHT_TEST_BOX);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.TEST_BOX.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TEST_BOX;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<LightTestBoxDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<LightTestBoxDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<TcpServerDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllTcpServer(getDeviceModel()),
                new TypeReference<JsonResponse<List<TcpServerDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    public static String getConfigFile() {
        JsonResponse<String> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.GET_TESTBOX_CONFIG_FILE, new TypeReference<JsonResponse<String>>() {
        });
        if (resp.isOk()) {
            return resp.getData();
        }
        return "";
    }

}
