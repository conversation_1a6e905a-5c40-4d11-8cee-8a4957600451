package sdk.base.execution;

import lombok.Data;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;

//TODO：绑定case uuid
@Data
public class ExecutionNotification {

    private Integer executionIndex; //excel行

    private String caseName;

    private int cycle;

    private int totalCycle; //历史总次数

    private int position;

    private Operation operation;

    private OperationResult operationResult;

    private ExecutionResultReport executionResultReport;

    private String extraMessage;

    private boolean userPausing;

    private boolean debugModeEnabled;

    private boolean allStepsOk;

    private String status;

}