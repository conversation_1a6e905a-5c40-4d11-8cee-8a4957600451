package sdk.base.execution;


import lombok.Data;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationContext;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-14 13:43
 * @description :
 * @modified By :
 * @since : 2022-5-14
 */
@Data
public class Execution {
    private int executionIndex;

    private OperationContext operationContext;

    private Integer testCycle = 1; //脚本执行次数

    private List<Operation> operationList;

    /**
     * 清空操作结果
     */
    public void clearOperationResults() {
        for (Operation operation : operationList) {
            operation.getOperationResultList().clear();
        }
    }
}
