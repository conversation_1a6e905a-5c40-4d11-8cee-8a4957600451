package sdk.base.execution;

public enum RemoteOperationStatus {
    RECEIVED,
    TESTING_START,
    TESTING_START_FAILED,
    NOT_TESTING,
    TESTING,
    PAUSED,
    TESTING_FAILED,
    TESTING_COMPLETED,
    TESTING_STOPPED_FAILED,
    TESTING_STOPPED_COMPLETED,
    IMPORT_START,
    IMPORT_FAILED,
    IMPORT_COMPLETED,
    C<PERSON>AR_SCRIPT_START,
    <PERSON><PERSON><PERSON>_SCRIPT_FAILED,
    C<PERSON>AR_SCRIPT_COMPLETED,
    EXCEL_CASE_TESTING_START,
    EXCEL_CASE_TESTING_START_FAILED,
    EXCEL_CASE_TESTING_START_COMPLETED,
    EXCEL_CASE_TESTING_STOP,
    EXCEL_CASE_TESTING_STOP_FAILED,
    EXCEL_CASE_TESTING_STOP_COMPLETED,
    IMPORT_EXCEL_CASE_START,
    IMPORT_EXCEL_CASE_FAILED,
    IMPORT_EXCEL_CASE_COMPLETED
}
