package sdk.base;

import common.utils.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
public class SseSession {
    private static final Integer DEFAULT_TIME_OUT = 0; //不超时
    @Getter
    private final List<String> readers;

    public SseSession() {
        readers = new CopyOnWriteArrayList<>();
    }


    /**
     * 带重连机制的流读取方法
     *
     * @param urlPath     SSE 地址
     * @param msgHandler  消息处理器
     * @param retryTimes  最大重试次数
     * @param baseDelayMs 初始延迟毫秒数（用于指数退避）
     */
    public void readStreamWithReconnect(String urlPath, MsgHandler msgHandler, int retryTimes, long baseDelayMs) {
        int attempt = 0;
        while (attempt <= retryTimes) {
            try {
                log.info("正在尝试连接 SSE 通道: {}，第 {} 次", urlPath, attempt);
                readStream(urlPath, msgHandler);
                log.info("SSE 通道已正常关闭: {}", urlPath);
                break; // 成功完成则退出循环
            } catch (Exception e) {
                log.error("SSE 连接异常: {}, 错误: {}", urlPath, e.getMessage());
                if (attempt < retryTimes) {
                    long delay = (long) (baseDelayMs * Math.pow(2, attempt));
                    log.warn("将在 {} 毫秒后尝试重新连接...", delay);
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("等待重连过程中被打断");
                        break;
                    }
                } else {
                    log.error("达到最大重试次数，停止重连");
                    break;
                }
                attempt++;
            }
        }
    }


    //读流数据
    public void readStream(String urlPath, MsgHandler msgHandler) throws Exception {
        BufferedReader reader = null;
        InputStream inputStream = null;
        try {
            log.info("SSE通道连接:{}", urlPath);
            HttpURLConnection urlConnection = getHttpURLConnection(urlPath, DEFAULT_TIME_OUT);
            inputStream = getSseInputStream(urlConnection);
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            readers.add(urlPath);
            String line;
            while (true) {
                try {
                    line = reader.readLine();
                    if (line == null) {
                        break;
                    }
                    if (!StringUtils.isBlank(line)) {
                        if (msgHandler != null) {
                            msgHandler.handleMsg(line);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    break;
                }

            }
            urlConnection.disconnect();
        } finally {
            // 服务器端主动关闭时，客户端手动关闭
//            log.info("SSE通道{}关闭", urlPath);
            readers.remove(urlPath);
            if (reader != null) {
                reader.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }


    //获取SSE输入流
    private InputStream getSseInputStream(URLConnection urlConnection) throws IOException {
        InputStream inputStream = urlConnection.getInputStream();
        return new BufferedInputStream(inputStream);
    }

    private HttpURLConnection getHttpURLConnection(String urlPath, int timeoutMill) throws IOException {
        URL url = new URL(urlPath);
        HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
        urlConnection.setDoOutput(true);
        urlConnection.setDoInput(true);
        urlConnection.setUseCaches(false);
        urlConnection.setRequestMethod("GET");
        urlConnection.setRequestProperty("Connection", "Keep-Alive");
        urlConnection.setRequestProperty("Charset", "UTF-8");
        urlConnection.setRequestProperty("Content-Type", "text/plain; charset=UTF-8");       //text/plain模式
        urlConnection.setReadTimeout(timeoutMill);  //读超时时间
        return urlConnection;
    }


    /**
     * 消息处理接口
     */
    public interface MsgHandler {
        void handleMsg(String line);
    }

    //FIXME: reader.close();阻塞造成多次点开始测试执行用例失败
    @Deprecated
    public void closeAll() {
//        System.out.println("closeAll  readers.size()---" + readers.size());
//        for (Reader reader : readers) {
//            if (reader != null) {
//                try {
//                    reader.close();
//                } catch (IOException e) {
//                      log.error(e.getMessage(), e);
//                }
//            }
//        }
        readers.clear();
    }

}