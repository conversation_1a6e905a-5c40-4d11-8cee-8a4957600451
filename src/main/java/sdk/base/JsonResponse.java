package sdk.base;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 13:22
 * @description :
 * @modified By :
 * @since : 2022-4-12
 */
@Data
public class JsonResponse<T> implements Serializable {
    private static final Integer SUCCESS = 0;
    private static final Integer FAIL = -1;

    private int code;
    private String message;
    private T data;

    public JsonResponse<T> setData(T data) {
        this.data = data;
        return this;
    }

    public static <T> JsonResponse<T> ok() {
        JsonResponse<T> response = new JsonResponse<>();
        response.setCode(SUCCESS);
        return response;
    }

    public static <T> JsonResponse<T> fail(String msg, T data) {
        JsonResponse<T> response = new JsonResponse<>();
        response.setCode(FAIL);
        response.setMessage(msg);
        response.setData(data);
        return response;
    }

    public static <T> JsonResponse<T> success(T data) {
        JsonResponse<T> response = new JsonResponse<>();
        response.setCode(SUCCESS);
        response.setData(data);
        return response;
    }

    public boolean isOk() {
        return code == SUCCESS;
    }

    public JsonResponse<T> from(JsonResponse<? extends T> resp) {
        setCode(resp.getCode());
        setMessage(resp.getMessage());
        setData(resp.getData());
        return this;
    }

//    public JsonResponse<List<? extends T>> fromArray(JsonResponse<List<? extends T>> resp) {
//        setCode(resp.getCode());
//        setMessage(resp.getMessage());
//        setData((T) resp.getData());
//        return this;
//    }

    public static <T> JsonResponse<T> retrieve(JsonResponse<? extends T> resp) {
        JsonResponse<T> response = new JsonResponse<>();
        response.setCode(resp.getCode());
        response.setMessage(resp.getMessage());
        response.setData(resp.getData());
        return response;
    }


}




