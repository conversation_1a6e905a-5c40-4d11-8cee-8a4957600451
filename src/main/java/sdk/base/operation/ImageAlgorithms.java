package sdk.base.operation;

import common.utils.ReflectionUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@AllArgsConstructor
public class ImageAlgorithms {

    private String algorithmName;
    private String friendlyName;

    public static final ImageAlgorithms cvTemplateMatching = new ImageAlgorithms("cvTemplateMatching", "全图搜索");
    public static final ImageAlgorithms strictTemplateMatching = new ImageAlgorithms("strictTemplateMatching", "精准匹配");
    public static final ImageAlgorithms perPixelTemplateMatching = new ImageAlgorithms("perPixelTemplateMatching", "逐像素匹配");
    public static final ImageAlgorithms colorMatching = new ImageAlgorithms("colorMatching", "颜色匹配");
    public static final ImageAlgorithms featureMatching = new ImageAlgorithms("featureMatching", "特征匹配");
    public static final ImageAlgorithms ocrMatching = new ImageAlgorithms("ocrMatching", "OCR识别");
    public static final ImageAlgorithms dynamicStrictTemplateMatching = new ImageAlgorithms("dynamicStrictTemplateMatching", "动态匹配");
    public static final ImageAlgorithms intelligentTemplateMatching = new ImageAlgorithms("intelligentTemplateMatching", "智能匹配");

    public static final ImageAlgorithms allMatchingAlgorithms = new ImageAlgorithms("All", "所有图像匹配算法");

    public static String getImageAlgorithmFriendlyName(String algorithmName) {
        try {
            Map<String, Object> map = ReflectionUtils.getStaticProperties(ImageAlgorithms.class);
            map.remove("log");
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(algorithmName)) {
                    return ((ImageAlgorithms) entry.getValue()).getFriendlyName();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    public static String getImageAlgorithmName(String friendlyName) {
        return getImageAlgorithmByFriendlyName(friendlyName).getAlgorithmName();
    }

    public static ImageAlgorithms getImageAlgorithmByFriendlyName(String friendlyName) {
        try {
            Map<String, Object> map = ReflectionUtils.getStaticProperties(ImageAlgorithms.class);
            map.remove("log");
            map.remove("allMatchingAlgorithms");
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() instanceof ImageAlgorithms) {
                    ImageAlgorithms imageAlgorithms = (ImageAlgorithms) entry.getValue();
                    if (imageAlgorithms.getFriendlyName().equals(friendlyName)) {
                        return imageAlgorithms;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return cvTemplateMatching;
    }


    public static Map<String, Object> getStaticProperties(Class<ImageAlgorithms> clazz) {
        try {
            Map<String, Object> map = ReflectionUtils.getStaticProperties(ImageAlgorithms.class);
            map.remove("log");
            map.remove("allMatchingAlgorithms");
            return map;
        } catch (Exception e) {
            return new HashMap<>();
        }
    }


    public static void main(String[] args) {
        System.out.println(getImageAlgorithmFriendlyName("ColorMatching"));
    }
}
