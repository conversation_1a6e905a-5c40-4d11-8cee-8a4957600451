package sdk.base.operation;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-13 16:52
 * @description :
 * @modified By :
 * @since : 2022-5-13
 */
@Data
@NoArgsConstructor
//@AllArgsConstructor
public class OperationMethod implements Serializable {

    private String methodName;

    private String keyword;

    //    @JSONField(serialize = false)
    private OperationMethod pairedOperationMethod;

    @Setter
    private Integer methodType = OperationMethodType.NORMAL.getValue();

    private String pairedCode;//配对码


    public OperationMethod(String methodName, String keyword) {
        this.methodName = methodName;
        this.keyword = keyword;
    }

    public OperationMethod(String methodName, String keyword, int methodType) {
        this(methodName, keyword);
        this.methodType = methodType;
    }

    public OperationMethod(String methodName, String keyword, OperationMethod pairedOperationMethod) {
        this.methodName = methodName;
        this.keyword = keyword;
        this.pairedOperationMethod = pairedOperationMethod;
//        this.pairedOperationMethod = new OperationMethod(pairedOperationMethod.getMethodName(), pairedOperationMethod.getKeyword());
    }

    public OperationMethod(String methodName, String keyword, OperationMethod pairedOperationMethod, int methodType) {
        this(methodName, keyword, pairedOperationMethod);
        this.methodType = methodType;
    }

    public String generatePairedCode() {
        pairedCode = DigestUtil.md5Hex(String.format("%s%d", UUID.randomUUID(), new Date().getTime()));
        return pairedCode;
    }

    public OperationMethod copy() {
        OperationMethod method = new OperationMethod();
        method.setMethodName(methodName);
        method.setKeyword(keyword);
        method.setMethodType(methodType);
        method.setPairedCode(pairedCode);
        if (pairedOperationMethod != null) {
            method.setPairedOperationMethod(pairedOperationMethod.copy());
        }
        return method;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OperationMethod that = (OperationMethod) o;
        return keyword.equals(that.keyword);
    }

    @Override
    public int hashCode() {
        return Objects.hash(keyword);
    }

    public String getSimpleStringExpression() {
        return keyword;
    }
}
