package sdk.constants.methods;

import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationMethodType;

/**
 * 设备方法
 */
public class DeviceMethods {
    // CAN
    public static final OperationMethod openChannel = new OperationMethod("打开CAN通道", "openChannel");
    public static final OperationMethod closeChannel = new OperationMethod("关闭CAN通道", "closeChannel");
    public static final OperationMethod sendCanMessage = new OperationMethod("发送CAN报文", "sendCanMessage");
    public static final OperationMethod responsiveServices = new OperationMethod("发送并校验UDS服务", "responsiveServices");
    public static final OperationMethod send3EService = new OperationMethod("发送UDS3E服务", "send3EService");
    public static final OperationMethod setUdsConfig = new OperationMethod("下发UDS配置", "setUdsConfig");
    public static final OperationMethod setCanUDS27ServerFun = new OperationMethod("发送27服务", "setCanUDS27ServerFun");

    public static final OperationMethod inspectCanMessage = new OperationMethod("检查CAN报文", "inspectCanMessage");
    public static final OperationMethod inspectCanSignal = new OperationMethod("检查CAN信号", "inspectCanSignal");
    public static final OperationMethod canReplay = new OperationMethod("回放CAN报文", "canReplay");
    public static final OperationMethod stopCanMessage = new OperationMethod("停止CAN报文", "stopCanMessage");
    public static final OperationMethod stopAllCanMessage = new OperationMethod("停止所有CAN报文", "stopAllCanMessage");
    public static final OperationMethod sendAllPeriodicCanMessage = new OperationMethod("发送所有周期CAN报文", "sendAllPeriodicCanMessage");
    public static final OperationMethod comparisonCanMessage = new OperationMethod("CAN诊断报文比对", "comparisonCanMessage");
    public static final OperationMethod sendPtsCanMessage = new OperationMethod("发送CAN诊断报文", "sendPtsCanMessage");

    public static final OperationMethod fetchRunningCanMessage = new OperationMethod("查询运行CAN报文", "fetchRunningCanMessage");
    public static final OperationMethod startDbcReceiver = new OperationMethod("获取实时CAN DBC报文", "startDbcReceiver");
    public static final OperationMethod startFrameReceiver = new OperationMethod("获取实时CAN帧报文", "startFrameReceiver");
    public static final OperationMethod stopFrameReceiver = new OperationMethod("获取实时CAN报文(Frame)关闭", "stopFrameReceiver");
    public static final OperationMethod stopDbcReceiver = new OperationMethod("获取实时CAN报文(DBC)关闭", "stopDbcReceiver");
    public static final OperationMethod closeOpenButNotConfigDevice = new OperationMethod("关闭未配置设备", "closeOpenButNotConfigDevice");
    public static final OperationMethod startRealTimeData = new OperationMethod("开启实时记录日志", "startRealTimeData");
    public static final OperationMethod stopRealTimeData = new OperationMethod("关闭实时记录日志", "stopRealTimeData");
    public static final OperationMethod startCaptureFrameCanLog = new OperationMethod("开始实时抓取canLog(Frame)", "startCaptureFrameCanLog");
    public static final OperationMethod stopCaptureFrameCanLog = new OperationMethod("停止实时抓取canLog(Frame)", "stopCaptureFrameCanLog");
    public static final OperationMethod startCaptureDbcCanLog = new OperationMethod("开始实时抓取canLog(DBC)", "startCaptureDbcCanLog");
    public static final OperationMethod stopCaptureDbcCanLog = new OperationMethod("停止实时抓取canLog(DBC)", "stopCaptureDbcCanLog");
    public static final OperationMethod saveLog = new OperationMethod("保存日志", "saveLog");

    public static final OperationMethod getDeviceOperationParameterByServer = new OperationMethod("获取CAN配置参数", "getDeviceOperationParameterByServer");
    public static final OperationMethod getTsChannelCountByDeviceName = new OperationMethod("通过设备名称获取同星通道数", "getTsChannelCountByDeviceName");

    public static final OperationMethod openLinChannel = new OperationMethod("打开LIN通道", "openLinChannel");
    public static final OperationMethod closeLinChannel = new OperationMethod("关闭LIN通道", "closeLinChannel");
    public static final OperationMethod sendLinMessage = new OperationMethod("发送LIN报文", "sendLinMessage");
    public static final OperationMethod stopLinMessage = new OperationMethod("停止LIN报文", "stopLinMessage");

    public static final OperationMethod startTsMaster = new OperationMethod("启动TsMaster", "startTsMaster");
    public static final OperationMethod stopTsMaster = new OperationMethod("停止TsMaster", "stopTsMaster");
    public static final OperationMethod sendFlexrayMessage = new OperationMethod("发送flexray报文", "sendFlexrayMessage");
    public static final OperationMethod fetchFlexrayMessage = new OperationMethod("查询运行flexray报文", "fetchRunningFlexrayMessage");
    public static final OperationMethod readFlexrayDataByIdCycle = new OperationMethod("通过ID检查接收flexRay报文", "readFlexrayDataByIdCycle");
    public static final OperationMethod stopFlexrayMessage = new OperationMethod("停止flexray报文", "stopFlexrayMessage");
    public static final OperationMethod stopAllFlexrayMessage = new OperationMethod("停止所有flexray报文", "stopAllFlexrayMessage");
    public static final OperationMethod getMaxChannelCount = new OperationMethod("获取设备最大通道数", "getMaxChannelCount");
    public static final OperationMethod fetchCanLogPath = new OperationMethod("获取CANoe Log文件路径", "fetchCanLogPath");

    // 配置
    public static final OperationMethod loadConfig = new OperationMethod("导入配置", "loadConfig");
    public static final OperationMethod updateConfig = new OperationMethod("更新配置", "updateConfig");

    public static final OperationMethod loadConfigByKey = new OperationMethod("导入配置", "loadConfigByKey");
    public static final OperationMethod updateConfigByKey = new OperationMethod("更新配置", "updateConfigByKey");

    // 报点
    public static final OperationMethod setUserCoordinate = new OperationMethod("设置机械臂用户坐标系", "setUserCoordinate");
    public static final OperationMethod touchAndCheckTouchPoint = new OperationMethod("机械臂点击并校验报点", "touchAndCheckTouchPoint");
    public static final OperationMethod swipeAndCheckTouchPoint = new OperationMethod("机械臂滑动并校验报点", "swipeAndCheckTouchPoint");
    public static final OperationMethod randomAndCheckTouchPoint = new OperationMethod("机械臂随机点击并校验报点", "randomAndCheckTouchPoint");
    public static final OperationMethod arcAndCheckTouchPoint = new OperationMethod("机械臂画圆弧并校验报点", "arcAndCheckTouchPoint");
    public static final OperationMethod touchAndCheckDisplayChange = new OperationMethod("机械臂点击自动判断", "touchAndCheckDisplayChange");
    public static final OperationMethod parseTouchPoint = new OperationMethod("解析报点", "parseTouchPoint");
    public static final OperationMethod monitorTouchPoint = new OperationMethod("监控报点", "monitorTouchPoint");

    // 机械臂
    public static final OperationMethod moveJog = new OperationMethod("关节点动", "moveJog");
    public static final OperationMethod stopMoveJog = new OperationMethod("停止关节点动", "stopMoveJog");
    public static final OperationMethod visionTouch = new OperationMethod("视觉引导点击", "visionTouch");
    public static final OperationMethod setVisionGuideCalibrationData = new OperationMethod("设置视觉引导校准数据", "setVisionGuideCalibrationData");
    public static final OperationMethod setVisionGuideZ = new OperationMethod("设置视觉平面", "setVisionGuideZ");
    public static final OperationMethod fetchVisionGuideConfig = new OperationMethod("获取视觉引导校准数据", "fetchVisionGuideConfig");
    public static final OperationMethod autoScreenCalibration = new OperationMethod("自动屏幕校准", "autoScreenCalibration");
    public static final OperationMethod touch = new OperationMethod("机械臂点击", "touch");
    public static final OperationMethod returnHome = new OperationMethod("机械臂回零", "returnHome");
    public static final OperationMethod enableRobot = new OperationMethod("机械臂启用使能", "enableRobot");
    public static final OperationMethod disableRobot = new OperationMethod("机械臂禁用使能", "disableRobot");
    public static final OperationMethod robotSwipe = new OperationMethod("机械臂滑动", "swipe");
    public static final OperationMethod swipeByName = new OperationMethod("机械臂滑动", "swipeByName");
    public static final OperationMethod robotMove = new OperationMethod("机械臂移动", "move");
    public static final OperationMethod moveByName = new OperationMethod("机械臂移动", "moveByName");
    public static final OperationMethod randomTouchByName = new OperationMethod("机械臂随机点击", "randomTouchByName");
    public static final OperationMethod centerTouchByName = new OperationMethod("机械臂随机点击", "centerTouchByName");
    public static final OperationMethod moveJointExt = new OperationMethod("机械臂滑轨移动(毫米)", "moveJointExt");
    public static final OperationMethod clearError = new OperationMethod("清除机械臂错误", "clearError");
    public static final OperationMethod searchError = new OperationMethod("查询机械臂错误与解决方法", "searchError");
    public static final OperationMethod setSpeedFactor = new OperationMethod("设置机械臂全局速度比例", "setSpeedFactor");
    public static final OperationMethod setPayLoad = new OperationMethod("设置机械臂负载参数", "setPayLoad");
    public static final OperationMethod longTouch = new OperationMethod("机械臂长按", "longTouch");
    public static final OperationMethod quickSwipe = new OperationMethod("快速滑动", "quickSwipe");
    public static final OperationMethod pressTouch = new OperationMethod("当前坐标持续按下", "pressTouch");
    public static final OperationMethod releaseTouch = new OperationMethod("当前坐标抬起", "releaseTouch");
    public static final OperationMethod fetchFeedbackData = new OperationMethod("获取位姿", "fetchFeedbackData");
    public static final OperationMethod enablePoseMonitor = new OperationMethod("启用位姿监控", "enablePoseMonitor");
    public static final OperationMethod resumePoseMonitor = new OperationMethod("恢复位姿监控", "resumePoseMonitor");
    public static final OperationMethod pausePoseMonitor = new OperationMethod("暂停位姿监控", "pausePoseMonitor");
    public static final OperationMethod fetchPose = new OperationMethod("获取笛卡尔坐标", "fetchPose");
    public static final OperationMethod moveLine = new OperationMethod("机械臂直线运动", "moveLine");
    public static final OperationMethod moveJoint = new OperationMethod("机械臂关节运动", "moveJoint");
    public static final OperationMethod arc = new OperationMethod("机械臂圆弧运动", "arc");
    public static final OperationMethod circle = new OperationMethod("机械臂画圆", "circle");
    public static final OperationMethod air = new OperationMethod("机械臂气压", "air");


    // Android
    public static final OperationMethod click = new OperationMethod("点击坐标", "click");
    public static final OperationMethod randomClick = new OperationMethod("范围内随机点击", "randomClick");
    public static final OperationMethod swipe = new OperationMethod("滑动坐标", "swipe");
    public static final OperationMethod randomSwipe = new OperationMethod("范围内随机滑动", "randomSwipe");
    public static final OperationMethod multiFingerSwipe = new OperationMethod("多手指滑动坐标", "multiFingerSwipe");
    public static final OperationMethod screenshot = new OperationMethod("截图", "screenshot");
    public static final OperationMethod videoStream = new OperationMethod("实时投屏", "videoStream");
    public static final OperationMethod pointCheckStop = new OperationMethod("报点监控结束", "pointCheckStop");
    public static final OperationMethod pointCheckStart = new OperationMethod("报点监控开始", "pointCheckStart");
    public static final OperationMethod executeADBCommand = new OperationMethod("执行ADB命令", "executeADBCommand");
    public static final OperationMethod adbMustOccur = new OperationMethod("ADB必须出现", "adbMustExist");
    public static final OperationMethod adbForbidOccur = new OperationMethod("ADB禁止出现", "adbForbidExist");

    // 电源
    public static final OperationMethod outputOn = new OperationMethod("输出打开", "outputOn");
    public static final OperationMethod outputOff = new OperationMethod("输出关闭", "outputOff");
    public static final OperationMethod setVoltage = new OperationMethod("设置电压(V)", "setVoltage");
    public static final OperationMethod setVoltageWithUnit = new OperationMethod("设置电压(V)", "setVoltageWithUnit");
    public static final OperationMethod setStepVoltage = new OperationMethod("设置步进电压(V)", "setStepVoltage");

    public static final OperationMethod setCurrent = new OperationMethod("设置电流(A)", "setCurrent");

    public static final OperationMethod fetchOutput = new OperationMethod("获取输出状态", "fetchOutput");
    public static final OperationMethod fetchVoltage = new OperationMethod("获取电压(V)", "fetchVoltage");
    public static final OperationMethod fetchCurrent = new OperationMethod("获取电流(A)", "fetchCurrent");
    public static final OperationMethod monitorVoltage = new OperationMethod("监控电压(V)", "monitorVoltage");
    public static final OperationMethod monitorCurrent = new OperationMethod("监控电流(A)", "monitorCurrent");
    public static final OperationMethod staticCurrentAcquire = new OperationMethod("静态电流采集(秒)", "staticCurrentAcquire");

    public static final OperationMethod searchMinimumCriticalVoltage = new OperationMethod("自动搜索最小临界电压", "searchMinimumCriticalVoltage");
    public static final OperationMethod loadCustomizePulse = new OperationMethod("加载自定义脉冲", "loadCustomizePulse");
    public static final OperationMethod loadWavePulse = new OperationMethod("加载脉冲", "loadWavePulse");
    public static final OperationMethod stopWavePulse = new OperationMethod("停止脉冲", "stopWavePulse");
    public static final OperationMethod writeCustomizeWavePulse = new OperationMethod("执行自定义脉冲", "writeCustomizeWavePulse");
    public static final OperationMethod writeStartWavePulse = new OperationMethod("执行启动脉冲", "writeStartWavePulse");
    public static final OperationMethod writeTimeLevelResetPulse = new OperationMethod("执行时间级复位脉冲", "writeTimeLevelResetPulse");
    public static final OperationMethod writeVoltageLevelResetPulse = new OperationMethod("执行电压级复位脉冲", "writeVoltageLevelResetPulse");
    public static final OperationMethod writeArbitraryVoltageResetPulse = new OperationMethod("执行任意电压级复位脉冲", "writeArbitraryVoltageResetPulse");
    public static final OperationMethod writePowerTemporarilyInterruptPulse = new OperationMethod("执行电源短暂中断脉冲", "writePowerTemporarilyInterruptPulse");
    //    public static final OperationMethod compareVoltage = new OperationMethod("判断电压(V)", "compareVoltage");
    //    public static final OperationMethod compareCurrent = new OperationMethod("判断电流(A)", "compareCurrent");

    public static final OperationMethod sendToDevice = new OperationMethod("发送", "send");
    public static final OperationMethod sendAndMatch = new OperationMethod("发送并判断返回值", "sendAndMatch");
    public static final OperationMethod sendAndMatchMultiple = new OperationMethod("发送并判断其一返回值", "sendAndMatchMultiple");
    public static final OperationMethod sendAndJudge = new OperationMethod("发送并判断其返回值大小", "sendAndJudge");
    public static final OperationMethod sendAndWatch = new OperationMethod("发送并检测变化", "sendAndWatch");
    public static final OperationMethod packageSend = new OperationMethod("批量发送", "batchSend");
    public static final OperationMethod setI2CParameter = new OperationMethod("设置IIC参数", "setI2CParameter");
    public static final OperationMethod setI2CIO = new OperationMethod("IO控制", "setI2CIO");
    public static final OperationMethod pwmOutputBegin = new OperationMethod("PWM输出开始", "pwmOutputBegin");
    public static final OperationMethod pwmOutputStop = new OperationMethod("PWM输出停止", "pwmOutputStop");
    public static final OperationMethod adcGather = new OperationMethod("ADC采集", "adcGather");
    public static final OperationMethod buildTcpServer = new OperationMethod("创建TCP服务器", "buildTcpServer");
    public static final OperationMethod stopTcpServer = new OperationMethod("关闭TCP服务器", "stopTcpServer");
    public static final OperationMethod tcpServerSend = new OperationMethod("发送", "tcpServerSend");
    public static final OperationMethod tcpMustExistMonitorEnd = new OperationMethod("必须出现|结束监控", "tcpMustExistMonitorEnd");
    public static final OperationMethod tcpMustExistMonitorStart = new OperationMethod("必须出现|开始监控", "tcpMustExistMonitorStart", tcpMustExistMonitorEnd);
    public static final OperationMethod tcpForbidExistMonitorEnd = new OperationMethod("禁止出现|结束监控", "tcpForbidExistMonitorEnd");
    public static final OperationMethod tcpForbidExistMonitorStart = new OperationMethod("禁止出现|开始监控", "tcpForbidExistMonitorStart", tcpForbidExistMonitorEnd);
    public static final OperationMethod waitFilter = new OperationMethod("等待LOG出现", "waitFilter");

    //couple groups
    public static final OperationMethod endVoltageTriggerFunction = new OperationMethod(
            "电压触发(V)|结束监控",
            "endVoltageTriggerFunction",
            OperationMethodType.CUSTOMIZE_FUNCTION_END.getValue());
    public static final OperationMethod beginVoltageTriggerFunction = new OperationMethod(
            "电压触发(V)|开始监控",
            "beginVoltageTriggerFunction",
            endVoltageTriggerFunction,
            OperationMethodType.CUSTOMIZE_FUNCTION_BEGIN.getValue());

    public static final OperationMethod kikusuiTriggerAction = new OperationMethod("菊水电源触发检测动作", "kikusuiTriggerAction");

    public static final OperationMethod judgeText = new OperationMethod("判断返回值", "judgeText");
    public static final OperationMethod mustExistMonitorEnd = new OperationMethod("必须出现|结束监控", "mustExistMonitorEnd");
    public static final OperationMethod mustExistMonitorStart = new OperationMethod("必须出现|开始监控", "mustExistMonitorStart", mustExistMonitorEnd);
    public static final OperationMethod forbidExistMonitorEnd = new OperationMethod("禁止出现|结束监控", "forbidExistMonitorEnd");
    public static final OperationMethod forbidExistMonitorStart = new OperationMethod("禁止出现|开始监控", "forbidExistMonitorStart", forbidExistMonitorEnd);

    public static final OperationMethod fetchResistanceBoardCard = new OperationMethod("读电阻板卡", "fetchResistanceBoardCard");
    public static final OperationMethod fetchRelayBoardCard = new OperationMethod("读继电器板卡", "fetchRelayBoardCard");
    public static final OperationMethod fetchVoltageBoardCard = new OperationMethod("读模拟量采集板卡", "fetchVoltageBoardCard");
    public static final OperationMethod fetchPWMOutputBoardCard = new OperationMethod("读PWM控制板卡", "fetchPWMOutputBoardCard");
    public static final OperationMethod fetchPWMInputBoardCard = new OperationMethod("读PWM控制板卡", "fetchPWMInputBoardCard");
    public static final OperationMethod fetchTriStateOutputBoardCard = new OperationMethod("读三态输出板卡", "fetchTriStateOutputBoardCard");
    public static final OperationMethod writeResistanceBoardCard = new OperationMethod("设置电阻板卡", "writeResistanceBoardCard");
    public static final OperationMethod writeResistanceInitData = new OperationMethod("初始化电阻值", "writeResistanceInitData");
    public static final OperationMethod writeRelayBoardCard = new OperationMethod("设置继电器板卡", "writeRelaySwitchBoardCard");
    public static final OperationMethod writeTriStateOutputBoardCard = new OperationMethod("设置三态输出板卡", "writeTriStateOutputBoardCard");
    public static final OperationMethod writePWMOutputBoardCard = new OperationMethod("设置PWM输出板卡", "writePWMOutputBoardCard");
    public static final OperationMethod setVoltAcquisitionBoardCard = new OperationMethod("设置电平采集板卡初始化", "setVoltAcquisitionBoardCardInit");

    // 点击器
    public static final OperationMethod clickOneChannelStart = new OperationMethod("点击", "clickOneChannelStart");
    public static final OperationMethod channelDown = new OperationMethod("点击器按下", "channelDown");
    public static final OperationMethod channelUp = new OperationMethod("点击器抬起", "channelUp");
    public static final OperationMethod autoClickCheckPoint = new OperationMethod("点击器点击并检测报点", "autoClickCheckPoint");
    public static final OperationMethod alarmReset = new OperationMethod("报警复位", "alarmReset");
    public static final OperationMethod forward = new OperationMethod("向前一步", "forward");
    public static final OperationMethod backward = new OperationMethod("后退一步", "backward");
    public static final OperationMethod getLocation = new OperationMethod("获取位置", "getLocation");

    // USB切换器
    public static final OperationMethod plugIn = new OperationMethod("USB插入", "plugIn");
    public static final OperationMethod pullOut = new OperationMethod("USB拔出", "pullOut");

    // 继电器
    public static final OperationMethod switchRelay = new OperationMethod("继电器通断", "switchRelay");
    public static final OperationMethod setRelayTime = new OperationMethod("设置继电器延时", "setRelayTime");

    // 电阻
    public static final OperationMethod sendResistanceValue = new OperationMethod("电阻板卡发送阻值", "sendResistanceValue");
    public static final OperationMethod logicStartCollection = new OperationMethod("逻辑分析仪开始采集", "logicStartCollection");
    public static final OperationMethod logicStopCollection = new OperationMethod("逻辑分析仪停止采集并保存数据", "logicStopCollection");
    public static final OperationMethod resistanceSet = new OperationMethod("设置电阻", "resistanceSet");
    public static final OperationMethod resistanceSetStep = new OperationMethod("设置步进电阻", "resistanceSetStep");
    public static final OperationMethod resistanceGet = new OperationMethod("查询电阻", "resistanceGet");
    public static final OperationMethod resistanceSetIP = new OperationMethod("设置电阻仪IP", "resistanceSetIP");

    // 相机
    public static final OperationMethod cameraScreenShoot = new OperationMethod("相机截屏", "cameraScreenShoot");
    public static final OperationMethod startRecording = new OperationMethod("是否录制", "startRecording");
    public static final OperationMethod startBacktrackVideo = new OperationMethod("开始捕获", "startBacktrackVideo");
    public static final OperationMethod stopBacktrackVideo = new OperationMethod("停止捕获", "stopBacktrackVideo");

    public static final OperationMethod videoCaptureScreenShoot = new OperationMethod("视频采集截屏", "videoCaptureScreenShoot");
    public static final OperationMethod startScheduledVideoCaptureScreenShoot = new OperationMethod("视频采集开始", "startScheduledVideoCaptureScreenShoot");
    public static final OperationMethod resumeScheduledVideoCaptureScreenShoot = new OperationMethod("视频采集恢复", "resumeScheduledVideoCaptureScreenShoot");
    public static final OperationMethod pauseScheduledVideoCaptureScreenShoot = new OperationMethod("视频采集暂停", "pauseScheduledVideoCaptureScreenShoot");

    // 声卡
    public static final OperationMethod monitorVolume = new OperationMethod("监控音量", "monitorVolume");
    public static final OperationMethod speak = new OperationMethod("播放音频", "speak");
    public static final OperationMethod beginSoundMonitor = new OperationMethod("开始声音监控", "beginSoundMonitor");
    public static final OperationMethod endSoundMonitor = new OperationMethod("结束声音监控", "endSoundMonitor", beginSoundMonitor);
    public static final OperationMethod soundCapture = new OperationMethod("声音实时采集", "soundCapture");
    public static final OperationMethod calibrateReferenceValue = new OperationMethod("校准参考值", "calibrateReferenceValue");
    public static final OperationMethod setReferenceValue = new OperationMethod("设置参考电压", "setReferenceValue");

    public static final OperationMethod startStream = new OperationMethod("获取视频流", "startStream");
    public static final OperationMethod stopStream = new OperationMethod("停止视频流", "stopStream");

    public static final OperationMethod signalStepDialog = new OperationMethod("信号配置步进", "signalStepDialog");
}
