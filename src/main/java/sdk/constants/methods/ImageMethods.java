package sdk.constants.methods;

import sdk.base.operation.OperationMethod;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-18 11:48
 * @description :
 * @modified By :
 * @since : 2022-4-18
 */
public class ImageMethods {
    //点击
    private static final String CLICK = "click";
    private static final String DOUBLE_CLICK = "doubleClick";
    private static final String LONG_CLICK = "longClick";

    //输入
    private static final String INPUT_TEXT = "inputText";

    //滑动
    private static final String SLIDE_UP = "slideUp";
    private static final String SLIDE_DOWN = "slideDown";
    private static final String SLIDE_LEFT = "slideLeft";
    private static final String SLIDE_RIGHT = "slideRight";

    //断言
    private static final String ASSERT = "assert";
    private static final String ASSERT_IF = "assertIf";
    private static final String ASSERT_LOOP = "assertLoop";

    public static final OperationMethod robotTouchImageCalibration = new OperationMethod("机械臂点击检测区域", "robotTouchImageCalibration");
    //    public static final OperationMethod smartCalibration = new OperationMethod("图像校准机械臂", "smartCalibration");
    public static final OperationMethod mustAppearMethod = new OperationMethod("必须出现该图片", "mustAppear");
    public static final OperationMethod mustDisappearMethod = new OperationMethod("不允许出现该图片", "mustDisappear");
    public static final OperationMethod waitAppearMethod = new OperationMethod("等待该图片出现", "waitAppear");
    public static final OperationMethod waitDisappearMethod = new OperationMethod("等待该图片消失", "waitDisappear");
    public static final OperationMethod waitClickMethod = new OperationMethod("等待图片并点击", "waitClick");
    public static final OperationMethod visionGuideClickMethod = new OperationMethod("视觉点击", "visionGuideClick");
    public static final OperationMethod coordinatesClickMethod = new OperationMethod("通过坐标点击", "coordinatesClick");
    public static final OperationMethod randomClickMethod = new OperationMethod("范围内随机点击", "randomClick");
    public static final OperationMethod coordinatesSwipeMethod = new OperationMethod("通过坐标滑动", "coordinatesSwipe");
    public static final OperationMethod randomSwipeMethod = new OperationMethod("范围内随机滑动", "randomSwipe");
    public static final OperationMethod multiFingerSwipeMethod = new OperationMethod("范围内多指滑动", "multiFingerSwipe");

    public static final OperationMethod testSimilarity = new OperationMethod("测试相似度", "testSimilarity");
    //    public static final OperationMethod enableBinarization = new OperationMethod("启用二值化图像处理", "enableBinarization");
    public static final OperationMethod saveImage = new OperationMethod("保存图片", "saveImage");
    public static final OperationMethod touchAndCheckDisplayChange = new OperationMethod("机械臂点击自动判断", "touchAndCheckDisplayChange");
    public static final OperationMethod setCameraParameters = new OperationMethod("设置相机参数", "setCameraParameters");
    public static final OperationMethod setExposureAutoMode = new OperationMethod("设置自动曝光模式", "setExposureAutoMode");
    public static final OperationMethod setReverseX = new OperationMethod("设置水平翻转", "setReverseX");
    public static final OperationMethod setReverseY = new OperationMethod("设置垂直翻转", "setReverseY");
    public static final OperationMethod startGrabbing = new OperationMethod("开始采集", "startGrabbing");
    public static final OperationMethod stopGrabbing = new OperationMethod("停止采集", "stopGrabbing");
    public static final OperationMethod setCameraCalibration = new OperationMethod("设置相机标定", "setCameraCalibration");
    public static final OperationMethod getCameraParameters = new OperationMethod("获取相机参数", "getCameraParameters");
    public static final OperationMethod setCameraFrameRate = new OperationMethod("设置相机帧率", "setCameraFrameRate");
}
