package excelcase.tree;

import javax.swing.*;
import javax.swing.tree.DefaultTreeModel;
import java.io.Serializable;

public abstract class DefaultTree<T extends Serializable> extends JTree {

    public DefaultTree() {

    }

    public void initTree(DefaultTreeModel model) {
        setModel(model);
        addMouseListener(new CheckBoxTreeNodeSelectionListener());
        setCellRenderer(new CheckBoxTreeCellRenderer());
    }

}
