package excelcase.config.json;

import com.alibaba.fastjson2.JSON;
import common.utils.FileUtils;
import excelcase.config.CaseHeaderJsonFileConfig;
import excelcase.tree.ExcelTableConfigTree;
import excelcase.tree.CheckBoxTreeNode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;


/**
 * <AUTHOR> lhy
 * @date : Created in 2023/3/28 10:06
 * @description : Excel Case json配置管理器
 * @modified By :
 * @since : 2023/3/28
 **/
@Slf4j
public class CaseConfigJsonManager {
    //case.json配置文件
    @Getter
    private static final File caseConfigFile = CaseHeaderJsonFileConfig.getInstance().getCaseHeaderConfigFile();

    public static void syncExcelCaseConfigFile() {
        String excelCaseConfigStr = JSON.toJSONString(CaseConfigJson.getInstance());
        writeCaseConfigJsonFile(excelCaseConfigStr);
    }


    //读取和解析CaseConfig.json
    public static CaseConfigJson readExcelCaseConfigFile(File file) {
        try {
            log.info("读取ExcelCase配置文件:{}", file.getAbsolutePath());
            String readStringFromFile = FileUtils.readStringFromFile(file);
            return JSON.parseObject(readStringFromFile, CaseConfigJson.class);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static CaseContent getCaseHeaderBySheetName(String sheetName) {
        CaseContent caseContent = null;
        List<CaseContent> caseContentList = CaseConfigJson.getInstance().getExcelCaseContentList();
        if (CollectionUtils.isNotEmpty(caseContentList)) {
            for (CaseContent exCaseContent : caseContentList) {
                if (sheetName.equals(exCaseContent.getSheetName())) {
                    caseContent = exCaseContent;
                    break;
                }
            }
        }

        return caseContent;
    }


    //写入到caseConfig.json
    public static void writeCaseConfigJsonFile(String caseConfigJsonString) {
        try {
            FileUtils.writeFileFromString(caseConfigFile, caseConfigJsonString, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    //表头设置后，根据树节点的选中状态，组装list
    public static List<CaseHeaderContent> getCaseHeaderConfigByTreeNodes(JTable table, ExcelTableConfigTree tree) {
        List<CaseHeaderContent> list = new ArrayList<>();
        CheckBoxTreeNode rootNode = (CheckBoxTreeNode) tree.getModel().getRoot();
        CaseHeaderContent rootHeaderContent = new CaseHeaderContent();
        rootHeaderContent.setColumnIndex(0);
        rootHeaderContent.setColumnName(rootNode.getUserObject().toString());
        rootHeaderContent.setVisible(rootNode.isSelected());
        list.add(rootHeaderContent);
        Enumeration<DefaultMutableTreeNode> en = rootNode.preorderEnumeration();
        int i = 1; //去掉留着id，uuid，tableName的ColumnId,ExcelCase的columnId从4开始改为1
        while (en.hasMoreElements()) {
            CheckBoxTreeNode node = (CheckBoxTreeNode) en.nextElement();
            if (rootNode != node) {
                CaseHeaderContent childHeaderContent = new CaseHeaderContent();
                childHeaderContent.setColumnName(node.getUserObject().toString());
                childHeaderContent.setColumnIndex(i++);
                childHeaderContent.setModelIndex(table.getColumnModel().getColumn(childHeaderContent.getColumnIndex()).getModelIndex());
                childHeaderContent.setVisible(node.isSelected());
                list.add(childHeaderContent);
            }
        }
        return list;
    }

}
