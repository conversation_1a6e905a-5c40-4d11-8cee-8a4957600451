package excelcase.config.json;

import lombok.Data;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@Data
public class ExcelCaseTemplate {
    private volatile static ExcelCaseTemplate instance;
    private HashMap<String, List<String>> templateColumnMap = new HashMap<>();

    public static ExcelCaseTemplate getInstance() {
        if (instance == null) {
            synchronized (ExcelCaseTemplate.class) {
                if (instance == null) {
                    instance = new ExcelCaseTemplate();
                }
            }
        }
        return instance;
    }

    public void initData() {
        templateColumnMap.put("no", Collections.singletonList("NO\n编号"));
        templateColumnMap.put("testCaseID", Arrays.asList("TC_ID\n案例编号", "Test Case ID"));
        templateColumnMap.put("testKey", Arrays.asList("TestKey\n测试功能点", "Test Key", "Focusing", "Test Key\n功能点"));
        templateColumnMap.put("initialCondition", Arrays.asList("Initial_Condition\n初始条件", "Pre-condition", "Pre-condition\n前提条件", "前置条件"));
        templateColumnMap.put("action", Arrays.asList("Action\n动作", "Action", "Action\n操作步骤", "操作步骤"));
        templateColumnMap.put("expectedResult", Arrays.asList("Expected_Result\n预期结果", "Expected Result", "ExpectedResult", "Expected Result\n期望结果", "预期结果"));
        templateColumnMap.put("initTestSequences", PRE_CONDITION_SEQUENCE_NAME_LIST);
        templateColumnMap.put("actionTestSequences", ACTION_SEQUENCE_NAME_LIST);
        templateColumnMap.put("expectedTestSequences", EXPECTED_SEQUENCE_NAME_LIST);
        templateColumnMap.put("choose", Arrays.asList("Executed_Select_Y/N\n是否执行案例", "Choose", "Selected"));
        templateColumnMap.put("testResult", Arrays.asList("Test_Result\n测试结果", "Test  Result", "Test Result"));
        templateColumnMap.put("actualResult", Arrays.asList("Actual_Result\n实际结果", "Actual Result", "Actual  Result"));
        templateColumnMap.put("tester", Collections.singletonList("Tester\n测试人员"));
        templateColumnMap.put("testTime", Arrays.asList("TestTime\n测试时间", "Test Date"));
        templateColumnMap.put("remark", Arrays.asList("Remark\n备注", "Remark"));
        templateColumnMap.put("testScenario", Arrays.asList("Test Scenario"));
        templateColumnMap.put("testLog", Arrays.asList("Test Log", "TestLog"));
    }

    /**
     * 检查传入的列表是否同时包含三种序列类型（初始条件序列、操作步骤序列和预期结果序列）中的各一个元素
     *
     * @param columnNames 待检查的列名列表
     * @return 如果列表同时包含三种序列类型中的各一个元素返回true，否则返回false
     */
    public static boolean containsAllRequiredSequenceTypes(List<String> columnNames) {
        if (columnNames == null || columnNames.isEmpty()) {
            return false;
        }

        boolean hasPreCondition = false;
        boolean hasAction = false;
        boolean hasExpected = false;

        for (String column : columnNames) {
            if (!hasPreCondition) {
                for (String preName : PRE_CONDITION_SEQUENCE_NAME_LIST) {
                    if (preName.equals(column)) {
                        hasPreCondition = true;
                        break;
                    }
                }
            }

            if (!hasAction) {
                for (String actionName : ACTION_SEQUENCE_NAME_LIST) {
                    if (actionName.equals(column)) {
                        hasAction = true;
                        break;
                    }
                }
            }

            if (!hasExpected) {
                for (String expectedName : EXPECTED_SEQUENCE_NAME_LIST) {
                    if (expectedName.equals(column)) {
                        hasExpected = true;
                        break;
                    }
                }
            }

            // 如果三种类型都找到了，可以提前返回
            if (hasPreCondition && hasAction && hasExpected) {
                return true;
            }
        }

        return hasPreCondition && hasAction && hasExpected;
    }

    public static class OfficialSequenceHeader {
        public final static String CONDITION = "Initial_TestSequences";
        public static final String ACTION = "Action_TestSequences";
        public static final String EXPECT = "Expected_TestSequences";

        public static String getHeader() {
            return String.format("%s、%s、%s", CONDITION, ACTION, EXPECT);
        }
    }

    private final static List<String> PRE_CONDITION_SEQUENCE_NAME_LIST = Arrays.asList("InitTestSequences\n初始条件序列", OfficialSequenceHeader.CONDITION, "Init Test Sequences\n测试序列前置条件", "Init Test Sequences", "测试序列前置条件");
    private final static List<String> ACTION_SEQUENCE_NAME_LIST = Arrays.asList("ActionTestSequences\n操作步骤序列", OfficialSequenceHeader.ACTION, "Action Test Sequences\n动作测试序列", "Action Test Sequences", "动作测试序列");
    private final static List<String> EXPECTED_SEQUENCE_NAME_LIST = Arrays.asList("ExpectedTestSequences\n预期结果序列", OfficialSequenceHeader.EXPECT, "Expected Test Sequences\n预期测试序列结果", "Expected Test Sequences", "预期测试序列结果");
}
