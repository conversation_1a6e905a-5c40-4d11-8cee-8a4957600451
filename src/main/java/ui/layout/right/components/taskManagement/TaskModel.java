package ui.layout.right.components.taskManagement;

import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class TaskModel implements BaseModel, ModelObservable, TaskEventObserver  {
    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void selectedTask(TaskFile taskFile) {
        for (ModelObserver observer : modelObservers) {
            ((TaskEventObserver)observer).selectedTask(taskFile);
        }
    }

    @Override
    public void unselectedTask(TaskFile taskFile) {
        for (ModelObserver observer : modelObservers) {
            ((TaskEventObserver)observer).unselectedTask(taskFile);
        }
    }

}
