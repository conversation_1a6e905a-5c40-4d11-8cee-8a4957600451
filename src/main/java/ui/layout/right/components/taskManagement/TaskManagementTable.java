package ui.layout.right.components.taskManagement;

import excelcase.config.json.TaskConfig;
import excelcase.config.json.TaskConfigJson;
import lombok.Setter;
import ui.base.BaseView;
import ui.base.table.TableCell;
import ui.base.table.checkbox.CheckboxTable;
import ui.model.MainModel;

import java.util.List;

public class TaskManagementTable extends CheckboxTable<TaskFile> implements BaseView {
    private static final TableCell<String> taskName = new TableCell<>(1, "任务名称");
    private static final TableCell<String> excelTableName = new TableCell<>(2, "关联用例表");
    private final MainModel mainModel;
    @Setter
    public boolean taskEmpty = true;

    public TaskManagementTable(MainModel mainModel) {
        super();
        this.mainModel = mainModel;
        createActions();

    }

    @Override
    public void createView() {

    }


    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    protected Object[] convertData(TaskFile data) {
        return new Object[]{data.isSelected(), data.getTaskName(), data.getExcelTableName()};
    }

    @Override
    protected String[] getColumns() {
        return new String[]{checkBox.getColumnName(), taskName.getColumnName(), excelTableName.getColumnName()};

    }

    @Override
    public void selectAllEvent(boolean isSelectAll) {
        getTableList().forEach(taskFile -> taskFile.setSelected(isSelectAll));
    }

    @Override
    public boolean isAllSelected() {
        return !getTableList().isEmpty() && getTableList().stream().allMatch(TaskFile::isSelected);
    }

    @Override
    public void setRowSelected(int row, boolean selected) {
        setValueAt(selected, row, 0);
        TaskFile taskFile = getRow(row);
        taskFile.setSelected(selected);
        List<TaskFile> taskFileList = TaskConfig.getInstance().getTaskFileList();
        for (TaskFile task : taskFileList) {
            if (task.getTaskName().equals(taskFile.getTaskName())) {
                task.setSelected(selected);
                taskFileList.set(taskFileList.indexOf(task), task);
                break;
            }
        }
        TaskConfig.getInstance().setTaskFileList(taskFileList);
        TaskConfigJson.getInstance().save();
        //存到配置文件中

//        String tableName = taskFile.getExcelTableName();
//        List<Integer> selectedRows = taskFile.getSelectedRows();
//        ClientManager.getTestScriptKit().updateTestScriptFile(testScriptFile);
        //mainModel.getTestScriptEventModel().changeSelectStatus(getCheckedRows());
//        save();
        if (selected) {
            mainModel.getTaskModel().selectedTask(taskFile);
        } else {
            mainModel.getTaskModel().unselectedTask(taskFile);
        }
    }

    public boolean isTaskEmpty() {
        return taskEmpty;
    }
}
