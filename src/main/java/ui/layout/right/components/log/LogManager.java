package ui.layout.right.components.log;

import common.constant.AppConstants;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import sdk.constants.UrlConstants;
import ui.entry.ClientView;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.Document;
import javax.swing.text.SimpleAttributeSet;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayDeque;
import java.util.Date;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/8/12 14:10
 * @description :
 * @modified By :
 * @since : 2023/8/12
 **/
@Slf4j
public class LogManager implements ILogObserver {
    private final SimpleDateFormat dsf = new SimpleDateFormat("yy-MM-dd HH:mm:ss:SSS");
    private final BlockingQueue<LogMessage> blockingQueue = new LinkedBlockingQueue<>();
    private LogMonitorWebSocketClient logMonitorWebSocketClient;
    @Setter
    private JTextPane textPane;
    private final MainModel mainModel;
    private final ClientView clientView;
    private boolean connectLogWebsocketServer = true;
    private final SimpleAttributeSet attrSet;
    private static final int MAX_LOG_COUNT = 1000; // 最大日志条数
    private final Deque<LogMessage> logBuffer = new ArrayDeque<>(MAX_LOG_COUNT);

    // 用于记录日志在Document中的位置
    private int currentDocumentLength = 0;

    public LogManager(MainModel mainModel, ClientView clientView) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        attrSet = new SimpleAttributeSet();
        LogCmd.getInstance().setMainModel(mainModel);
        mainModel.getLogModel().addObserver(this);
        if (connectLogWebsocketServer) {
            startLogMonitor();
        }
        startPrintLogTasks();
    }

    /**
     * 开启打印日志线程
     */
    private void startPrintLogTasks() {
        new SwingWorker<Void, LogMessage>() {
            @Override
            protected Void doInBackground() {
                while (true) {
                    try {
                        LogMessage logMessage = blockingQueue.take();
                        publish(logMessage);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

            @Override
            protected void process(List<LogMessage> chunks) {
                for (LogMessage chunk : chunks) {
                    print(chunk);
                }
            }
        }.execute();
    }

    /**
     * 打印单条日志，优化后避免遍历整个logBuffer
     *
     * @param logMessage 要打印的日志消息
     */
    public void print(LogMessage logMessage) {
        String logEntry = String.format("%s %s  %s%n", dsf.format(new Date()), logMessage.getLevel(), logMessage.getMessage());
        SwingUtilities.invokeLater(() -> {
            Document doc = textPane.getDocument();
            try {
                // 添加新日志到文档末尾
                doc.insertString(doc.getLength(), logEntry, attrSet);
                currentDocumentLength += logEntry.length();

                logBuffer.addLast(logMessage);

                // 如果超过最大日志数，移除最早的一条日志
                if (logBuffer.size() > MAX_LOG_COUNT) {
                    logBuffer.pollFirst();// 移除文档中最早的一条日志
                    String docText = doc.getText(0, doc.getLength());
                    int firstLineEnd = docText.indexOf('\n') + 1;
                    if (firstLineEnd > 0) {
                        doc.remove(0, firstLineEnd);
                        currentDocumentLength -= firstLineEnd;
                    }
                }

                if (isAutoScroll()) {
                    textPane.setCaretPosition(doc.getLength());
                }
            } catch (BadLocationException e) {
                log.error("更新日志失败: ", e);
            }
        });
    }



    private boolean isAutoScroll() {
        return clientView.getTestStepView().getLogOutputPanelView().getAutoScrollCheckBox().isSelected();
    }

    /**
     * 清空日志
     */
    public void clear() {
        SwingUtilities.invokeLater(() -> {
            Document doc = textPane.getDocument();
            try {
                doc.remove(0, doc.getLength());
                logBuffer.clear();
                currentDocumentLength = 0;
                print(LogMessage.info("---------以上日志已清除---------"));
            } catch (BadLocationException e) {
                log.error("清除日志失败: ", e);
            }
        });
    }

    private boolean startLogMonitor() {
        try {
            if (logMonitorWebSocketClient != null) {
                if (logMonitorWebSocketClient.isClosed()) {
                    log.info("重新连接Log Monitor websocket");
                    logMonitorWebSocketClient.reconnectBlocking();
                    log.info("重新连接Log Monitor websocket成功");
                } else {
                    log.info("Log Monitor websocket已连接");
                }
                return true;
            }
            log.info("正在连接Log Monitor websocket...");
            logMonitorWebSocketClient = new LogMonitorWebSocketClient(mainModel, UrlConstants.WebSocketUrls.LOG_MONITOR_URL);

            logMonitorWebSocketClient.setConnectionLostTimeout(AppConstants.WEBSOCKET_TIMEOUT);
            logMonitorWebSocketClient.connectBlocking();
            if (logMonitorWebSocketClient.isOpen()) {
                log.info("连接Log Monitor websocket成功");
                return true;
            } else {
                log.warn("连接Log Monitor websocket失败");
                return false;
            }
        } catch (URISyntaxException | InterruptedException e) {
            log.error("连接Log Monitor websocket失败:", e);
            return false;
        }
    }

    @Override
    public void receiveMessage(LogMessage logMessage) {
        try {
            blockingQueue.put(logMessage);
        } catch (InterruptedException e) {
            log.warn(e.getMessage(), e);
        }
    }

    @Override
    public void connectLogMonitor() {
        startLogMonitor();
    }
    
}


