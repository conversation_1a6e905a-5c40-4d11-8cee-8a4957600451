package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/5/13 16:27
 * @Version: 1.0
 * @Desc : 描述信息
 */

public class AllNetLinChannelOpenDialog extends NetLinOpenDialog{

    private static AllNetLinChannelOpenDialog instance;

    private static Map<Integer, AllNetLinChannelOpenDialog> channelMap = new HashMap<>();

    private AllNetLinChannelOpenDialog(int channel) {
        super(channel);
    }

    public static synchronized AllNetLinChannelOpenDialog getInstance(int channel) {
        instance = channelMap.get(channel);
        if (instance == null) {
            instance = new AllNetLinChannelOpenDialog(channel);
            channelMap.put(channel,instance);
        }
        return instance;
    }


    public static Map<Integer, AllNetLinChannelOpenDialog>  getChannelMap(){
        return channelMap;
    }
}
