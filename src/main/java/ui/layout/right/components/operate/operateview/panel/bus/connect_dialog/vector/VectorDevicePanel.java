package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.vector;

import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base.BaseBusDevicePanel;
import ui.model.MainModel;

public class VectorDevicePanel extends BaseBusDevicePanel {

    public VectorDevicePanel(MainModel mainModel, String deviceType) {
        super(mainModel,
                deviceType,
                deviceType.equals(DeviceType.DEVICE_CAN) ? new String[]{DeviceModel.Bus.VECTOR_CAN} :
                        deviceType.equals(DeviceType.DEVICE_ETHERNET) ? new String[]{DeviceModel.Bus.VECTOR_ETHERNET} : new String[]{DeviceModel.Bus.VECTOR_LIN});
    }

    @Override
    protected void openDevice() {

    }
}
