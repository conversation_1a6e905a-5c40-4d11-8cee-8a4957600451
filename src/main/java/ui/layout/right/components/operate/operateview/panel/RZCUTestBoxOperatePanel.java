package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.TestBoxDevice;
import ui.layout.right.components.operate.operateview.base.BaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 12:17
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
public class RZCUTestBoxOperatePanel extends BaudRateDeviceOperatePanel {

    public RZCUTestBoxOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        TestBoxDevice httpClient = TestBoxDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        createView();
        createActions();
        restoreView();
        setBaudRate(9600);
    }

}
