package ui.layout.right.components.operate.header;

import common.constant.AppConstants;
import lombok.Getter;
import ui.base.BaseView;

import javax.swing.*;
import java.awt.*;

/**
 * 波特率连接面板组件
 */
@Getter
public class BaudRateHeaderHookComponent extends HeaderHookComponent implements BaseView {
    private final JComboBox<Integer> baudRateComboBox = new JComboBox<>();

    public BaudRateHeaderHookComponent() {
        this(false);
    }

    public BaudRateHeaderHookComponent(boolean delayLoad) {
        setLayout(new FlowLayout(FlowLayout.LEFT));
        if (!delayLoad) {
            createView();
            createActions();
        }
    }

    @Override
    public void createView() {
        for (Integer baudRate : AppConstants.baudRates) {
            baudRateComboBox.addItem(baudRate);
        }
        baudRateComboBox.setSelectedItem(AppConstants.defaultBaudRate);
        add(new JLabel("波特率:"));
        add(baudRateComboBox);
    }

    public void setBaudRete(int baudRete) {
        baudRateComboBox.setSelectedItem(baudRete);
    }

    public Integer getBaudRete() {
        return (Integer) baudRateComboBox.getSelectedItem();
    }


}
