package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.RobotOperatePanel;
import ui.model.MainModel;

public class RobotOperateTabWrapper extends DeviceOperateTabWrapper {
    public RobotOperateTabWrapper(MainModel mainModel) {
        super(mainModel);

    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_ROBOT;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("越疆魔术师", new RobotOperatePanel(mainModel, DeviceModel.Robot.DOBOT_MAGICIAN));
        addOperatePanel("越疆MG400", new RobotOperatePanel(mainModel, DeviceModel.Robot.DOBOT_MG400,
                "<html>电脑端网络配置:<br> ipv4地址:************<br>IPV4子网掩码:*************<br>IPV4默认网关:***********</html>"));
    }
}
