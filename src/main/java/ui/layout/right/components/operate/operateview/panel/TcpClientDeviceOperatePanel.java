package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.TcpClientDevice;
import ui.layout.right.components.operate.operateview.base.AddressPortClientDeviceOperatePanel;
import ui.model.MainModel;

public class TcpClientDeviceOperatePanel extends AddressPortClientDeviceOperatePanel {
    private TcpClientDevice httpClient;
    private static final String TCP_DEVICE_IP = "************";
    private static final String TCP_DEVICE_PORT = "13400";

    public TcpClientDeviceOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        httpClient = TcpClientDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        createView();
        createActions();
        ethernetClientHeader.getIpField().setText(TCP_DEVICE_IP);
        ethernetClientHeader.getPortField().setText(TCP_DEVICE_PORT);
    }

}
