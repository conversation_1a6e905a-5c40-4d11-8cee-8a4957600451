package ui.layout.right.components.operate.operateview.wrapper;

import common.constant.DeviceCategoryConstants;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.TcpServerOperatePanel;
import ui.model.MainModel;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/9/3 20:27
 */
public class TcpServerOperateTabWrapper extends DeviceOperateTabWrapper {
    public TcpServerOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_TCP_SERVER;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel(DeviceCategoryConstants.TCP_SERVER.getDeviceOfficialName(), new TcpServerOperatePanel(mainModel));
    }
}
