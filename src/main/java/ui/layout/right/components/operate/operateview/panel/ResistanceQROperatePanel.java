package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.OperationTargetHolder;
import sdk.entity.ResistanceQRDevice;
import ui.layout.right.components.operate.operateview.base.BaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/2/23 15:48
 */
public class ResistanceQROperatePanel extends BaudRateDeviceOperatePanel {
    public ResistanceQROperatePanel(MainModel mainModel) {
        super(mainModel);
        ResistanceQRDevice httpClient = OperationTargetHolder.getResistanceQRDeviceManager();
        setDeviceClient(httpClient);
        createView();
        createActions();
        setBaudRate(115200);
    }
}
