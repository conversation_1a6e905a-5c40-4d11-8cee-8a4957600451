package ui.layout.right.components.operate.operateview.base;

import ui.layout.right.components.operate.header.CommProtocolBaudRateHeaderHookComponent;
import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.model.MainModel;

public class CommProtocolBaudRateDeviceOperatePanel extends BaudRateDeviceOperatePanel {

    public CommProtocolBaudRateDeviceOperatePanel(MainModel mainModel) {
        super(mainModel);
        registerModelObservers();
    }

    @Override
    public HeaderHookComponent newHeaderHookComponent() {
        CommProtocolBaudRateHeaderHookComponent hookComponent = new CommProtocolBaudRateHeaderHookComponent();
        setHookComponent(hookComponent);
        return hookComponent;
    }

    public String getCommProtocol() {
        return ((CommProtocolBaudRateHeaderHookComponent) getHookComponent()).getCommProtocol();
    }

    public void setCommProtocol(String protocol) {
        ((CommProtocolBaudRateHeaderHookComponent) getHookComponent()).setCommProtocol(protocol);
    }

//    @Override
//    public void deviceConnected(Device device, boolean autoOpenChannel) {
//        ((CommProtocolBaudRateHeaderHookComponent) getHookComponent()).getCommProtocolComboBox().setEnabled(false);
//        super.deviceConnected(device, autoOpenChannel);
//    }
//
//    @Override
//    public void deviceDisconnected(Device device) {
//        super.deviceDisconnected(device);
//        ((CommProtocolBaudRateHeaderHookComponent) getHookComponent()).getCommProtocolComboBox().setEnabled(true);
//    }
}
