package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.UdpDeviceOperatePanel;
import ui.model.MainModel;

import static sdk.constants.DeviceModel.UdpDevice.UDP_DEVICE;

public class UdpDeviceOperateTabWrapper extends DeviceOperateTabWrapper {
    public UdpDeviceOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_UDP;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("UDP设备", new UdpDeviceOperatePanel(mainModel, UDP_DEVICE));
    }
}
