package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog;

import ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base.BaseBusDevicePanel;
import ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.zlg.ZlgBusDevicePanel;

import javax.swing.*;
import java.awt.*;

public class Main {

    public static void main(String[] args) {
        final JFrame frame = new JFrame();
        JButton button = new JButton("设备管理");
        button.addActionListener(e -> {
            BaseBusDevicePanel canDevicePanel = new ZlgBusDevicePanel(null, null);
            canDevicePanel.setVisible(true);
        });
        frame.add(button);
        frame.setSize(new Dimension(800, 800));
        frame.setVisible(true);
        frame.setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
        frame.setLocationRelativeTo(null);
    }

}
