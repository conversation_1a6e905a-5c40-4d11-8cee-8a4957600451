package ui.layout.right.components.operate.operateview.panel;

import common.constant.AppConstants;
import sdk.entity.PowerDevice;
import ui.layout.right.components.operate.operateview.base.CommProtocolBaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * 可更改通讯协议的电源面板
 */
public class ComplexPowerOperatePanel extends CommProtocolBaudRateDeviceOperatePanel {

    public ComplexPowerOperatePanel(MainModel mainModel, String deviceModel) {
        this(mainModel, deviceModel, true, null);
    }

    public ComplexPowerOperatePanel(MainModel mainModel, String deviceModel, String displayText) {
        this(mainModel, deviceModel, true, displayText);
    }

    public ComplexPowerOperatePanel(MainModel mainModel, String deviceModel, boolean isEnableBaudRateSetting, String displayText) {
        super(mainModel);
        PowerDevice httpClient = PowerDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        setDisplayText(displayText);
        createView();
        createActions();
        setCommProtocol(AppConstants.rs232Protocol);
        setBaudRate(9600, isEnableBaudRateSetting);
    }
}
