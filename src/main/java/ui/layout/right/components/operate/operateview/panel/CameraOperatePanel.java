package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.CameraDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

/**
 * 相机连接面板
 */
public class CameraOperatePanel extends DeviceOperatePanel  {

    public CameraOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        CameraDevice httpClient = CameraDevice.getDevice(deviceModel);
        setLaterOpen(false);
        setDeviceClient(httpClient);
        createView();
    }

}
