package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.OperationTargetHolder;
import sdk.entity.QnxDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

public class QnxOperatePanel extends DeviceOperatePanel {

    public QnxOperatePanel(MainModel mainModel) {
        super(mainModel);
        QnxDevice httpClient = OperationTargetHolder.getQnxDevice();
        setLaterOpen(true);
        setDeviceClient(httpClient);
        createView();
        createActions();
    }

}
