package ui.layout.right.components.operate.operateview.base;

import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.layout.right.components.operate.header.ParityBaudRateHeaderHookComponent;
import ui.model.MainModel;

public class ParityBaudRateDeviceOperatePanel extends BaudRateDeviceOperatePanel {

    public ParityBaudRateDeviceOperatePanel(MainModel mainModel) {
        super(mainModel);
        registerModelObservers();
    }

    @Override
    public HeaderHookComponent newHeaderHookComponent() {
        ParityBaudRateHeaderHookComponent hookComponent = new ParityBaudRateHeaderHookComponent();
        setHookComponent(hookComponent);
        return hookComponent;
    }

    public String getParity() {
        return ((ParityBaudRateHeaderHookComponent) getHookComponent()).getParity();
    }

    public void setParity(String parity) {
        ((ParityBaudRateHeaderHookComponent) getHookComponent()).setParity(parity);
    }

}
