package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.TestBoxDevice;
import ui.layout.right.components.operate.operateview.base.AddressPortClientDeviceOperatePanel;
import ui.model.MainModel;

public class LightTestBoxOperatePanel extends AddressPortClientDeviceOperatePanel {
    private TestBoxDevice httpClient;
    private static final String LIGHT_TEST_BOX_IP = "***********";
    private static final String LIGHT_TEST_BOX_PORT = "23";

    public LightTestBoxOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        httpClient = TestBoxDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        createView();
        createActions();
        ethernetClientHeader.getIpField().setText(LIGHT_TEST_BOX_IP);
        ethernetClientHeader.getPortField().setText(LIGHT_TEST_BOX_PORT);
    }
}
