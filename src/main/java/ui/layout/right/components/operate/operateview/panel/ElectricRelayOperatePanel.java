package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.ElectricRelayDevice;
import ui.layout.right.components.operate.operateview.base.AddressBaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * 继电器操作面板
 */
public class ElectricRelayOperatePanel extends AddressBaudRateDeviceOperatePanel {

    public ElectricRelayOperatePanel(MainModel mainModel, String deviceModel, int address) {
        super(mainModel);
        ElectricRelayDevice httpClient = ElectricRelayDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        createView();
        createActions();
        restoreView();
        setBaudRate(9600);
        setAddress(address);
    }

}
