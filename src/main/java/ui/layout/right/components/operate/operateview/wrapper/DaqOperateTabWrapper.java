package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.DaqOperatePanel;
import ui.model.MainModel;

public class DaqOperateTabWrapper extends DeviceOperateTabWrapper {
    public DaqOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("Keysight 34461A", new DaqOperatePanel(mainModel, DeviceModel.Daq.KEYSIGHT_34461A));
        addOperatePanel("Ut8806", new DaqOperatePanel(mainModel, DeviceModel.Daq.UT8806));
        addOperatePanel("USB3200N", new DaqOperatePanel(mainModel, DeviceModel.Daq.USB3200N));
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_DAQ;
    }

}
