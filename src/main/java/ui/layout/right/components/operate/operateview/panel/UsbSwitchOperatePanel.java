package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.OperationTargetHolder;
import sdk.entity.UsbSwitchDevice;
import ui.layout.right.components.operate.operateview.base.BaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * USB切换器操作面板
 */
public class UsbSwitchOperatePanel extends BaudRateDeviceOperatePanel {

    public UsbSwitchOperatePanel(MainModel mainModel) {
        super(mainModel);
        UsbSwitchDevice httpClient = OperationTargetHolder.getUsbSwitchDeviceManager();
        setDeviceClient(httpClient);
        createView();
        createActions();
        restoreView();
        setBaudRate(9600);
    }

}
