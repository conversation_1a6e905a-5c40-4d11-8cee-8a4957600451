package ui.layout.right.components.operate.operateview.wrapper;

import common.constant.DeviceCategoryConstants;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.UsbSwitchOperatePanel;
import ui.model.MainModel;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/27 19:34
 * @description :
 * @modified By :
 * @since : 2023/7/27
 **/
public class UsbSwitchOperateTabWrapper extends DeviceOperateTabWrapper {
    public UsbSwitchOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_USB_SWITCH;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel(DeviceCategoryConstants.USB_SWITCH.getDeviceOfficialName(), new UsbSwitchOperatePanel(mainModel));
    }

}
