package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.OperationTargetHolder;
import sdk.entity.RemoteResistanceDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/7/30 10:57
 */
public class RemoteResistanceOperatePanel extends DeviceOperatePanel {
    public RemoteResistanceOperatePanel(MainModel mainModel) {
        super(mainModel);
        RemoteResistanceDevice httpClient = OperationTargetHolder.getRemoteResistanceManager();
        setDeviceClient(httpClient);
        createView();
        createActions();
    }
}
