package ui.layout.right.components.operate.operateview.panel;

import common.constant.AppConstants;
import sdk.entity.PowerDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

/**
 * 菊水电源连接面板
 */
public class KikusuiOperatePanel extends DeviceOperatePanel {

    public KikusuiOperatePanel(MainModel mainModel) {
        super(mainModel);
    }

    public KikusuiOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        PowerDevice httpClient = PowerDevice.getDevice(deviceModel);
        httpClient.setCommProtocol(AppConstants.usbProtocol);
        setDeviceClient(httpClient);
        createView();
        createActions();
    }


}
