package ui.layout.right.components.operate.operateview.panel.bus;

import lombok.Getter;
import lombok.Setter;
import sdk.domain.Device;
import sdk.entity.BusDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

/**
 * CAN操作面板
 */
@Setter
@Getter
public abstract class BusOperatePanel extends DeviceOperatePanel {

    public interface BusOperationHandler {

        void startDevice(BusDevice busDevice);

        void stopDevice(BusDevice busDevice);

        Device getTreeDevice(BusDevice busDevice);
    }

    private BusOperationHandler busOperationHandler;


    public BusOperatePanel(MainModel mainModel) {
        super(mainModel);
    }

}
