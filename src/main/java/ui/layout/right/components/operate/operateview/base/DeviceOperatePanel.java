package ui.layout.right.components.operate.operateview.base;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.Getter;
import lombok.Setter;
import sdk.domain.Device;
import ui.base.BaseView;
import ui.layout.right.components.operate.header.DeviceConfigDialog;
import ui.layout.right.components.operate.header.DeviceOperateHeader;
import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.event.ListDataEvent;
import javax.swing.event.ListDataListener;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备操作模板
 */
public abstract class DeviceOperatePanel extends JPanel implements BaseView {
    @Setter
    private AddressPortClientDeviceOperatePanel.EthernetClientHeader ethernetClientHeader;
    @Setter
    @Getter
    private HeaderHookComponent hookComponent;

    @Setter
    @Getter
    private Device deviceClient;

    @Setter
    private boolean laterOpen = false;

    @Getter
    protected DeviceOperateHeader deviceOperateHeader;

    @Getter
    private final MainModel mainModel;

    @Setter
    private String displayText;

    @Getter
    private DeviceConfigDialog deviceConfigDialog;

    @Getter
    private JPanel centerPanel;

    private final JList<String> historyList = new JList<>();

    private final DeviceConnHistoryManager deviceConnHistoryManager;

    /**
     * 中间显示面板
     *
     * @return 面板
     */
    public JPanel centerPanel() {
//        JPanel panel = new JPanel(new GridLayout(1, 1));   //modify by lhy
        centerPanel = new JPanel(new BorderLayout());
        if (displayText != null && !displayText.isEmpty()) {
            centerPanel.add(new JLabel(displayText), BorderLayout.CENTER);
        }
        return centerPanel;
    }

    public DeviceOperatePanel(MainModel mainModel) {
        this.mainModel = mainModel;
        deviceConnHistoryManager = new DeviceConnHistoryManager();
        deviceConnHistoryManager.init();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        deviceOperateHeader = newDeviceOperateHeader();
        deviceConfigDialog = buildDeviceConfigDialog();
        if (deviceConfigDialog != null) {
            deviceOperateHeader.setDeviceConfigDialog(deviceConfigDialog);
        }
        add(deviceOperateHeader, BorderLayout.NORTH);
        add(centerPanel(), BorderLayout.CENTER);
        if (deviceClient != null) {// 初始化加载设备连接记录（Can、Lin、EtherNet不提供deviceClient的就不会记录设备-端口记录）
            JPanel historyConnectPanel = new JPanel();
            historyConnectPanel.setLayout(new BorderLayout());
            JLabel tipLabel = new JLabel("设备连接历史：");
            DefaultListModel<String> model = new DefaultListModel<>();
            // 添加ListDataListener来监听数据变化
            model.addListDataListener(new ListDataListener() {
                @Override
                public void intervalAdded(ListDataEvent e) {
                    controlDisplay();
                }

                @Override
                public void intervalRemoved(ListDataEvent e) {
                    controlDisplay();
                }

                @Override
                public void contentsChanged(ListDataEvent e) {
                    controlDisplay();
                }

                private void controlDisplay() {
                    if (model.isEmpty()) {
                        historyConnectPanel.remove(tipLabel);
                    } else {
                        historyConnectPanel.add(tipLabel, BorderLayout.WEST);
                    }
                }
            });
            historyList.setModel(model);
            historyConnectPanel.add(historyList, BorderLayout.SOUTH);
            add(historyConnectPanel, BorderLayout.SOUTH);
            loadConnectHistory();
        }
    }

    /**
     * 加载设备连接历史
     */
    public void loadConnectHistory() {
        Map<String, Device> portDeviceMap = deviceConnHistoryManager.getConnectHistoryMap().get(deviceClient.getDeviceModel());
        if (portDeviceMap == null) {
            portDeviceMap = new HashMap<>();
        }
        // index -> device
        DefaultListModel<String> model = (DefaultListModel<String>) historyList.getModel();
        model.clear();
        portDeviceMap.forEach((s, device) -> model.addElement(s + "-->" + device.getDeviceName()));
    }


    public DeviceConfigDialog buildDeviceConfigDialog() {
        return null;
    }

    protected HeaderHookComponent newHeaderHookComponent() {
        return null;
    }

    /**
     * 创建新的DeviceOperateHeader
     *
     * @return DeviceOperateHeader
     */
    public DeviceOperateHeader newDeviceOperateHeader() {
        DeviceOperateHeader newDeviceOperateHeader = new DeviceOperateHeader(
                mainModel,
                "端口:",
                deviceClient,
                newHeaderHookComponent(),
                laterOpen,
                this);
        deviceConfigDialog = buildDeviceConfigDialog();
        if (deviceConfigDialog != null) {
            newDeviceOperateHeader.setDeviceConfigDialog(deviceConfigDialog);
        }
        return newDeviceOperateHeader;
    }

    /**
     * 获取设备实例
     *
     * @return 设备实例
     */
    public Device getDevice() {
        if (deviceOperateHeader.getDeviceOperateHeaderManagerPanel() != null) {
            return deviceOperateHeader.getDeviceOperateHeaderManagerPanel().getConfigDevice();
        } else {
            Device targetDevice = getDeviceClient();
            String name = ethernetClientHeader.getIpField().getText() + ":" + ethernetClientHeader.getPortField().getText();
            targetDevice.setDeviceName(name);
            targetDevice.setAliasName(targetDevice.getDeviceModel() + "#1");
            targetDevice.setDeviceUniqueCode(targetDevice.getDeviceName() + "_" + DigestUtil.md5Hex(targetDevice.getDeviceName()));
            return targetDevice;
        }
    }

    public Device getDevice(int deviceIndex) {
        return getDevice();
    }

    public void addDeviceConnectedHistory(Device device) {
        deviceConnHistoryManager.addDeviceConnectedHistory(device);
        loadConnectHistory();
    }

    public void initPanel() {

    }

}
