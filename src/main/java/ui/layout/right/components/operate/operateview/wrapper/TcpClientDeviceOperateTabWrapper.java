package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.TcpClientDeviceOperatePanel;
import ui.model.MainModel;

import static sdk.constants.DeviceModel.TcpClient.TCP_CLIENT;

public class TcpClientDeviceOperateTabWrapper extends DeviceOperateTabWrapper {
    public TcpClientDeviceOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_TCP_CLIENT;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("TCP客户端设备", new TcpClientDeviceOperatePanel(mainModel, TCP_CLIENT));
    }
}
