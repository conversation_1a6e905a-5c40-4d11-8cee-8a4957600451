package ui.layout.right.components.operate.operateview.base;

import ui.base.BaseView;
import ui.layout.right.components.operate.header.BaudRateHeaderHookComponent;
import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.model.MainModel;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 17:02
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
public class BaudRateDeviceOperatePanel extends DeviceOperatePanel implements BaseView {

    public BaudRateDeviceOperatePanel(MainModel mainModel) {
        super(mainModel);
        registerModelObservers();
    }

    @Override
    protected HeaderHookComponent newHeaderHookComponent() {
        BaudRateHeaderHookComponent baudRateHeaderHookComponent = new BaudRateHeaderHookComponent();
        setHookComponent(baudRateHeaderHookComponent);
        return baudRateHeaderHookComponent;
    }


    public void setBaudRate(int baudRate) {
        setBaudRate(baudRate, true);
    }

    public void setBaudRate(int baudRate, boolean isEnabled) {
        BaudRateHeaderHookComponent baudRateHeaderHookComponent = (BaudRateHeaderHookComponent) getHookComponent();
        baudRateHeaderHookComponent.setBaudRete(baudRate);
        baudRateHeaderHookComponent.getBaudRateComboBox().setEnabled(isEnabled);
    }

}
