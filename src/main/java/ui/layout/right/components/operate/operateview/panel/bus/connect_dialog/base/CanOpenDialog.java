package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base;

import lombok.Getter;
import sdk.domain.bus.CanConfigParameter;
import sdk.domain.bus.LinConfigParameter;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.Objects;

/**
 * 通道启动时，属性设置面板
 */
public class CanOpenDialog extends JDialog implements ActionListener {
    private static final String CONFIRM = "确认";
    private static final String CANCEL = "取消";
    private static final String ARBITRATION_BPS = "仲裁域波特率";
    private static final String CAN_BUS_UTILIZATION_RATE = "上报总线利用率";
    private static final String CAN_FD_ACCELERATOR = "CANFD加速";
    private static final String CAN_PROTOCOL = "CAN协议";
    private final JComboBox<String> protocolComboBox;
    private final JComboBox<String> canFdStandardComboBox;
    private final JComboBox<String> canFdAccelerateComboBox;
    private final JComboBox<String> arbitrationBpsComboBox; //仲裁
    private final JComboBox<String> dataBpsComboBox; //数据
    private final JTextField userDefinedBaudRateTextField;
    private final JComboBox<String> workModelComboBox;
    private final JComboBox<String> terminalResistanceComboBox;
    private final JComboBox<String> canBusUtilizationRateComboBox;
    private final JLabel sendRetryLabel;
    private final JLabel canBusUtilizationRateCycleLabel;
    private final JLabel canBusUtilizationRateLabel;
    private final JLabel terminalResistanceLabel;
    private final JLabel workModelLabel;
    private final JLabel userDefinedBaudrateLabel;
    private final JLabel dataBpsLabel;
    private final JLabel arbitrationBpsLabel;
    private final JLabel canfdAccelerateLabel;
    private final JLabel canfdStandardLabel;
    private final JLabel protocolLabel;
    private final JSpinner canBusUtilizationRateCycleSpinner;
    private final JComboBox<String> sendRetryComboBox;
    private static final int WIDTH = 450;
    private static final int HEIGHT = 500;
    private static final String TITLE = "启动";
    private volatile boolean flag = true;//总线利用率周期“下拉框”，鼠标滑动编辑是否可用标志
    @Getter
    private boolean configConfirmed = true;//对话框“取消”标志
    @Getter
    private CanConfigParameter canConfigParameter;

    public CanOpenDialog(int channel) {
        canConfigParameter = new CanConfigParameter();
        canConfigParameter.setChannel(channel);
        //创建面板与滚动面板
        JPanel panel = new JPanel();
        //确认与取消按钮
        JButton confirmButton = new JButton("确认");
        JButton cancelButton = new JButton("取消");
        confirmButton.setActionCommand("确认");
        confirmButton.addActionListener(this);
        cancelButton.setActionCommand("取消");
        cancelButton.addActionListener(this);
        confirmButton.setFocusPainted(false);
        cancelButton.setFocusPainted(false);
        Box horizontalBox = Box.createHorizontalBox();
        horizontalBox.add(Box.createHorizontalGlue());
        horizontalBox.add(confirmButton);
        horizontalBox.add(Box.createHorizontalGlue());
        horizontalBox.add(cancelButton);
        horizontalBox.add(Box.createHorizontalGlue());
        //协议
        String[] protocol = {"CAN", "CAN FD"};
        protocolLabel = new JLabel("协议");
        protocolComboBox = new JComboBox<>(protocol);
        protocolComboBox.setActionCommand(CAN_PROTOCOL);
        protocolComboBox.addActionListener(this);
        //CANFD标准
        String[] canFdStandard = {"CAN FD ISO", "Non-ISO"};
        canfdStandardLabel = new JLabel("CANFD标准");
        canFdStandardComboBox = new JComboBox<>(canFdStandard);
        //CANFD加速
        String[] canFdAccelerate = {"否", "是"};
        canfdAccelerateLabel = new JLabel(CAN_FD_ACCELERATOR);
        canFdAccelerateComboBox = new JComboBox<>(canFdAccelerate);
        canFdAccelerateComboBox.setActionCommand(CAN_FD_ACCELERATOR);
        canFdAccelerateComboBox.setSelectedIndex(0);
        canFdAccelerateComboBox.addActionListener(this);
        //仲裁域波特率
        String[] arbitrationBps = {"1Mbps 80%", "800kbps 80%", "500kbps 80%", "250kbps 80%",
                "125kbps 80%", "100kbps 80%", "50kbps 80%", "自定义"};
        arbitrationBpsLabel = new JLabel(ARBITRATION_BPS);
        arbitrationBpsComboBox = new JComboBox<>(arbitrationBps);
        arbitrationBpsComboBox.setActionCommand(ARBITRATION_BPS);
        arbitrationBpsComboBox.setSelectedItem("500kbps 80%");
        arbitrationBpsComboBox.addActionListener(this);
        //数据域波特率
        String[] dataBps = {"5Mbps 75%", "4Mbps 80%", "2Mbps 80%", "1Mbps 80%", "800kbps 80%", "500kbps 80%",
                "250kbps 80%", "125kbps 80%", "100kbps 80%"};
        dataBpsLabel = new JLabel("数据域波特率");
        dataBpsComboBox = new JComboBox<>(dataBps);
        dataBpsComboBox.setSelectedItem("2Mbps 80%");
        //自定义波特率
        userDefinedBaudrateLabel = new JLabel("自定义波特率");
        userDefinedBaudRateTextField = new JTextField("1.0Mbps(66%),4.0Mbps(66%),(60,04C00000,01000000)");
        userDefinedBaudRateTextField.setEnabled(false);
        //工作模式
        String[] workMode = {"正常模式", "只听模式"};
        workModelLabel = new JLabel("工作模式");
        workModelComboBox = new JComboBox<>(workMode);
        //终端电阻
        String[] terminalResistance = {"禁能", "使能"};
        terminalResistanceLabel = new JLabel("终端电阻");
        terminalResistanceComboBox = new JComboBox<>(terminalResistance);
        //上报总线利用率
        String[] canBusUtilizationRate = {"禁能", "使能"};
        canBusUtilizationRateLabel = new JLabel(CAN_BUS_UTILIZATION_RATE);
        canBusUtilizationRateComboBox = new JComboBox<>(canBusUtilizationRate);
        canBusUtilizationRateComboBox.setActionCommand(CAN_BUS_UTILIZATION_RATE);
        canBusUtilizationRateComboBox.addActionListener(this);
        //总线利用率周期(ms)
        canBusUtilizationRateCycleLabel = new JLabel("总线利用率周期(ms)");
        UIManager.put("Spinner.editorAlignment", JTextField.LEFT);//JSpinner值放置在左侧
        SpinnerModel spinnerModel = new SpinnerNumberModel(1000, 20, 2000, 1);
        canBusUtilizationRateCycleSpinner = new JSpinner(spinnerModel);
        canBusUtilizationRateCycleSpinner.addMouseWheelListener(e -> {
            if (e.getScrollType() != MouseWheelEvent.WHEEL_UNIT_SCROLL) {
                return;
            }
            if (flag) {
                int value = (int) canBusUtilizationRateCycleSpinner.getValue();
                value -= e.getUnitsToScroll();
                canBusUtilizationRateCycleSpinner.setValue(value);
            }
        });
        //发送重试
        String[] sendRetry = {"单次发送", "发送到总线关闭"};
        sendRetryLabel = new JLabel("发送重试");
        sendRetryComboBox = new JComboBox<>(sendRetry);
        sendRetryComboBox.setSelectedIndex(1);
        dataBpsComboBox.setEnabled(false);
        //布局设置
        setLayout(panel);
        //对话框属性设置
        setTitle(TITLE);
        setLayout(new BorderLayout());
        setSize(new Dimension(WIDTH, HEIGHT));
        setResizable(true);
        setLocationRelativeTo(null);
        add(panel, BorderLayout.CENTER);
        add(horizontalBox, BorderLayout.SOUTH);
        setModal(true);
        protocolComboBox.addItemListener(e -> enableCanFdLayout(isCanFd()));
        enableCanFdLayout(false);
        canBusUtilizationRateCycleSpinner.setEnabled(false);
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                // 在这里实现关闭事件的处理逻辑
                dialogClosed();
            }
        });
        protocolComboBox.setSelectedIndex(1);
        terminalResistanceComboBox.setSelectedIndex(1);

    }


    /**
     * 初始化对话框的组件状态，根据传入的CanConfigParameter
     *
     * @param configParameter 配置参数
     */
    public void initializeWithConfig(CanConfigParameter configParameter) {
        if(configParameter!=null){
            this.canConfigParameter = configParameter;

            // 设置各个组件的值
            protocolComboBox.setSelectedItem(configParameter.getProtocol());
            canFdStandardComboBox.setSelectedItem(configParameter.isCanFdStandard() ? "CAN FD ISO" : "Non-ISO");
            canFdAccelerateComboBox.setSelectedItem(configParameter.isAccelerate() ? "是" : "否");
            arbitrationBpsComboBox.setSelectedItem(configParameter.getArbitrationBps());
            dataBpsComboBox.setSelectedItem(configParameter.getDataBps());
            userDefinedBaudRateTextField.setText(configParameter.getUserDefinedBaudrate());
            workModelComboBox.setSelectedItem(configParameter.isNormalWorkMode() ? "正常模式" : "只听模式");
            terminalResistanceComboBox.setSelectedItem(configParameter.isTerminalResistanceEnabled() ? "使能" : "禁能");
            canBusUtilizationRateComboBox.setSelectedItem(configParameter.isCanBusUtilizationRateEnabled() ? "使能" : "禁能");
            canBusUtilizationRateCycleSpinner.setValue(configParameter.getCanBusUtilizationRateCycle());
            sendRetryComboBox.setSelectedItem(configParameter.getSendRetry());

            // 根据配置启用或禁用相关组件
            enableCanFdLayout(configParameter.getProtocol().equals("CAN FD"));
            dataBpsComboBox.setEnabled(configParameter.isAccelerate() && !"自定义".equals(configParameter.getArbitrationBps()));
            userDefinedBaudRateTextField.setEnabled("自定义".equals(configParameter.getArbitrationBps()));
            canBusUtilizationRateCycleSpinner.setEnabled(configParameter.isCanBusUtilizationRateEnabled());
        }
    }
    private boolean isCanFd() {
        return Objects.requireNonNull(protocolComboBox.getSelectedItem()).equals("CAN FD");
    }

    private void enableCanFdLayout(boolean isEnable) {
        canFdStandardComboBox.setEnabled(isEnable);
        canFdAccelerateComboBox.setEnabled(isEnable);
        dataBpsComboBox.setEnabled(isEnable);
    }

    protected void dialogClosed() {
        configConfirmed = false;
        setVisible(false);
    }

    /**
     * 确认事件，封装通道启动时的属性
     * 取消事件，使SwitchPanel面板中“启动”按钮事件失效
     * 其他下拉框监听，控制自定义、数据域波特率和总线利用率周期的启用与禁用
     *
     * @param e “确认”与"取消"按钮事件
     */
    @Override
    public void actionPerformed(ActionEvent e) {
        //封装设备管理属性
        switch (e.getActionCommand()) {
            case CONFIRM:
                setCanConfigParameter();
                configConfirmed = true;
                setVisible(false);
                break;
            case CANCEL: {
                dialogClosed();
                break;
            }
            case ARBITRATION_BPS: {//动态设置数据域波特率与自定义波特率组件可用与否
                @SuppressWarnings("unchecked")//泛型类型转换警告
                JComboBox<String> source = (JComboBox<String>) e.getSource();
                if (isCanFd()) {
                    dataBpsComboBox.setEnabled(!"自定义".equals(source.getSelectedItem()) && "是".equals(canFdAccelerateComboBox.getSelectedItem()));

                }
                userDefinedBaudRateTextField.setEnabled("自定义".equals(source.getSelectedItem()));
                break;
            }
            case CAN_BUS_UTILIZATION_RATE: {
                @SuppressWarnings("unchecked")
                JComboBox<String> source = (JComboBox<String>) e.getSource();
                canBusUtilizationRateCycleSpinner.setEnabled("使能".equals(source.getSelectedItem()));
                flag = "使能".equals(source.getSelectedItem());
                break;
            }
            case CAN_PROTOCOL:
                dataBpsComboBox.setEnabled("是".equals(canFdAccelerateComboBox.getSelectedItem()));
                break;
            case CAN_FD_ACCELERATOR: {
                @SuppressWarnings("unchecked")//泛型类型转换警告
                JComboBox<String> source = (JComboBox<String>) e.getSource();
                dataBpsComboBox.setEnabled("是".equals(source.getSelectedItem()) && !"自定义".equals(arbitrationBpsComboBox.getSelectedItem()));
                break;
            }
            default:
                configConfirmed = false;
                setVisible(false);
                break;
        }
    }


    public void setCanConfigParameter() {
        canConfigParameter.setProtocol(String.valueOf(protocolComboBox.getSelectedItem()));
        canConfigParameter.setCanFd(canConfigParameter.getProtocol().equals("CAN FD"));
        canConfigParameter.setCanFdStandard("CAN FD ISO".equals(String.valueOf(canFdStandardComboBox.getSelectedItem())));
        canConfigParameter.setAccelerate("是".equals(canFdAccelerateComboBox.getSelectedItem()));
        canConfigParameter.setArbitrationBps(String.valueOf(arbitrationBpsComboBox.getSelectedItem()));
        canConfigParameter.setDataBps(String.valueOf(dataBpsComboBox.getSelectedItem()));
        canConfigParameter.setUserDefinedBaudrate(userDefinedBaudRateTextField.getText());
        canConfigParameter.setNormalWorkMode("正常模式".equals(workModelComboBox.getSelectedItem()));
        canConfigParameter.setTerminalResistanceEnabled("使能".equals(terminalResistanceComboBox.getSelectedItem()));
        canConfigParameter.setCanBusUtilizationRateEnabled("使能".equals(canBusUtilizationRateComboBox.getSelectedItem()));
        canConfigParameter.setCanBusUtilizationRateCycle((Integer) canBusUtilizationRateCycleSpinner.getValue());
        canConfigParameter.setSendRetry(String.valueOf(sendRetryComboBox.getSelectedItem()));
    }

    private void setLayout(JPanel panel) {
        //布局设置
        GroupLayout layout = new GroupLayout(panel); //分组布局的面板
        panel.setLayout(layout);
        layout.setAutoCreateGaps(true);//组件之间的间隙自动适应
        layout.setAutoCreateContainerGaps(true); //容器与触到容器边框的组件之间的间隙自动适应
        /*分组布局*/
        //水平并行: 垂直排列（上下排列）
        GroupLayout.ParallelGroup labelParallelGroup = layout.createParallelGroup().
                addComponent(protocolLabel).
                addComponent(canfdStandardLabel).
                addComponent(canfdAccelerateLabel).
                addComponent(arbitrationBpsLabel).
                addComponent(dataBpsLabel).
                addComponent(userDefinedBaudrateLabel).
                addComponent(workModelLabel).
                addComponent(terminalResistanceLabel).
                addComponent(canBusUtilizationRateLabel).
                addComponent(canBusUtilizationRateCycleLabel).
                addComponent(sendRetryLabel);
        GroupLayout.ParallelGroup elementParallelGroup = layout.createParallelGroup().
                addComponent(protocolComboBox).
                addComponent(canFdStandardComboBox).
                addComponent(canFdAccelerateComboBox).
                addComponent(arbitrationBpsComboBox).
                addComponent(dataBpsComboBox).
                addComponent(userDefinedBaudRateTextField).
                addComponent(workModelComboBox).
                addComponent(terminalResistanceComboBox).
                addComponent(canBusUtilizationRateComboBox).
                addComponent(canBusUtilizationRateCycleSpinner).
                addComponent(sendRetryComboBox);
        //水平串行（左右）labelParallelGroup和elementParallelGroup
        GroupLayout.SequentialGroup sequentialGroup = layout.createSequentialGroup().
                addGroup(labelParallelGroup).
                addGroup(elementParallelGroup);
        layout.setHorizontalGroup(sequentialGroup);
        //垂直并行（左右）
        GroupLayout.ParallelGroup TCPparallelGroup = layout.createParallelGroup().
                addComponent(protocolLabel).
                addComponent(protocolComboBox);
        GroupLayout.ParallelGroup CANFDStandardparallelGroup = layout.createParallelGroup().
                addComponent(canfdStandardLabel).
                addComponent(canFdStandardComboBox);
        GroupLayout.ParallelGroup CANFDAccelerateparallelGroup = layout.createParallelGroup().
                addComponent(canfdAccelerateLabel).
                addComponent(canFdAccelerateComboBox);
        GroupLayout.ParallelGroup arbitrationBpsparallelGroup = layout.createParallelGroup().
                addComponent(arbitrationBpsLabel).
                addComponent(arbitrationBpsComboBox);
        GroupLayout.ParallelGroup DataBpsparallelGroup = layout.createParallelGroup().
                addComponent(dataBpsLabel).
                addComponent(dataBpsComboBox);
        GroupLayout.ParallelGroup userDefinedparallelGroup = layout.createParallelGroup().
                addComponent(userDefinedBaudrateLabel).
                addComponent(userDefinedBaudRateTextField);
        GroupLayout.ParallelGroup workModelparallelGroup = layout.createParallelGroup().
                addComponent(workModelLabel).
                addComponent(workModelComboBox);
        GroupLayout.ParallelGroup terminalResistanceparallelGroup = layout.createParallelGroup().
                addComponent(terminalResistanceLabel).
                addComponent(terminalResistanceComboBox);
        GroupLayout.ParallelGroup CANBusUtilizationRateparallelGroup = layout.createParallelGroup().
                addComponent(canBusUtilizationRateLabel).
                addComponent(canBusUtilizationRateComboBox);
        GroupLayout.ParallelGroup CANBusUtilizationRateCycleparallelGroup = layout.createParallelGroup().
                addComponent(canBusUtilizationRateCycleLabel).
                addComponent(canBusUtilizationRateCycleSpinner);
        GroupLayout.ParallelGroup sendRetryparallelGroup = layout.createParallelGroup().
                addComponent(sendRetryLabel).
                addComponent(sendRetryComboBox);
        //垂直串行（上下）
        GroupLayout.SequentialGroup layoutSequentialGroup = layout.createSequentialGroup().
                addGroup(TCPparallelGroup).
                addGroup(CANFDStandardparallelGroup).
                addGroup(CANFDAccelerateparallelGroup).
                addGroup(arbitrationBpsparallelGroup).
                addGroup(DataBpsparallelGroup).
                addGroup(userDefinedparallelGroup).
                addGroup(workModelparallelGroup).
                addGroup(terminalResistanceparallelGroup).
                addGroup(CANBusUtilizationRateparallelGroup).
                addGroup(CANBusUtilizationRateCycleparallelGroup).
                addGroup(sendRetryparallelGroup);
        layout.setVerticalGroup(layoutSequentialGroup);
    }

    public static void main(String[] args) {
        // 创建一个示例的 LIN 配置参数
        CanConfigParameter configParameter = new CanConfigParameter();
        configParameter.setCanFd(true);
        configParameter.setChannel(1);
        configParameter.setAccelerate(true);
        configParameter.setCanFdStandard(true);
        configParameter.setArbitrationBps("500000");
        configParameter.setDataBps("1000000");
        configParameter.setNormalWorkMode(true);
        configParameter.setTerminalResistanceEnabled(true);
        configParameter.setCanBusUtilizationRateEnabled(true);
        configParameter.setCanBusUtilizationRateCycle(100);
        configParameter.setSendRetry("1");
        configParameter.setUserDefinedBaudrate("1000000");
        configParameter.setProtocol("CAN FD");

        // 在Swing线程中启动对话框
        SwingUtilities.invokeLater(() -> {
            // 创建并显示 LIN 配置对话框
            CanOpenDialog linOpenDialog = new CanOpenDialog(1);
            linOpenDialog.initializeWithConfig(configParameter);  // 使用示例配置初始化对话框
            linOpenDialog.setVisible(true);  // 显示对话框
        });
    }
}
