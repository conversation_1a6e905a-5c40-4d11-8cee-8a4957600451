package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.PowerDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

/**
 * PVD8V50电源连接面板
 */
public class PVD8V50EOperatePanel extends DeviceOperatePanel {

    public PVD8V50EOperatePanel(MainModel mainModel) {
        super(mainModel);
    }

    public PVD8V50EOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        PowerDevice httpClient = PowerDevice.getDevice(deviceModel);
//        httpClient.setCommProtocol(AppConstants.usbProtocol);
        setDeviceClient(httpClient);
        createView();
        createActions();
    }


}
