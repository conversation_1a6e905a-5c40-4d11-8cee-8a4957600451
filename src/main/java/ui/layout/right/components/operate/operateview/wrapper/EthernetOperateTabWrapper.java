package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.tosun.ToSunDevicePanel;
import ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.vector.VectorDevicePanel;
import ui.model.MainModel;

/**
 * CAN设备连接面板
 */
public class EthernetOperateTabWrapper extends BusOperateTabWrapper {

    public EthernetOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("Vector Ethernet", new VectorDevicePanel(mainModel, DeviceType.DEVICE_ETHERNET));
        addOperatePanel("同星Ethernet", new ToSunDevicePanel(mainModel, DeviceType.DEVICE_ETHERNET));
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ETHERNET;
    }

}
