package ui.layout.right.components.testscript.scriptview;

import javax.swing.*;
import java.awt.*;

public class OperationObjectEditor extends DefaultCellEditor {

    private String friendlyOperationObject;
    private String operationObject;

    public OperationObjectEditor(JTextField textField) {
        super(textField);
    }

    @Override
    public Object getCellEditorValue() {
        String value = String.valueOf(super.getCellEditorValue());
        value = value.trim();
        if (!friendlyOperationObject.isEmpty() && !operationObject.isEmpty()) {
            return String.format("%s->%s", friendlyOperationObject, value);
        }
        return value.trim();
    }

    @Override
    public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
        friendlyOperationObject = "";
        operationObject = "";
        // 使用 String.valueOf 安全转换任何类型的值为字符串
        String valueStr = String.valueOf(value);
        String[] parts = valueStr.split("->", 2);
        if (parts.length == 2) {
            friendlyOperationObject = parts[0].trim();
            operationObject = parts[1].trim();
            valueStr = parts[1].trim();  // 只显示 OperationObject 部分
        }
        return super.getTableCellEditorComponent(table, valueStr, isSelected, row, column);
    }
}
