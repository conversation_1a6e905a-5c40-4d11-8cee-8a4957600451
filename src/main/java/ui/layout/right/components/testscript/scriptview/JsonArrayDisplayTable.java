package ui.layout.right.components.testscript.scriptview;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import ui.base.OperationAssembler;
import ui.base.table.DefaultTable;
import ui.base.table.TableCell;

import java.awt.event.ActionEvent;
import java.io.Serializable;

/**
 * Json数组展示表格
 */
public class JsonArrayDisplayTable extends DefaultTable<Serializable> implements OperationAssembler<Object> {
    private static final TableCell<String> element = new TableCell<>(0, "数组元素");

    public JsonArrayDisplayTable(JSONArray jsonArray) {
        for (Object o : jsonArray) {
            addRowData((Serializable) o);
        }
        setCellEditor(jsonArray);
        createView();
        createActions();
    }

    private void setCellEditor(JSONArray jsonArray) {
        if (!jsonArray.isEmpty() && jsonArray.get(0).getClass().equals(JSONObject.class)) {
            getColumnModel().getColumn(0).setCellEditor(new JsonObjectTableCellEditor()); // 设置第一列的单元格编辑器
        }
    }

    @Override
    protected void updateTableActivated(ActionEvent e) {
        super.updateTableActivated(e);
    }

    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    protected void createMenu() {
        super.createMenu();
        addCopyMenu();
        addPasteMenu();
        addDelMenu();
    }

    @Override
    protected String[] getColumns() {
        return new String[]{element.getColumnName()};
    }

    @Override
    protected Object[] convertData(Serializable element) {
        return new Object[]{
                element
        };
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {

    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationObject(new JSONArray(getColumnList(0, Object.class)));
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}
