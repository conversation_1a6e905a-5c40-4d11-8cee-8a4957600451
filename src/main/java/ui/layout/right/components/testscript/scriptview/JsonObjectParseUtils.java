package ui.layout.right.components.testscript.scriptview;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import ui.JsonParserClazzInstance;
import ui.UIDataLoader;
import ui.base.OperationAssembler;
import ui.model.MainModel;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

@Slf4j
public class JsonObjectParseUtils {

    @SuppressWarnings("unchecked")
    public static OperationAssembler<JSONObject> getJsonObjectParser(MainModel mainModel, String keyword) {
        JsonParserClazzInstance jsonParserClazzInstance = UIDataLoader.getOperationObjectMap().get(keyword);
        if (jsonParserClazzInstance != null && jsonParserClazzInstance.getAssemblerClazz() != null) {
//            if (jsonParserClazzInstance.getInstance() != null) {
//                //保证初始化一次
//                return jsonParserClazzInstance.getInstance();
//            }
            // 为了保证初始化正确，每次都创建对话框
            OperationAssembler<JSONObject> jsonOperationAssembler;
            try {
                //MainModel参数构造器
                Constructor<? extends OperationAssembler<?>> declaredConstructor = jsonParserClazzInstance.getAssemblerClazz().getDeclaredConstructor(MainModel.class);
                jsonOperationAssembler = (OperationAssembler<JSONObject>) declaredConstructor.newInstance(mainModel);
                jsonParserClazzInstance.setInstance(jsonOperationAssembler);
                return jsonOperationAssembler;
            } catch (Exception e) {
                try {
                    //空构造器
                    jsonOperationAssembler = (OperationAssembler<JSONObject>) jsonParserClazzInstance.getAssemblerClazz().getDeclaredConstructor().newInstance();
                    jsonParserClazzInstance.setInstance(jsonOperationAssembler);
                    return jsonOperationAssembler;
                } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                         NoSuchMethodException ex) {
                    log.error(ex.getMessage(), ex);
                    return null;
                }
            }
        }
        return null;
    }
}
