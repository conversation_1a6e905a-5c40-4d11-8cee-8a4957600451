package ui.layout.right.components.testcase;

import sdk.domain.action_sequence.ActionSequence;
import sdk.domain.action_sequence.ActionSequenceCheckReporter;
import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/4/7 10:07
 * @description :
 * @modified By :
 * @since : 2023/4/7
 **/
public class TestStepModel implements BaseModel, ModelObservable, TestStepEventObserver {

    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void handleTestStepScrollPane(int row) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).handleTestStepScrollPane(row);
        }
    }

    @Override
    public void switchTestStep(String columnName) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).switchTestStep(columnName);
        }
    }

    @Override
    public void groupingByStepType(List<TestStep> testStepList) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).groupingByStepType(testStepList);
        }
    }

    @Override
    public void renderTestStepCheckReport(ActionSequenceCheckReporter actionSequenceCheckReporter) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).renderTestStepCheckReport(actionSequenceCheckReporter);
        }
    }

    @Override
    public void renderTestStepRowHeaderMarker(String stepType) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).renderTestStepRowHeaderMarker(stepType);
        }
    }

    @Override
    public void renderTestStepExecuteResultColor(ActionSequence actionSequence) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).renderTestStepExecuteResultColor(actionSequence);
        }
    }

    @Override
    public void renderTestStepRowColorByUuid(String testStepUuid) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).renderTestStepRowColorByUuid(testStepUuid);
        }
    }

    @Override
    public void renderTestStepErrorColor(Map<Integer, ActionSequence> actionSequenceErrorMap) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).renderTestStepErrorColor(actionSequenceErrorMap);
        }
    }

    @Override
    public void clearTestStepErrorColor() {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).clearTestStepErrorColor();
        }
    }

    @Override
    public void clearTestStepExecuteColor() {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).clearTestStepExecuteColor();
        }
    }

    @Override
    public void setExcelTableRowId(int row) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).setExcelTableRowId(row);
        }
    }

    @Override
    public void switchPanel(boolean isTestStepPanel) {
        for (ModelObserver observer : modelObservers) {
            ((TestStepEventObserver) observer).switchPanel(isTestStepPanel);
        }
    }
}
