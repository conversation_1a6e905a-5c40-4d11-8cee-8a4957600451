package ui.layout.right.components.testcase;

import excelcase.AutoScrollPane;
import lombok.Getter;
import sdk.domain.action_sequence.ActionSequence;
import sdk.domain.action_sequence.ActionSequenceCheckReporter;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.layout.right.components.log.LogOutputPanelView;
import ui.layout.right.components.sequence.SequenceDescriptionPanelView;
import ui.layout.right.components.taskManagement.TaskManagementPaneView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Map;

import static ui.base.table.rowHeader.RowHeaderTable.ADJUST_WIDTH;

/**
 * 动作序列侧边视图
 */
public class TestStepView extends JTabbedPane implements BaseView, TestStepEventObserver {

    private final MainModel mainModel;
    @Getter
    private final TestStepTable testStepTable;
    private final AutoScrollPane scrollPane;
    @Getter
    private final LogOutputPanelView logOutputPanelView;
    private final SequenceDescriptionPanelView sequenceDescriptionPanelView;
    private final TaskManagementPaneView taskManagementPaneView;

    public TestStepView(ClientView clientView, MainModel mainModel) {
        this.mainModel = mainModel;
        testStepTable = new TestStepTable(clientView.getRightPanelController(), mainModel, clientView);
        scrollPane = (AutoScrollPane) testStepTable.addAutoScrollRowHeader(1, ADJUST_WIDTH);
        logOutputPanelView = new LogOutputPanelView(clientView.getRightPanelController(), clientView, mainModel);
        sequenceDescriptionPanelView = new SequenceDescriptionPanelView(clientView, mainModel);
        taskManagementPaneView = new TaskManagementPaneView(clientView, mainModel);
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        JPanel testViewPanel = new JPanel();
        testViewPanel.setLayout(new BorderLayout());
        testViewPanel.add(scrollPane, BorderLayout.CENTER);
        addTab("测试过程", testViewPanel);
        addTab("日志输出", logOutputPanelView);
        addTab("序列描述", sequenceDescriptionPanelView);
        addTab("任务管理", taskManagementPaneView);
    }

    @Override
    public void createActions() {
        scrollPane.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                if (testStepTable.isEditing()) {
                    if (testStepTable.getCellEditor() != null)
                        testStepTable.getCellEditor().stopCellEditing();
                }
            }
        });
    }


    @Override
    public void registerModelObservers() {
        mainModel.getTestStepModel().registerObserver(this);
    }

    @Override
    public void switchTestStep(String columnName) {
        prepareNewTestStep();
        testStepTable.loadTestStepFromExcel(columnName);
    }

    @Override
    public void groupingByStepType(List<TestStep> testStepList) {
        testStepTable.groupingByStepType(testStepList);
    }

    @Override
    public void renderTestStepRowHeaderMarker(String stepType) {
        testStepTable.renderTestStepTableMarker(stepType);
    }

    @Override
    public void renderTestStepExecuteResultColor(ActionSequence actionSequence) {
        testStepTable.renderRowExecuteResultColor(actionSequence);
    }

    @Override
    public void renderTestStepRowColorByUuid(String testStepUuid) {
        testStepTable.renderRowColor(testStepUuid);
    }

    @Override
    public void renderTestStepErrorColor(Map<Integer, ActionSequence> actionSequenceErrorMap) {
        testStepTable.renderTableErrorColor(actionSequenceErrorMap);
    }

    @Override
    public void clearTestStepErrorColor() {
        testStepTable.clearError();
    }

    @Override
    public void clearTestStepExecuteColor() {
        testStepTable.clearExecuteColor();
    }

    @Override
    public void setExcelTableRowId(int row) {
        testStepTable.setModifyRowId(row);
    }

    @Override
    public void switchPanel(boolean isTestStepPanel) {
        setSelectedIndex(isTestStepPanel ? 0 : 1);
    }

    private void prepareNewTestStep() {
        testStepTable.clearTable();
    }


    public void handleTestStepScrollPane(int row) {
        if (scrollPane != null) {
            scrollPane.scrollToRow(row);
        }
    }

    @Override
    public void renderTestStepCheckReport(ActionSequenceCheckReporter actionSequenceCheckReporter) {
        testStepTable.clearError();
        testStepTable.renderTestStepCheckReport(actionSequenceCheckReporter);
    }

}
