package ui.layout.right;

import common.constant.ResourceConstant;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import static ui.utils.SwingUtil.getResourceAsImageIcon;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/10 18:07
 * @description :
 * @modified By :
 * @since : 2023/7/10
 **/
public class RightToolbarPanelView extends JToolBar {
    private final ClientView clientView;
    private final MainModel mainModel;
    private final ImageIcon openIcon;
    private final ImageIcon closeIcon;
    private final JLabel collapseIconLabel;
    private boolean isCloseRightPanel;

    public RightToolbarPanelView(ClientView clientView, MainModel mainModel) {
        setOrientation(JToolBar.VERTICAL);
        setLayout(new BoxLayout(this, BoxLayout.Y_AXIS));
        this.clientView = clientView;
        this.mainModel = mainModel;
        setLayout(new BorderLayout());
        float defaultRatio = 2.0f;
        collapseIconLabel = SwingUtil.makeImageLabel(ResourceConstant.LeftLayout.openRightPanelViewIconPath, defaultRatio);
        openIcon = getResourceAsImageIcon(ResourceConstant.LeftLayout.closeRightPanelViewIconPath, defaultRatio);
        closeIcon = getResourceAsImageIcon(ResourceConstant.LeftLayout.openRightPanelViewIconPath, defaultRatio);
        collapseIconLabel.setText(SwingUtil.convertToVertical("测试步骤"));
        collapseIconLabel.setHorizontalTextPosition(SwingConstants.CENTER);
        collapseIconLabel.setVerticalTextPosition(SwingConstants.BOTTOM);
        collapseIconLabel.setVerticalAlignment(SwingConstants.CENTER);
        add(collapseIconLabel, BorderLayout.CENTER);
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                setStepOrActionSideView();
            }
        });
    }


    private void setStepOrActionSideView() {
        isCloseRightPanel = !isCloseRightPanel;
        updateStepOrActionSideView(isCloseRightPanel);
    }

    public void updateStepOrActionSideView(boolean isClose) {
        if (isClose) {
            collapseIconLabel.setIcon(openIcon);
        } else {
            collapseIconLabel.setIcon(closeIcon);
        }
        clientView.displayDrawerView(!isClose);
    }

}
