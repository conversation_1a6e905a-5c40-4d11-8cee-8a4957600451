package ui.layout.right.instrument.power;

import javax.swing.*;
import java.util.Hashtable;

/**
 * 支持双精度值的自定义JSlider组件
 * 通过缩放因子将double值转换为整数范围，实现精确控制
 */
public class DoubleJSlider extends JSlider {
    private final double scaleFactor;

    /**
     * 完整参数构造方法
     * @param min 最小值(实际值)
     * @param max 最大值(实际值)
     * @param value 初始值(实际值)
     * @param scale 缩放因子(将实际值转换为整数)
     */
    public DoubleJSlider(double min, double max, double value, double scale) {
        super((int) (min * scale), (int) (max * scale), (int) (value * scale));
        this.scaleFactor = scale;
        initSlider();
    }

    /**
     * 简化构造方法(默认缩放因子为10)
     * @param min 最小值(实际值)
     * @param max 最大值(实际值)
     * @param value 初始值(实际值)
     */
    public DoubleJSlider(double min, double max, double value) {
        this(min, max, value, 10.0);
    }

    /**
     * 获取当前滑块的双精度值
     * @return 实际值(缩放后的值)
     */
    public double getDoubleValue() {
        return getValue() / scaleFactor;
    }

    /**
     * 设置滑块的双精度值
     * @param value 要设置的实际值
     */
    public void setDoubleValue(double value) {
        setValue((int) (value * scaleFactor));
    }

    /**
     * 初始化滑块样式
     * 1. 显示刻度和标签
     * 2. 设置主副刻度间距
     * 3. 更新标签显示
     */
    private void initSlider() {
        setPaintTicks(true);
        setPaintLabels(true);
        setMajorTickSpacing((int) (10 * scaleFactor));  // 扩大主刻度间距
        setMinorTickSpacing((int) (2 * scaleFactor));   // 扩大次刻度间距
        updateLabelTable();
    }

    /**
     * 更新刻度标签显示
     * 1. 整数值去掉小数部分
     * 2. 非整数保留一位小数
     */
    public void updateLabelTable() {
        Hashtable<Integer, JLabel> labelTable = new Hashtable<>();
        int min = getMinimum();
        int max = getMaximum();

        for (int i = min; i <= max; i += (int)(5 * scaleFactor)) {
            double value = i / scaleFactor;
            String labelText = value % 1 == 0 ?
                    String.format("%.0f", value) :
                    String.format("%.1f", value);
            labelTable.put(i, new JLabel(labelText));
        }
        setLabelTable(labelTable);
    }

    /**
     * 设置最大值并同步更新标签
     * @param max 最大整数值(缩放后的值)
     */
    @Override
    public void setMaximum(int max) {
        super.setMaximum(max);
        updateLabelTable();
    }

    /**
     * 设置最小值并同步更新标签
     * @param min 最小整数值(缩放后的值)
     */
    @Override
    public void setMinimum(int min) {
        super.setMinimum(min);
        updateLabelTable();
    }
}