package ui.layout.right.instrument;

import com.desaysv.CANDbcPanel;
import sdk.entity.CanDevice;
import ui.layout.left.display.components.container.can.CanContainer;
import ui.layout.left.display.components.container.can.CanDbcReceiveSettingView;
import ui.model.MainModel;
import ui.service.CanDbcReceiverControlService;

import javax.swing.*;
import java.awt.*;
import java.util.Map;

import static sdk.constants.DeviceModel.Bus.TC1016;

public class BusTraceManagerPanel extends JPanel {
    private CanDevice canDevice;
    private MainModel mainModel;

    public BusTraceManagerPanel(MainModel mainModel) {
        this.mainModel = mainModel;
        //树的第一个CAN设备devicemodel
        canDevice = CanDevice.getDevice(1, TC1016);
        setLayout(new BorderLayout());
        JPanel mainPanel = createMainPanel();
        add(mainPanel, BorderLayout.CENTER);
    }


    private JPanel createMainPanel() {
        // 主面板使用边框布局
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.setBackground(new Color(240, 240, 240)); // 浅灰色背景
        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(0.5); // 初始平分空间
        splitPane.setResizeWeight(0.5);
        splitPane.setOneTouchExpandable(true);
        splitPane.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, Color.GRAY));
        splitPane.setDividerSize(3);

        // 创建左侧面板
        JScrollPane leftScrollPane = createTraceScrollPanel("CAN1 Trace", 1);
        // 创建右侧面板
        JScrollPane rightScrollPane = createTraceScrollPanel("CAN2 Trace", 2);

        // 添加到分割面板
        splitPane.setLeftComponent(leftScrollPane);
        splitPane.setRightComponent(rightScrollPane);

        mainPanel.add(splitPane, BorderLayout.CENTER);
        return mainPanel;
    }

//    private JScrollPane createScrollableTracePanel(String title, int channel, Map<String, DbcConfig> dbcConfigs) {
//        DbcConfig dbcConfig = dbcConfigs.get(String.valueOf(channel));
//        List<String> dbcPaths = (dbcConfig != null) ? dbcConfig.getDbcPaths() : Collections.emptyList();
//        JPanel tracePanel = createTracePanel(title, channel, "", dbcPaths);
//        JScrollPane scrollPane = new JScrollPane(tracePanel);
//        // 强制显示滚动条（与截图UI一致）
//        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
//        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
//        // 优化滚动体验
//        JScrollBar vertical = scrollPane.getVerticalScrollBar();
//        vertical.setUnitIncrement(16);
//        vertical.setBlockIncrement(100);
//
//        // 添加滚动面板最小尺寸约束
//        scrollPane.setMinimumSize(new Dimension(250, 300));
//        return scrollPane;
//    }


//    private JPanel createMainPanel() {
//        JPanel mainPanel = new JPanel();
//        // 设置主面板布局和边框
//        mainPanel.setLayout(new GridLayout(1, 2, 10, 0)); // 左右两栏，间距10px
//        // 创建左侧CAN1面板
//
//        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
//        mainSplitPane.setDividerLocation(300); // 左右各占一半
//        mainSplitPane.setResizeWeight(0.5);
//        mainSplitPane.setOneTouchExpandable(true);
//        CanConfig canConfig = canDevice.loadConfig(mainModel.getAppInfo().getProject(), CanConfig.class);
//        Map<String, DbcConfig> dbcConfigs = canConfig.getDbcConfigs();
//        for (int channel = 1; channel <= 2; channel++) {
//            DbcConfig dbcConfig = dbcConfigs.get(channel + "");
//            List<String> dbcPaths = dbcConfig.getDbcPaths();
//            String title = String.format("CAN%s Trace", channel);
//            JPanel tracePanel = createTracePanel(title, channel, "", dbcPaths);
//            JScrollPane scrollPane = new JScrollPane(tracePanel);
//            scrollPane.setBorder(BorderFactory.createLineBorder(Color.RED));
//            scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS);
//            scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
//            scrollPane.getVerticalScrollBar().setUnitIncrement(16); // 设置滚动速度
//            if (channel == 1) {
//                mainSplitPane.setLeftComponent(scrollPane);
//            } else {
//                mainSplitPane.setRightComponent(scrollPane);
//            }
//
//        }
//        mainPanel.add(mainSplitPane);
//        return mainPanel;
//    }


    private JScrollPane createTraceScrollPanel(String title, int channel) {
        // 创建表格面板容器
        JPanel panel = new JPanel(new BorderLayout());

        // 创建标题标签
        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("SansSerif", Font.BOLD, 14));
        titleLabel.setForeground(Color.BLACK);
        panel.add(titleLabel, BorderLayout.NORTH);
        Map<String, CanContainer> canContainers = CanDbcReceiverControlService.getInstance().getCanContainers();
        if (canContainers != null && !canContainers.isEmpty()) {
            //获取canContainers的第一个key
            String firstCanDeviceName = canContainers.keySet().iterator().next();
            CanDbcReceiveSettingView canDbcReceiveSettingView = CanDbcReceiverControlService.getInstance().getCanDbcReceiveSettingViews().get(String.format("%s-%d",firstCanDeviceName, channel));
            if (canDbcReceiveSettingView != null) {
                CANDbcPanel candbcPanel = canDbcReceiveSettingView.getCanPanel();
                panel.add(candbcPanel, BorderLayout.CENTER);
            }

        }


        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(panel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_ALWAYS);

        // 设置滚动速度
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        scrollPane.getHorizontalScrollBar().setUnitIncrement(16);

        // 设置最小尺寸
        scrollPane.setMinimumSize(new Dimension(300, 300));

        return scrollPane;
    }


    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("CAN Trace Viewer");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(1000, 400);

            BusTraceManagerPanel busTraceManagerPanel = new BusTraceManagerPanel(null);
            frame.add(busTraceManagerPanel);

            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
        });
    }


}
