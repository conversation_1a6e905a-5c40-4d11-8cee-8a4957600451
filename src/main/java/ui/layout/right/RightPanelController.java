package ui.layout.right;

import lombok.Getter;
import ui.base.BaseController;
import ui.entry.ClientView;
import ui.layout.right.components.testscript.scriptview.TestScriptEditorTable;
import ui.layout.right.components.testscript.toolkit.CaseToolkitView;
import ui.model.MainModel;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-11 16:31
 * @description :
 * @modified By :
 * @since : 2022-4-11
 */
@Getter
public class RightPanelController implements BaseController {
    private final RightPanelView rightPanelView;

    private final ClientView clientView;

    public RightPanelController(ClientView clientView, MainModel mainModel) {
        this.clientView = clientView;
        rightPanelView = new RightPanelView(this, mainModel);
    }

    @Override
    public RightPanelView getPanelView() {
        return rightPanelView;
    }

    public CaseToolkitView getCaseToolkitView() {
        return rightPanelView.getTestScriptView().getCaseToolkitView();
    }

    //TODO：减少这种交互
    public TestScriptEditorTable getTestScriptEditTable() {
        return rightPanelView.getTestScriptEditWidget();
    }


}
