package ui.layout.aichat;

import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;

public class AIChatMgmtPane extends JTabbedPane implements BaseView {
    private final MainModel mainModel;
    private final AIActionSequenceChatPanel aiActionSequenceChatPanel;

    public AIChatMgmtPane(MainModel mainModel) {
        this.mainModel = mainModel;
        aiActionSequenceChatPanel = AIActionSequenceChatPanel.getInstance(mainModel);

        createView();
        createActions();
    }

    @Override
    public void createView() {
        addTab("聊天助手", aiActionSequenceChatPanel);
    }

    @Override
    public void createActions() {
        // 添加尺寸变化监听
        addComponentListener(new java.awt.event.ComponentAdapter() {
            public void componentResized(java.awt.event.ComponentEvent evt) {
                revalidate();
                repaint();
            }
        });
    }
}
