package ui.layout.top.dialogs.TemperatureProtection;

import ui.base.BaseView;

import javax.swing.*;
import java.awt.*;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/12/18 16:23
 */
public class TemperatureProtectionTabPanel extends JPanel implements BaseView {
    private final JButton addToScriptButton;
    private final JButton selectOddButton;
    private final JButton selectEvenButton;

    public TemperatureProtectionTabPanel() {
        addToScriptButton = new JButton("添加到脚本");
        selectOddButton = new JButton("选择奇数行");
        selectEvenButton = new JButton("选择偶数行");
        createView();
        createActions();
    }
    @Override
    public void createView() {
        setLayout(new BorderLayout(0, 0));
        JPanel panel = new JPanel();
        add(panel, BorderLayout.NORTH);
        panel.add(addToScriptButton);
        panel.add(selectOddButton);
        panel.add(selectEvenButton);


    }

    @Override
    public void createActions() {

    }
}
