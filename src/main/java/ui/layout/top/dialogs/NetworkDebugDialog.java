package ui.layout.top.dialogs;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import ui.base.BaseView;
import ui.base.dialogs.SingletonDialog;
import ui.config.SettingManager;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class NetworkDebugDialog extends SingletonDialog implements BaseView {
    private static final String FOLDER = "commons";
    private static final String REQUIRE_METHOD = "REQUIRE_METHOD";
    private static final String URL = "URL";
    private static final String DATA = "DATA";
    private static final String FILENAME = "network.debug.config";
    private final JComboBox<String> requestMethodComboBox;
    private final JTextField urlTextField;
    private final JTextArea dataTextArea;
    private final JButton debugButton;

    private final ExecutorService executor;

    public NetworkDebugDialog() {
        super();
        requestMethodComboBox = new JComboBox<>();
        requestMethodComboBox.addItem("GET");
        requestMethodComboBox.addItem("POST");
        urlTextField = new JTextField();
        dataTextArea = new JTextArea();
        dataTextArea.setRows(10);

        debugButton = SwingUtil.getDebugButton();
        executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        createView();
        createActions();
        restoreView();
    }

    @Override
    public void createView() {
        setTitle("网络调试工具");
        setModal(true);
        setSize(600, 300);
        setLocationRelativeTo(null);
        Box box = Box.createVerticalBox();
        Box box1 = Box.createHorizontalBox();
        box1.add(new JLabel("请求方式:"));
        box1.add(requestMethodComboBox);

        Box box2 = Box.createHorizontalBox();
        box2.add(new JLabel("接口URL:"));
        box2.add(urlTextField);

        Box box3 = Box.createHorizontalBox();
        box3.add(new JLabel("请求数据:"));
        box3.add(dataTextArea);

        box.add(box1);
        box.add(box2);
        box.add(box3);
        box.add(debugButton);
        setContentPane(box);
    }

    private void saveConfig() {
        SettingManager settingManager = SettingManager.folder(FOLDER).setting(FILENAME);
        settingManager.set(REQUIRE_METHOD, (String) requestMethodComboBox.getSelectedItem());
        settingManager.set(URL, urlTextField.getText());
        settingManager.set(DATA, dataTextArea.getText().replaceAll("\\n+", "\\\\n"));
        settingManager.store();
    }

    @Override
    public void restoreView() {
        SettingManager settingManager = SettingManager.folder(FOLDER).setting(FILENAME);
        requestMethodComboBox.setSelectedItem(settingManager.get(REQUIRE_METHOD));
        urlTextField.setText(settingManager.get(URL));
        dataTextArea.setText(settingManager.getOrDefault(DATA, "").replaceAll("\\\\n+", "\n"));
    }

    @Override
    public void createActions() {
        debugButton.addActionListener(e -> {
            saveConfig();
            executor.submit(() -> {
                try {
                    JsonResponse<Object> response = BaseHttpClient.postJsonResponse(UrlConstants.SERVER_URL + urlTextField.getText(),
                            JSON.parseObject(dataTextArea.getText()), new TypeReference<JsonResponse<Object>>() {
                            }, null);
                    if (!response.isOk()) {
                        SwingUtil.showWarningDialog(NetworkDebugDialog.this, response.getMessage());
                    }
                } catch (Exception ex) {
                    SwingUtil.showWarningDialog(NetworkDebugDialog.this, ex.getMessage());
                }
            });
        });
    }

}
