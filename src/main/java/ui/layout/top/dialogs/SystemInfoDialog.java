package ui.layout.top.dialogs;

import common.utils.NetworkUtils;
import ui.base.BaseView;
import ui.base.dialogs.SingletonDialog;

import javax.swing.*;
import javax.swing.border.Border;

public class SystemInfoDialog extends SingletonDialog implements BaseView {

    private final JLabel computerIpLabel;

    public SystemInfoDialog() {
        computerIpLabel = new JLabel();
        createView();
        restoreView();
    }

    @Override
    public void createView() {
        setTitle("系统信息工具");
        setModal(true);
        setSize(600, 300);
        setLocationRelativeTo(null);
        Box box = Box.createVerticalBox();
        Box box1 = Box.createHorizontalBox();
        box1.add(new JLabel("电脑ip:"));
        box1.add(computerIpLabel);
        box1.add(Box.createHorizontalGlue());

        box.add(box1);
        // 创建一个空白边框并应用到对话框的内容面板
        int padding = 10; // 设置内边距的大小
        Border border = BorderFactory.createEmptyBorder(padding, padding, padding, padding);
        box.setBorder(border);

        setContentPane(box);
    }

    @Override
    public void restoreView() {
        computerIpLabel.setText(NetworkUtils.getIpAddress());
    }


}
