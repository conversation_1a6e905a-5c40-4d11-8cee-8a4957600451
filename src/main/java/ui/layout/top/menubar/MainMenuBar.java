package ui.layout.top.menubar;

import ch.qos.logback.classic.LoggerContext;
import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.constant.ResourceConstant;
import common.utils.StringUtils;
import llm.QaLLM;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.Device;
import sdk.entity.OperationTargetHolder;
import ui.base.*;
import ui.cicd.UpgradeResponse;
import ui.config.json.devices.serial.SerialConfig;
import ui.config.xml.app.AppConfiguration;
import ui.entry.ClientView;
import ui.layout.config.UIGlobalConfig;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestScriptCaseDisplayTable;
import ui.layout.left.display.components.treemenu.devicetree.LeftDeviceMgmtPane;
import ui.layout.top.dialogs.*;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static sdk.base.BaseHttpClient.defaultGetJsonResponse;
import static ui.config.json.tree.DeviceConfigManager.getSingletonDeviceConfig;

/**
 * 菜单栏
 */
@Slf4j
public class MainMenuBar extends JMenuBar implements BaseView, BarStatusListener, AppInfoObserver , AppObserver{
    private final static String ONLINE_TUTORIAL_URL = "https://yesv-desaysv.feishu.cn/docx/BAwWdvTLfoiXavxnJKccNPrXnXd";
    private final static String CLOUD_PLATFORM_MONITOR_URL = "http://st-home.desaysv.com:83/testpc/monitor";
    private final static String IMAGE_CHECK_MONITOR_URL = "http://127.0.0.1:12399/AITestX/api/images/viewer";
    private final static int SCRIPT_VIEW_INDEX = 0;
    private final static int EXCEL_CASE_VIEW_INDEX = 1;
    private int sumTest = 1;//测试总循环数

    private JMenuItem appConfigMenuItem; //配置脚本子菜单项
    private JMenuItem actionSequenceConfigMenuItem; //配置动作序列子菜单项
    private JMenuItem deviceItemDisplayMenuItem;//配置设备项显示子菜单项
    private JMenuItem remotelyOperatedMenuItem; //远程操作子菜单项
    private JMenuItem switchSimulateModeMenuItem; //远程操作子菜单项
    private JMenuItem openProjectFolderMenuItem;
    private JMenuItem openLogFolderMenuItem;
    private JMenuItem openAppFolderMenuItem;
    private JMenuItem openItemFolderSerialLogMenuItem;
    private JMenuItem importProjectItem;
    private JMenuItem exportProjectItem;
    private JMenuItem networkDebugMenuItem;
    private JMenuItem systemInfoMenuItem;
    private JMenuItem openNetDiskInstallPathMenuItem;
    private JMenuItem exitAccountMenuItem; //退出账户
    private JMenuItem exitMenuItem; //退出子菜单项
    private JMenuItem openCloudMonitorMenuItem; //打开云平台
    private JMenuItem openImageMonitorMenuItem;
    private JMenuItem openClientLogMenuItem;
    private JMenuItem openServerLogMenuItem;
    private JMenuItem externalPictureMenuItem;
    private JMenuItem hudDetectionMenuItem;
    private JMenuItem upgradeMenuItem;
    private JMenuItem endUpgradeMenuItem;
    private JMenuItem openOnlineMenu;
    private JMenuItem aboutMenu;
    private JMenuItem scriptViewMenu;
    private JMenuItem excelCaseViewMenu;

    private final JMenu projectMenu; //项目菜单项
    private JMenu openItemFolderMenu;

    private JLabel startButtonIcon;
    private JLabel pauseButtonIcon;
    private JLabel stopButtonIcon;

    private final MainModel mainModel;
    private final ClientView clientView;
    @Setter
    private LeftDeviceMgmtPane leftDeviceMgmtPane;
    private JDialog resultDialog;
    private JTextArea resultArea;


    public MainMenuBar(MainModel mainModel, ClientView clientView) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        projectMenu = new JMenu("项目");
        createView();
        createActions();
        registerModelObservers();
        mainModel.getTestScriptEventModel().addBarStatusListener(this);//监听器
        mainModel.getAppInfo().registerObserver(this); // 注册为观察者
    }

    @Override
    public void updateAppInfo(AppInfo appInfo) {
        // 确保在事件线程更新UI
        SwingUtilities.invokeLater(() -> {
            upgradeMenuItem.setVisible(appInfo.isSmokeTest());
            endUpgradeMenuItem.setVisible(appInfo.isSmokeTest());
            log.info("更新升级菜单可见性: {}", appInfo.isSmokeTest()); // 添加日志便于调试
        });
    }

    @Override
    public void createView() {
        setLayout(new BoxLayout(this, BoxLayout.LINE_AXIS));  // 设置为BoxLayout
        openProjectFolderMenuItem = new JMenuItem("打开项目文件夹");
        openLogFolderMenuItem = new JMenu("打开日志文件夹");
        openClientLogMenuItem = new JMenuItem("打开客户端日志");
        openServerLogMenuItem = new JMenuItem("打开服务端日志");
        openLogFolderMenuItem.add(openClientLogMenuItem);

        openLogFolderMenuItem.add(openServerLogMenuItem);

        openAppFolderMenuItem = new JMenuItem("打开APP文件夹");
        openItemFolderMenu = new JMenu("打开设备数据文件夹");
        initOpenItemFolderMenu();
        importProjectItem = new JMenuItem("导入项目工程");
        exportProjectItem = new JMenuItem("导出项目工程");
        exitMenuItem = new JMenuItem("退出APP");
        exitAccountMenuItem = new JMenuItem("清除账户并退出APP");
//        projectMenu.add(configMenuItem);
//        projectMenu.add(remotelyOperatedMenuItem);
//        projectMenu.add(enableAdvancedMenuItem);
//        projectMenu.add(remotelyOperatedMenuItem);
        projectMenu.add(openProjectFolderMenuItem);
        projectMenu.add(openLogFolderMenuItem);
        projectMenu.add(openAppFolderMenuItem);
        projectMenu.add(openItemFolderMenu);
        projectMenu.add(importProjectItem);
        projectMenu.add(exportProjectItem);
        projectMenu.addSeparator();
        projectMenu.add(exitMenuItem);
        projectMenu.add(exitAccountMenuItem);
        add(projectMenu);

        //视图菜单项
        JMenu viewMenu = new JMenu("视图");
        scriptViewMenu = new JMenuItem("步骤视图");
        excelCaseViewMenu = new JMenuItem("序列视图");
        viewMenu.add(scriptViewMenu);
        viewMenu.add(excelCaseViewMenu);
        add(viewMenu);

        //配置菜单项
        JMenu configMenu = new JMenu("配置");
        appConfigMenuItem = new JMenuItem("步骤视图配置");
        actionSequenceConfigMenuItem = new JMenuItem("动作序列配置");
        deviceItemDisplayMenuItem = new JMenuItem("设备项显示配置");
        configMenu.add(appConfigMenuItem);
        configMenu.add(actionSequenceConfigMenuItem);
        configMenu.add(deviceItemDisplayMenuItem);
        add(configMenu);

        //日志菜单项
        JMenu toolMenu = new JMenu("工具");
        remotelyOperatedMenuItem = new JMenuItem("远程操作");
        switchSimulateModeMenuItem = new JMenuItem("切换模拟方式");
        externalPictureMenuItem = new JMenuItem("导入外部图片");
        networkDebugMenuItem = new JMenuItem("网络调试工具");
        openCloudMonitorMenuItem = new JMenuItem("云平台监控中心");
        openImageMonitorMenuItem = new JMenuItem("检索图像测试结果");
        hudDetectionMenuItem = new JMenuItem("HUD检测");
        upgradeMenuItem = new JMenuItem("CICD升级测试");
        endUpgradeMenuItem = new JMenuItem("结束升级测试任务");
        toolMenu.add(remotelyOperatedMenuItem);
        toolMenu.add(switchSimulateModeMenuItem);
        toolMenu.add(externalPictureMenuItem);
        toolMenu.add(networkDebugMenuItem);
        toolMenu.add(openCloudMonitorMenuItem);
        toolMenu.add(openImageMonitorMenuItem);
        toolMenu.add(hudDetectionMenuItem);
        toolMenu.add(upgradeMenuItem);
        toolMenu.add(endUpgradeMenuItem);

        add(toolMenu);

        //帮助菜单项
        JMenu helpMenu = new JMenu("帮助");
        systemInfoMenuItem = new JMenuItem("系统信息");
        openNetDiskInstallPathMenuItem = new JMenuItem("打开网盘安装路径");
        openOnlineMenu = new JMenuItem("在线帮助");
        aboutMenu = new JMenuItem("关于此软件");
        helpMenu.add(systemInfoMenuItem);
        helpMenu.add(openNetDiskInstallPathMenuItem);
        helpMenu.add(openOnlineMenu);
        helpMenu.add(aboutMenu);
        add(helpMenu);

        startButtonIcon = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.runScriptIconPath, 2.5f);
        startButtonIcon.setBorder(BorderFactory.createEmptyBorder(2, 8, 0, 4));
        add(startButtonIcon);

        pauseButtonIcon = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.pauseScriptIconPath, 2.2f);
        pauseButtonIcon.setBorder(BorderFactory.createEmptyBorder(2, 8, 0, 4));
//        pauseButtonIcon.setEnabled(false);
        add(pauseButtonIcon);

        stopButtonIcon = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.stopScriptIconPath, 2.5f);
        stopButtonIcon.setBorder(BorderFactory.createEmptyBorder(2, 8, 0, 4));
        add(stopButtonIcon);

        pauseButtonIcon.setEnabled(false);
        stopButtonIcon.setEnabled(false);

        // 创建一个搜索框
        add(Box.createHorizontalGlue()); // 将搜索框推到右边
        // 创建搜索框面板
        JPanel searchPanel = new JPanel();
        searchPanel.setLayout(new FlowLayout(FlowLayout.RIGHT));
        JTextField searchField = new JTextFieldWithHint("搜索");
        searchField.setPreferredSize(new Dimension(200, 30));

        // 移除默认的搜索动作监听器
        for (ActionListener al : searchField.getActionListeners()) {
            searchField.removeActionListener(al);
        }
        // 初始化结果对话框
        initResultDialog();
        // 只响应回车键事件
        searchField.addActionListener(e -> {
            String query = searchField.getText().trim();
            if (!query.isEmpty()) {
                // 禁用搜索框以防止多次点击
                searchField.setEnabled(false);
                // 创建并执行SwingWorker
                SwingWorker<String, Void> worker = new SwingWorker<String, Void>() {
                    @Override
                    protected String doInBackground() throws Exception {
                        // 在后台线程中执行请求
                        return QaLLM.sendQARequest(query);
                    }

                    @Override
                    protected void done() {
                        try {
                            // 获取后台任务的结果
                            String result = get();
                            resultArea.setText(result);
                        } catch (Exception ex) {
                            resultArea.setText("请求出错: " + ex.getCause().getMessage());
                        } finally {
                            // 重新启用搜索框
                            searchField.setEnabled(true);
                        }

                        // 显示结果对话框
                        resultDialog.setVisible(true);
                    }
                };

                worker.execute();
            }
        });
    }

    //注册为AppModel的观察者
    @Override
    public void registerModelObservers() {
        mainModel.getAppModel().registerObserver(this);
    }

    @Override
    public void switchView(TestView testView) {
        // 更新菜单项的显示状态
        updateMenuItems(testView);
    }

    /**
     * 根据当前的TestView更新菜单项的显示状态
     *
     * @param testView 当前的TestView
     */
    private void updateMenuItems(TestView testView) {
        appConfigMenuItem.setVisible(testView == TestView.OPERATION_STEP);
        actionSequenceConfigMenuItem.setVisible(testView == TestView.ACTION_SEQUENCE);

        // 更新按钮状态
        iconStatusSetting(testView == TestView.OPERATION_STEP);
    }

    /**
     * 初始化结果对话框
     */
    private void initResultDialog() {
        // 创建结果显示区域
        resultArea = new JTextArea();
        resultArea.setEditable(false);
        resultArea.setLineWrap(true);
        resultArea.setWrapStyleWord(true);

        // 创建结果显示对话框（模态）
        resultDialog = new JDialog(clientView, "搜索结果", true);
        resultDialog.setSize(400, 300);

        // 创建一个滚动面板来包含文本区域
        JScrollPane scrollPane = new JScrollPane(resultArea);
        resultDialog.setContentPane(scrollPane);

        // 设置对话框关闭时的行为
        resultDialog.setDefaultCloseOperation(JDialog.HIDE_ON_CLOSE);

        // 相对于clientView居中显示
        resultDialog.setLocationRelativeTo(clientView);
    }

    private void initOpenItemFolderMenu() {
        JMenu openItemFolderSerialMenu = new JMenu("串口");
        openItemFolderSerialLogMenuItem = new JMenuItem("串口日志文件夹");
        openItemFolderSerialMenu.add(openItemFolderSerialLogMenuItem);
        openItemFolderMenu.add(openItemFolderSerialMenu);
    }

    private void openNetDiskInstallPathMenu() {
        String path = "\\\\hzhe003a\\DFS\\DIDA3019\\Div IC\\ST\\02_PTV\\07_小组管理\\03_AT\\01_自动化测试系统\\06-FlyTest\\01-软件版本\\03-最新版本";
        try {
            // 获取桌面实例
            Desktop desktop = Desktop.getDesktop();
            // 创建指向路径的文件对象
            File file = new File(path);

            if (file.exists()) {
                log.info("打开软件安装路径：{}", path);
                // 使用桌面应用打开文件夹
                desktop.open(file);
            } else {
                log.error("软件安装路径不存在: {}", path);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } catch (UnsupportedOperationException e) {
            log.error("当前平台不支持打开文件管理器");
        }
    }

    private void openOnlineMenu(String url) {
        try {
            // 获取当前系统桌面扩展
            Desktop desktop = Desktop.getDesktop();
            // 判断系统桌面是否支持要执行的功能
            if (desktop.isSupported(Desktop.Action.BROWSE)) {
                // 获取系统默认浏览器打开链接
                desktop.browse(new URI(url));
            }
        } catch (java.net.URISyntaxException e) {
            log.error(e.getMessage(), e);
        } catch (NullPointerException e) {
            // 此为uri为空时抛出异常
            log.error(e.getMessage(), e);
        } catch (IOException e) {
            // 此为无法获取系统默认浏览器
            log.error(e.getMessage(), e);
        }
    }

    private void openSystemInfoDialog() {
        SystemInfoDialog.getInstance(SystemInfoDialog.class).setVisible(true);
    }

    private void openExternPictureDialog() {
        Class<?>[] parameterTypes = new Class[]{MainModel.class, ClientView.class};
        Object[] parameters = new Object[]{mainModel, clientView};
        ExternPictureDialog.getInstance(ExternPictureDialog.class, parameterTypes, parameters).setVisible(true);
    }

    private void switchSimulateMode() {
        try {
            // 获取当前模拟状态
            JsonResponse<Boolean> response = BaseHttpClient.get(
                    UrlConstants.ConfigUrls.GET_MOCK_ENABLED,
                    new TypeReference<JsonResponse<Boolean>>() {
                    }
            );

            if (!response.isOk()) {
                JOptionPane.showMessageDialog(this, "获取模拟状态失败");
                return;
            }

            boolean currentMode = response.getData();
            // 切换状态
            boolean newMode = !currentMode;

            // 发送更新请求
            Map<String, Object> params = new HashMap<>();
            params.put("mockEnabled", newMode);
            JsonResponse<String> updateResponse = BaseHttpClient.defaultUpdate(
                    UrlConstants.ConfigUrls.SET_MOCK_ENABLED,
                    params,
                    new TypeReference<JsonResponse<String>>() {
                    }
            );

            if (updateResponse.isOk()) {
                String message = newMode ? "已切换到模拟模式" : "已切换到正常模式";
                switchSimulateModeMenuItem.setText(newMode ? "切换到正常模式" : "切换到模拟模式");
                JOptionPane.showMessageDialog(this, message);
            } else {
                JOptionPane.showMessageDialog(this, "切换模式失败");
            }
        } catch (Exception e) {
            log.error("切换模式失败", e);
            JOptionPane.showMessageDialog(this, "切换模式发生错误: " + e.getMessage());
        }
    }

    private void openHudDetectionDialog() {
        HudDetectionDialog dialog = new HudDetectionDialog();
        dialog.setVisible(true);
    }

    private void openNetworkDebugDialog() {
        NetworkDebugDialog.getInstance(NetworkDebugDialog.class).setVisible(true);
    }

    private void about() {
        String message = String.format("%s %s \n负责人:<EMAIL>", AppConstants.APP_NAME, mainModel.getAppInfo().getAppVersion());

        JTextArea textArea = new JTextArea(message);
        textArea.setEditable(false);
        textArea.setBorder(null);
        textArea.setBackground(UIManager.getColor("Label.background"));
        Font font = textArea.getFont().deriveFont(20.0f);
        textArea.setFont(font);
        textArea.setLineWrap(true);
        textArea.setWrapStyleWord(true);

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(400, 200));
        JOptionPane.showMessageDialog(null, scrollPane, "关于此软件", JOptionPane.INFORMATION_MESSAGE, Objects.requireNonNull(SwingUtil.getResourceAsImageIcon(ResourceConstant.IconLayout.faviconIconPath, 2)));
    }

    /**
     * 获取TestScriptCaseDisplayTable实例
     *
     * @return TestScriptCaseDisplayTable实例
     */
    private TestScriptCaseDisplayTable getTestScriptCaseDisplayTable() {
        // 从客户端视图中获取TestScriptCaseDisplayTable
        if (clientView != null && clientView.getLeftPanelController() != null) {
            return clientView.getLeftPanelController().getLeftPanelView()
                    .getDisplayTabPane().getCaseMgmtTabPaneView()
                    .getTestScriptCaseTabPaneView().getTestScriptCaseDisplayTable();
        }
        return null;
    }

    @Override
    public void createActions() {
        AppConfigDialog appConfigDialog = new AppConfigDialog(this, mainModel);
        ActionSequenceConfigDialog actionSequenceConfigDialog = new ActionSequenceConfigDialog(mainModel);
        exitMenuItem.addActionListener(e -> {
//            ClientManager.getTestClientManager().exitClient(mainModel.getAppInfo());
            System.exit(0);
        });
        appConfigMenuItem.addActionListener(e -> appConfigDialog.setVisible(true));
        actionSequenceConfigMenuItem.addActionListener(e -> actionSequenceConfigDialog.setVisible(true));
        deviceItemDisplayMenuItem.addActionListener(e -> {
            DeviceItemDisplayConfigDialog deviceItemDisplayConfigDialog = new DeviceItemDisplayConfigDialog(mainModel, leftDeviceMgmtPane);
            deviceItemDisplayConfigDialog.setVisible(true);
        });
        WebSocketServerDialog webSocketServerDialog = new WebSocketServerDialog(this, mainModel);
        webSocketServerDialog.triggerConnectButton(); // 触发 连接测试事件 连接websocket
        remotelyOperatedMenuItem.addActionListener(e -> webSocketServerDialog.setVisible(true));
        openProjectFolderMenuItem.addActionListener(e -> {
            String fileName = String.format("D:\\FlyTest\\data\\client\\projects\\%s", mainModel.getAppInfo().getProject());
            try {
                Desktop.getDesktop().browse(new File(fileName).toURI());
            } catch (IOException ex) {
                log.error(ex.getMessage(), ex);
            }
        });
        openClientLogMenuItem.addActionListener(e -> {
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            String fileName = context.getName();
            try {
                Desktop.getDesktop().browse(new File(fileName).toURI());
            } catch (IOException ex) {
                log.error(ex.getMessage(), ex);
            }
        });
        openServerLogMenuItem.addActionListener(e -> {
            try {
                String fileName = BaseHttpClient.getForString(UrlConstants.ServerUrls.SERVER_LOG_FILE, false);
                Desktop.getDesktop().browse(new File(fileName).toURI());
            } catch (IOException ex) {
                log.error(ex.getMessage(), ex);
            }
        });
        openAppFolderMenuItem.addActionListener(e -> {
            String fileName = "D:\\FlyTest\\data\\client\\app\\config";
            try {
                Desktop.getDesktop().browse(new File(fileName).toURI());
            } catch (IOException ex) {
                log.error(ex.getMessage(), ex);
            }
        });
        exitAccountMenuItem.addActionListener(e -> {
            AppInfo appInfo = mainModel.getAppInfo();
            appInfo.setSecretKey("");
            appInfo.setPasswordLength(0);
            AppConfiguration.getInstance().getAppConfig().setAppInfo(appInfo).save();
            OperationTargetHolder.getTestClientKit().logoutUser(mainModel.getAppInfo());
            System.exit(0);
        });
        systemInfoMenuItem.addActionListener(e -> openSystemInfoDialog());
        externalPictureMenuItem.addActionListener(e -> openExternPictureDialog());
        switchSimulateModeMenuItem.addActionListener(e -> switchSimulateMode());
        hudDetectionMenuItem.addActionListener(e -> openHudDetectionDialog());
        upgradeMenuItem.addActionListener(e -> {
            // 创建并执行后台任务
            new UpgradeWorker(mainModel).execute();
        });
        endUpgradeMenuItem.addActionListener(e -> {
            new SwingWorker<Boolean, Void>() {
                @Override
                protected Boolean doInBackground() throws Exception {
                    //通知升级结果给云文档
                    reportUpgradeResultToCloudDc();
                    return uploadResultToEndTheTask("{\"result\":\"endTheTask\"}");
                }

                @Override
                protected void done() {
                    try {
                        boolean success = get();
                        if (success) {
                            SwingUtilities.invokeLater(() -> {
                                log.info("通知本地python进程结束此次任务成功！");
                                SwingUtil.showNonModalDialog(MainMenuBar.this, "此次任务已结束，系统将在3分钟后退出！");
                                //等待3分钟后
                                log.info("自动升级测试任务已结束,3分钟后将自动退出系统...");
                                // 使用javax.swing.Timer替代Thread.sleep()，避免线程暂停导致卡面冻住
                                Timer timer = new Timer(3 * 60 * 1000, event -> {
                                    clientView.shutdownApp();
                                    System.exit(0);
                                });
                                timer.setRepeats(false); // 设置为只执行一次
                                timer.start();
                            });
                        } else {
                            JOptionPane.showMessageDialog(MainMenuBar.this, "结束此次任务失败！", "错误", JOptionPane.ERROR_MESSAGE);
                        }
                    } catch (Exception ex) {
                        log.error("异步任务异常", ex);
                        JOptionPane.showMessageDialog(MainMenuBar.this, "操作失败: " + ex.getMessage());
                    }
                }
            }.execute();
        });

        upgradeMenuItem.setVisible(mainModel.getAppInfo().isSmokeTest());
        endUpgradeMenuItem.setVisible(mainModel.getAppInfo().isSmokeTest());
        networkDebugMenuItem.addActionListener(e -> openNetworkDebugDialog());
        openCloudMonitorMenuItem.addActionListener(e -> openOnlineMenu(CLOUD_PLATFORM_MONITOR_URL));
        openImageMonitorMenuItem.addActionListener(e -> openOnlineMenu(IMAGE_CHECK_MONITOR_URL));
        openOnlineMenu.addActionListener(e -> openOnlineMenu(ONLINE_TUTORIAL_URL));
        openNetDiskInstallPathMenuItem.addActionListener(e -> openNetDiskInstallPathMenu());
        aboutMenu.addActionListener(e -> about());
        scriptViewMenu.addActionListener(e -> {
            if (!clientView.getDeviceMgmtDockable().asDockable().isDockableShowing()) {
                clientView.getDeviceMgmtButton().doClick();
            }
            clientView.getActionMgmtButton().setEnabled(true);
            clientView.getLogMgmtButton().setEnabled(true);
            clientView.getSmokingTestButton().setEnabled(false);
            jumpToView(SCRIPT_VIEW_INDEX);
            UIGlobalConfig.getInstance().getUILayout().setViewTabIndex(SCRIPT_VIEW_INDEX);
            iconStatusSetting(true);
        });
        excelCaseViewMenu.addActionListener(e -> {
            clientView.getLogMgmtButton().setEnabled(false);
            clientView.getActionMgmtButton().setEnabled(false);
            clientView.getSmokingTestButton().setEnabled(true);
            jumpToView(EXCEL_CASE_VIEW_INDEX);
            UIGlobalConfig.getInstance().getUILayout().setViewTabIndex(EXCEL_CASE_VIEW_INDEX);
            iconStatusSetting(false);
        });
        openItemFolderSerialLogMenuItem.addActionListener(e -> {
            //判断是否有串口 采用自定义配置
            String customPath = processCustomSerialLog();
            String fileName;
            if (StringUtils.isEmpty(customPath)) {
                fileName = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\serialLogs", mainModel.getAppInfo().getProject());
            } else {
                fileName = customPath;
            }
            File folder = new File(fileName);

            if (!folder.exists() || !folder.isDirectory()) {
                JOptionPane.showMessageDialog(null, "请先打开串口，才会生成文件夹。", "提示", JOptionPane.WARNING_MESSAGE);
            } else {
                try {
                    Desktop.getDesktop().browse(folder.toURI());
                } catch (IOException ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
        });

        importProjectItem.addActionListener(e -> {
            TestScriptCaseDisplayTable displayTable = getTestScriptCaseDisplayTable();
            ScriptUtils.getInstance(mainModel, displayTable).importFileScript();
        });

        exportProjectItem.addActionListener(e -> {
            TestScriptCaseDisplayTable displayTable = getTestScriptCaseDisplayTable();
            ScriptUtils.getInstance(mainModel, displayTable).exportFileScript();
        });

        startButtonIcon.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mainModel.getTestScriptEventModel().runScript(sumTest);
            }
        });

        pauseButtonIcon.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mainModel.getTestScriptEventModel().pauseScript();
                pauseButtonIcon.setEnabled(false);
            }
        });

        stopButtonIcon.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mainModel.getTestScriptEventModel().stopScript();
                iconStatusChange(false);
            }
        });
    }

    private String processCustomSerialLog() {
        java.util.List<Device> devices = getSingletonDeviceConfig().getDevices();
        //Set<Device> devices = mainModel.getDeviceManageModel().getDevices();
        Device serialDevice = devices.stream().filter(device -> device.getDeviceType().equals("serialType")).findFirst().orElse(null);
        if (serialDevice != null) {
            SerialConfig serialConfig = serialDevice.loadConfig(mainModel.getAppInfo().getProject(), SerialConfig.class);
            if (serialConfig.isUseCustomLogPath() && !StringUtils.isEmpty(serialConfig.getCustomLogPath())) {//采用自定义路径
                return serialConfig.getCustomLogPath();
            }
        }
        return null;
    }

    @Override
    public void iconStatusChange(boolean isRunning) {  //运行状态
        startButtonIcon.setEnabled(!isRunning);
        stopButtonIcon.setEnabled(isRunning);
        pauseButtonIcon.setEnabled(isRunning);
    }

    @Override
    public void pauseStatusChange(boolean isResume) {
        pauseButtonIcon.setEnabled(isResume);
    }

    @Override
    public void changeSumTestCycle(int sumTestCycle) {
        sumTest = sumTestCycle;
    }

    public void iconStatusSetting(boolean isVisible) { //可见状态
        stopButtonIcon.setVisible(isVisible);
        pauseButtonIcon.setVisible(isVisible);
        startButtonIcon.setVisible(isVisible);

        importProjectItem.setVisible(isVisible);
        exportProjectItem.setVisible(isVisible);
    }

    private static void openScriptDefineTable() {
        String scriptDefineTablePath = "D:\\FlyTest\\data\\client\\app\\config\\自动化脚本序列定义表.xlsx";
        File localFile = new File(scriptDefineTablePath);
        if (!localFile.exists()) {
            log.warn("本地路径没有自动化脚本序列定义表.xlsx文件");
            return;
        }
        try {
            Desktop.getDesktop().open(localFile);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    private void jumpToView(int index) {
        clientView.getLeftPanelController().getLeftPanelView().getDisplayTabPane().setSelectedIndex(0); //测试用例视图
        clientView.getLeftPanelController().getLeftPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().setSelectedIndex(index); //步骤视图还是动作序列
        mainModel.getAppModel().switchView(index == 0 ? TestView.OPERATION_STEP : TestView.ACTION_SEQUENCE);
    }

    public static void main(String[] args) {
        openScriptDefineTable();
    }

    private class UpgradeWorker extends SwingWorker<Void, Void> {
        private final MainModel mainModel;

        public UpgradeWorker(MainModel mainModel) {
            this.mainModel = mainModel;
        }

        @Override
        protected Void doInBackground() throws Exception {
            // 在后台线程执行耗时操作
            performUpgrade();
            return null;
        }

        @Override
        protected void done() {
            try {
                // 处理完成后的UI更新
                get(); // 获取结果（如果有异常会在此处抛出）
//                JOptionPane.showMessageDialog(MainMenuBar.this, "升级完成", "提示", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                log.error("升级失败", e);
                JOptionPane.showMessageDialog(MainMenuBar.this, "升级失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            } finally {
                // 恢复按钮状态
                SwingUtilities.invokeLater(() -> upgradeMenuItem.setEnabled(true));
            }
        }

        private void performUpgrade() {
            JsonResponse<UpgradeResponse> response = defaultGetJsonResponse(
                    UrlConstants.CicdTestUrls.AUTO_UPGRADE,
                    new TypeReference<JsonResponse<UpgradeResponse>>() {
                    }
            );

            if (response.isOk()) {
                log.info("升级结果: {}", response);
                UpgradeResponse upgradeResponse = response.getData();
                if (upgradeResponse.isSuccess()) {
                    // 使用线程安全方式更新UI
                    SwingUtilities.invokeLater(() -> {
                        mainModel.getTestVersion().setQnxVersion(upgradeResponse.getQnxVersionInfo());
                        mainModel.getTestVersion().setMcuVersion(upgradeResponse.getMcuVersionInfo());
                        mainModel.getTestVersion().setSocVersion(upgradeResponse.getSocVersionInfo());
                        mainModel.getTestVersion().setVersion(upgradeResponse.getVersionInfo());
                        mainModel.getTestCaseTableModel().onUpgradeSuccess(true);
                    });
                }else {
                    SwingUtilities.invokeLater(() -> {
                        log.error("升级失败: {}", upgradeResponse.getErrorMessage());
                        if (upgradeResponse.getErrorMessage() != null && !upgradeResponse.getErrorMessage().isEmpty()) {
                            if (upgradeResponse.getErrorMessage().contains("403")) {
                                endUpgradeMenuItem.doClick();
                            }
                        }
                    });
                }
            }
        }
    }

    /**
     * 通知本地python进程结束此次任务
     *
     * @param resultJson 测试结果JSON
     */
    public boolean uploadResultToEndTheTask(String resultJson)  {
        try{
            URL url = new URL("http://localhost:5000/receive_test_result");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json; utf-8");
            conn.setDoOutput(true);

            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = resultJson.getBytes("utf-8");
                os.write(input, 0, input.length);
            } catch (Exception e) {
                log.error("通知本地python进程结束此次任务失败", e);
            }

            int code = conn.getResponseCode();
            if (code == 200) {
                return  true;
            } else {
                log.error("通知本地python进程结束此次任务失败，HTTP状态码：{}", code);
            }
        } catch (Exception e) {
            log.error("通知本地python进程结束此次任务失败", e);
        }
        return false;
    }

    //通知升级结果给云文档
    public void reportUpgradeResultToCloudDc() {
        JsonResponse<Boolean> response = defaultGetJsonResponse(
                UrlConstants.CicdTestUrls.CLIENT_REPORT_UPGRADE_RESULT_TO_CLOUD_DC,
                new TypeReference<JsonResponse<Boolean>>() {}
        );

        if (response.isOk()) {
            log.info("升级结果写入云文档成功！");
        }else {
            log.error("升级结果写入云文档失败！");
        }
    }
}
