package ui.layout.left.display.dialogs;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import lombok.Getter;
import sdk.base.operation.Operation;
import sdk.domain.bus.CanPts;
import sdk.domain.bus.CanPtsMessage;
import ui.base.OperationAssemblerPanel;

import javax.swing.*;
import java.awt.*;

@Getter
public class CanSendConfigDialog extends OperationAssemblerPanel<CanPts> {
    protected final JComboBox<String> canMessageTypeJComboBox;
    protected final JTextField sendMessageIdTextField;
    protected final JTextField sendMessageTextField;
    protected final JPanel mainPanel;

    public CanSendConfigDialog() {
        sendMessageIdTextField = new JTextField();
        sendMessageTextField = new JTextField();

        // 设置主面板
        setLayout(new BorderLayout());
        mainPanel = new JPanel();
        // 报文消息类型面板
        JLabel canMessageType = new JLabel("消息类型:");
        String[] comboBoxCanMessageTypeItems = {"CAN", "CANFD"};
        canMessageTypeJComboBox = new JComboBox<>(comboBoxCanMessageTypeItems);
        JPanel ptsPanel = new JPanel();
        ptsPanel.add(canMessageType);
        ptsPanel.add(canMessageTypeJComboBox, "wrap");
        mainPanel.add(ptsPanel);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 10)));

        // 发送报文ID面板
        JPanel panel1 = createLabelFieldPanel("发送报文ID：0x", sendMessageIdTextField, 10);
        mainPanel.add(panel1);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 10)));

        // 发送报文内容面板
        JPanel panel2 = createLabelFieldPanel("发送报文内容：", sendMessageTextField, 50);
        mainPanel.add(panel2);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 10)));
        add(mainPanel, BorderLayout.CENTER);
    }

    /**
     * 创建标签和文本字段的面板，确保布局整齐一致
     */
    public JPanel createLabelFieldPanel(String labelText, JTextField textField, int columns) {
        JPanel panel = new JPanel();
        panel.setLayout(new BorderLayout(5, 0));
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 30));

        JLabel label = new JLabel(labelText);
        label.setPreferredSize(new Dimension(100, 25));
        panel.add(label, BorderLayout.WEST);

        textField.setColumns(columns);
        textField.setMaximumSize(new Dimension(Integer.MAX_VALUE, 25));
        textField.setMinimumSize(new Dimension(100, 25));
        panel.add(textField, BorderLayout.CENTER);

        return panel;
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        CanPtsMessage canPtsMessage = JSON.to(CanPtsMessage.class, injectedOperation.getOperationObject());
        canMessageTypeJComboBox.setSelectedItem(canPtsMessage.getCanType());
        sendMessageIdTextField.setText(canPtsMessage.getSendMessageId());
        sendMessageTextField.setText(canPtsMessage.getSendMessage());
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        CanPtsMessage canPtsMessage = JSON.to(CanPtsMessage.class, injectedOperation.getOperationObject());
        canPtsMessage.setCanType((String) canMessageTypeJComboBox.getSelectedItem());
        canPtsMessage.setSendMessageId(sendMessageIdTextField.getText());
        canPtsMessage.setSendMessage(sendMessageTextField.getText());
        return injectedOperation.setOperationObject(canPtsMessage);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}