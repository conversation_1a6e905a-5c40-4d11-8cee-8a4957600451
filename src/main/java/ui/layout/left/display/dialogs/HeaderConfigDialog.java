package ui.layout.left.display.dialogs;

import excelcase.config.json.CaseConfigJson;
import excelcase.config.json.CaseConfigJsonManager;
import excelcase.config.json.CaseContent;
import excelcase.config.json.CaseHeaderContent;
import excelcase.tree.ExcelTableConfigTree;
import lombok.extern.slf4j.Slf4j;
import ui.base.BaseView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseRenderTabbedPane;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static excelcase.config.json.CaseConfigJsonManager.syncExcelCaseConfigFile;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/3/27 11:43
 * @description : 表头配置对话框
 * @modified By :
 * @since : 2023/3/27
 **/
@Slf4j
public class HeaderConfigDialog extends JDialog implements BaseView {
    private final ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane;
    private final Container container;
    private final ExcelTableConfigTree excelTableConfigTree;
    private final JCheckBox allHeaderConfigCheckBox;
    private final JButton saveExcelHeaderBtn;

    public HeaderConfigDialog(ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane, CaseContent caseContent) {
        this.excelCaseRenderTabbedPane = excelCaseRenderTabbedPane;
        excelTableConfigTree = new ExcelTableConfigTree();
        excelTableConfigTree.initHeaderConfigTree(caseContent);
        container = this.getContentPane();
        allHeaderConfigCheckBox = new JCheckBox("此表头设置应用到所有表");
        allHeaderConfigCheckBox.setSelected(true);
        saveExcelHeaderBtn = new JButton("确定");
        createView();
        createActions();
        finalSetting();
    }

    @Override
    public void createView() {
        setTitle("表头设置");
        setSize(new Dimension(500, 300));
        setLayout(new BorderLayout());
        Box verticalBox = Box.createVerticalBox();
        verticalBox.add(allHeaderConfigCheckBox);
        verticalBox.add(saveExcelHeaderBtn);
        JScrollPane scroll = new JScrollPane(excelTableConfigTree);
        container.add(scroll, BorderLayout.CENTER);
        container.add(verticalBox, BorderLayout.SOUTH);
    }

    @Override
    public void createActions() {
        saveExcelHeaderBtn.addActionListener(e -> saveCaseHeaderConfig());
    }

    private void finalSetting() {
        SwingUtil.centerInScreen(this);
        setModal(true);
        setVisible(true);
        setResizable(false);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    }


    /**
     * 保存表头设置，FIXME：保存时会再次刷新id，uuid，tableName
     */
    private void saveCaseHeaderConfig() {
        List<ExcelCaseTable> excelCaseTableList = new ArrayList<>();
        if (allHeaderConfigCheckBox.isSelected()) {
            Collection<ExcelCaseTable> values = excelCaseRenderTabbedPane.getAllDisplayTableMap().values();
            excelCaseTableList.addAll(values);
        } else {
            excelCaseTableList.add(excelCaseRenderTabbedPane.getSelectedExcelCaseTable());
        }
        for (ExcelCaseTable table : excelCaseTableList) {
            new SwingWorker<Void, Void>() {
                List<CaseHeaderContent> tableHeaderTreeConfigList;
                List<CaseHeaderContent> tableHeaderJsonConfigList;

                @Override
                protected Void doInBackground() {
                    try {
                        tableHeaderTreeConfigList = CaseConfigJsonManager.getCaseHeaderConfigByTreeNodes(table, excelTableConfigTree);
                        tableHeaderJsonConfigList = CaseConfigJson.getInstance().getCaseContentBySheetName(table.getSheetName()).getCaseHeaderContentList();
                        Map<Integer, CaseHeaderContent> treeNodeHeaderMap = tableHeaderTreeConfigList.stream()
                                .collect(Collectors.toMap(CaseHeaderContent::getColumnIndex, Function.identity()));
                        tableHeaderJsonConfigList = tableHeaderJsonConfigList.stream()
                                .peek(caseHeader -> {
                                    CaseHeaderContent caseHeaderContent = treeNodeHeaderMap.get(caseHeader.getColumnIndex());
                                    if (caseHeaderContent != null) {
                                        int width = table.getColumnModel().getColumn(caseHeader.getModelIndex()).getWidth();
                                        caseHeader.setVisible(caseHeaderContent.isVisible());
                                        caseHeader.setColumnWidth(width == 0 ? caseHeader.getColumnWidth() : width);
                                    }
                                }).collect(Collectors.toList());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        if (e instanceof IllegalStateException) {
                            SwingUtil.showWarningDialog(null, e.getMessage());
                        }
                    }
                    return null;
                }

                @Override
                protected void done() {
                    excelCaseRenderTabbedPane.renderTableByHeaderConfig(tableHeaderJsonConfigList, table);
                    CaseConfigJson.getInstance().getCaseContentBySheetName(table.getSheetName()).setCaseHeaderContentList(tableHeaderJsonConfigList);
                    syncExcelCaseConfigFile();
                }
            }.execute();
        }
        closeDialog();
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        setModal(false);
        dispose();
    }
}
