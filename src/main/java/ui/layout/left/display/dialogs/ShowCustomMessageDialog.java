package ui.layout.left.display.dialogs;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/3/5 18:45
 */


import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class ShowCustomMessageDialog extends JDialog implements BaseView {


    private final JLabel label;
    private final JButton continueButton, cancelButton;
    private final MainModel mainModel;

    public ShowCustomMessageDialog(MainModel mainModel, String message) {
        this.mainModel = mainModel;
        label = new JLabel(message, SwingConstants.CENTER);
        continueButton = new JButton("继续测试");
        cancelButton = new JButton("终止测试");
        createView();
        createActions();
    }

    @Override
    public void createView() {

        this.setLayout(new BorderLayout());
        this.setSize(300, 200);
        this.add(label, BorderLayout.NORTH);
        this.setTitle("操作暂停");
        JPanel buttonPanel = new JPanel();
        buttonPanel.setLayout(new FlowLayout());
        buttonPanel.add(continueButton);
        buttonPanel.add(cancelButton);
        this.add(buttonPanel, BorderLayout.SOUTH);
        this.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        this.setLocationRelativeTo(null); // 居中显示
    }

    @Override
    public void createActions() {
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainModel.getTestScriptEventModel().stopScript();
                dispose();
            }
        });
        continueButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                OperationTargetHolder.getExecutorKit().resumeExecution();
                SwingUtilities.invokeLater(() -> {
                    mainModel.getTestExecuteStatusModel().testResumed();
                    mainModel.getTestScriptEventModel().notifyPauseStatus(true);
                });
                dispose();
            }
        });

    }

}