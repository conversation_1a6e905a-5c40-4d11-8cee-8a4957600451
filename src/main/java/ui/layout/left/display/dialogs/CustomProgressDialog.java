package ui.layout.left.display.dialogs;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.swing.*;
import java.awt.*;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomProgressDialog extends J<PERSON>ialog implements PropertyChangeListener {

    private JProgressBar progressBar;
    private JLabel statusLabel;

    public CustomProgressDialog(Frame owner, String title) {
        super(owner, title, false); // false for non-modal
        initComponents();
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));

        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);

        statusLabel = new JLabel("处理中...");

        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        panel.add(statusLabel, BorderLayout.NORTH);
        panel.add(progressBar, BorderLayout.CENTER);

        add(panel, BorderLayout.CENTER);

        setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
        setSize(300, 100);
        setLocationRelativeTo(getOwner());
    }

    @Override
    public void propertyChange(PropertyChangeEvent evt) {
        if ("progress".equals(evt.getPropertyName())) {
            int progress = (Integer) evt.getNewValue();
            progressBar.setValue(progress);
        }
    }

    public void setStatus(String status) {
        statusLabel.setText(status);
    }
}

