package ui.layout.left.display.dialogs;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import sdk.domain.screen.AutoClickerDataPackage;
import ui.base.OperationAssemblerPanel;
import ui.layout.left.display.components.container.resistance.ResistanceData;

import javax.swing.*;
import java.awt.*;

public class ResistanceConfigDialog extends OperationAssemblerPanel<AutoClickerDataPackage> {


    private final JComboBox<String> channelSettingComboBox;
    private final JSpinner resistanceSpinner;


    public ResistanceConfigDialog() {
        resistanceSpinner = new JSpinner();
        channelSettingComboBox = new JComboBox<>();
        channelSettingComboBox.setModel(new DefaultComboBoxModel<>(new String[]{"通道1", "通道2", "通道3", "通道4"}));
        resistanceSpinner.setModel(new SpinnerNumberModel(1000, 1, 16000, 100));
        JPanel panel = new JPanel(new GridLayout(2, 2, 5, 5));
        panel.add(new JLabel("通道："));
        panel.add(channelSettingComboBox);
        panel.add(new JLabel("阻值:"));
        panel.add(resistanceSpinner);
        add(panel, BorderLayout.CENTER);
    }

    @Override
    public Dimension getPerfectDimension() {
        return new Dimension(400, 200);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        ResistanceData resistanceData = JSON.to(ResistanceData.class, injectedOperation.getOperationObject());
        channelSettingComboBox.setSelectedIndex(resistanceData.getChannel() - 10);
        resistanceSpinner.setValue(resistanceData.getResistance());

    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        ResistanceData resistanceData = JSON.to(ResistanceData.class, injectedOperation.getOperationObject());
        resistanceData.setChannel(channelSettingComboBox.getSelectedIndex() + 10);
        resistanceData.setResistance((Integer) resistanceSpinner.getValue());
        return injectedOperation.setOperationObject(resistanceData);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}
