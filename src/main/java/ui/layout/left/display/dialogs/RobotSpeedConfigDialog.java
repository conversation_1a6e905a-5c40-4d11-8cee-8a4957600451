package ui.layout.left.display.dialogs;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import sdk.domain.CheckPointInfo;
import sdk.domain.robot.SpeedCoordinates;
import ui.base.OperationAssemblerPanel;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;

/**
 * @author: QinHao
 * @description: 机械臂速度调整
 * @date: 2024/8/24 16:12
 */
public class RobotSpeedConfigDialog extends OperationAssemblerPanel<CheckPointInfo> {
    private final JTextField coordinateNameTextField;
    private final JTextField speedTextField;
    private final JTextField acceleratedTextField;
    private final JSlider speedSlider;
    private final JSlider acceleratedSlider;


    public RobotSpeedConfigDialog() {
        coordinateNameTextField = new JTextField();
        speedSlider = new JSlider();
        speedTextField = new JTextField();
        acceleratedSlider = new JSlider();
        acceleratedTextField = new JTextField();
        createView();
        widgetAction();
    }

    private void createView() {
        setBounds(100, 100, 420, 200);
        setLayout(new BorderLayout());
        JPanel contentPanel = new JPanel();
        contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
        add(contentPanel, BorderLayout.CENTER);
        contentPanel.setLayout(new BorderLayout(0, 0));

        JPanel panel = new JPanel();
        contentPanel.add(panel, BorderLayout.NORTH);
        panel.setLayout(new GridLayout(3, 1, 0, 0));

        JPanel panel_coordinate = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.add(panel_coordinate);
        panel_coordinate.add(new JLabel("坐标名"));
        panel_coordinate.add(coordinateNameTextField);


        JPanel panel_1 = new JPanel();
        panel.add(panel_1);
        panel_1.add(new JLabel("速  度"));

        speedSlider.setValue(0);
        panel_1.add(speedSlider);

        panel_1.add(speedTextField);
        speedTextField.setColumns(3);
        panel_1.add(new JLabel("%"));

        JPanel panel_2 = new JPanel();
        panel.add(panel_2);
        panel_2.add(new JLabel("加速度"));

        acceleratedSlider.setValue(0);
        panel_2.add(acceleratedSlider);

        acceleratedTextField.setColumns(3);
        panel_2.add(acceleratedTextField);
        panel_2.add(new JLabel("%"));

        JPanel buttonPane = new JPanel();
        buttonPane.setLayout(new FlowLayout(FlowLayout.CENTER));
        add(buttonPane, BorderLayout.SOUTH);
    }

    private void widgetAction() {
        speedSlider.addChangeListener(e -> {
            int value = speedSlider.getValue();
            speedTextField.setText(String.valueOf(value));
        });
        speedTextField.addActionListener(e -> {
            try {
                int value = Integer.parseInt(speedTextField.getText());
                speedSlider.setValue(value);
            } catch (NumberFormatException ex) {
                speedTextField.setText(String.valueOf(speedSlider.getValue()));
            }
        });
        acceleratedSlider.addChangeListener(e -> {
            int value = acceleratedSlider.getValue();
            acceleratedTextField.setText(String.valueOf(value));
        });
        acceleratedTextField.addActionListener(e -> {
            try {
                int value = Integer.parseInt(acceleratedTextField.getText());
                acceleratedSlider.setValue(value);
            } catch (NumberFormatException ex) {
                acceleratedTextField.setText(String.valueOf(acceleratedSlider.getValue()));
            }
        });

    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        SpeedCoordinates speedCoordinates = JSON.to(SpeedCoordinates.class, injectedOperation.getOperationObject());
        coordinateNameTextField.setText(speedCoordinates.getCoordinateName());
        speedSlider.setValue(speedCoordinates.getSpeed());
        acceleratedSlider.setValue(speedCoordinates.getAcceleration());
        speedTextField.setText(String.valueOf(speedCoordinates.getSpeed()));
        acceleratedTextField.setText(String.valueOf(speedCoordinates.getAcceleration()));
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        SpeedCoordinates speedCoordinates = JSON.to(SpeedCoordinates.class, injectedOperation.getOperationObject());
        speedCoordinates.setCoordinateName(coordinateNameTextField.getText());
        speedCoordinates.setSpeed(speedSlider.getValue());
        speedCoordinates.setAcceleration(acceleratedSlider.getValue());
        return injectedOperation.setOperationObject(speedCoordinates);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }
}
