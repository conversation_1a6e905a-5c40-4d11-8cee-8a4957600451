package ui.layout.left.display.dialogs;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import ui.base.OperationAssemblerPanel;
import ui.layout.left.display.components.container.robot.RobotImagePoint;

import javax.swing.*;
import java.awt.*;

public class RobotAutoRecognitionConfigDialog extends OperationAssemblerPanel<RobotImagePoint> {
    private final JComboBox<String> recognitionAlgorithmComboBox;
    private final JTextField nameTextField;
    private final JSpinner jSpinner;

    public RobotAutoRecognitionConfigDialog() {
        setLayout(new GridLayout(4, 1));
        Panel textFieldPanel = new Panel(new FlowLayout());
        Panel percentPanel = new Panel(new FlowLayout());
        Panel recognitionAlgorithmPanel = new Panel(new FlowLayout());
        String[] options = {"全图搜索","精准匹配","颜色匹配","逐像素匹配"};
        nameTextField = new JTextField();
        recognitionAlgorithmComboBox = new JComboBox<>(options);
        jSpinner = new JSpinner(new SpinnerNumberModel(80, 0, 100, 10));
        recognitionAlgorithmPanel.add(new JLabel("识别算法"));
        recognitionAlgorithmPanel.add(recognitionAlgorithmComboBox);
        percentPanel.add(new JLabel("相似度:"));
        percentPanel.add(jSpinner);
        textFieldPanel.add(new JLabel("坐标名:"));
        textFieldPanel.add(nameTextField);
        add(recognitionAlgorithmPanel);
        add(percentPanel);
        add(textFieldPanel);
    }
    @Override
    public Dimension getPerfectDimension() {
        return new Dimension(200, 200);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        RobotImagePoint robotImagePoint = JSON.to(RobotImagePoint.class,injectedOperation.getOperationObject());
        jSpinner.setValue(robotImagePoint.getSimilarity());
        nameTextField.setText(robotImagePoint.getCoordinateName());
        recognitionAlgorithmComboBox.setSelectedItem(robotImagePoint.getSelectedOption());
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        RobotImagePoint robotImagePoint = JSON.to(RobotImagePoint.class,injectedOperation.getOperationObject());
        robotImagePoint.setSimilarity((Integer) jSpinner.getValue());
        robotImagePoint.setCoordinateName(nameTextField.getText());
        robotImagePoint.setSelectedOption((String) recognitionAlgorithmComboBox.getSelectedItem());
        return injectedOperation.setOperationObject(robotImagePoint);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}
