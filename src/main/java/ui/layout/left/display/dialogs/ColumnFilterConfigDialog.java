package ui.layout.left.display.dialogs;

import excelcase.tree.CheckBoxTreeNode;
import excelcase.tree.ExcelTableConfigTree;
import lombok.extern.slf4j.Slf4j;
import ui.base.BaseView;
import ui.base.table.FilterListener;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import java.awt.*;
import java.util.Enumeration;
import java.util.LinkedHashMap;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/3/27 11:43
 * @description : 表头配置对话框
 * @modified By :
 * @since : 2023/3/27
 **/
@Slf4j
public class ColumnFilterConfigDialog extends JDialog implements BaseView {
    private final Container container;
    private final ExcelTableConfigTree excelTableConfigTree;
    private final JButton saveColumnFilterBtn;
    private String title;
    private FilterListener filterListener;
    private LinkedHashMap<String, Boolean> options;

    public ColumnFilterConfigDialog(String filterColumnName, LinkedHashMap<String, Boolean> options) {
        title = String.format("筛选列： %s", filterColumnName);
        this.options = options;
        excelTableConfigTree = new ExcelTableConfigTree();
        excelTableConfigTree.initFilterConfigTree(options);
        container = this.getContentPane();
        saveColumnFilterBtn = new JButton("确定");
        createView();
        createActions();
        finalSetting();
    }

    public void addFilterListener(FilterListener listener) {
        this.filterListener = listener;
    }

    @Override
    public void createView() {
        setTitle(title);
        setSize(new Dimension(500, 300));
        setLayout(new BorderLayout());
        Box verticalBox = Box.createVerticalBox();
        verticalBox.add(saveColumnFilterBtn);
        JScrollPane scroll = new JScrollPane(excelTableConfigTree);
        container.add(scroll, BorderLayout.CENTER);
        container.add(verticalBox, BorderLayout.SOUTH);
    }

    @Override
    public void createActions() {
        saveColumnFilterBtn.addActionListener(e -> saveColumnFilterConfig());
    }

    private void finalSetting() {
        SwingUtil.centerInScreen(this);
        setModal(true);
        setResizable(false);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    }

    /**
     * 保存筛选设置
     */
    private void saveColumnFilterConfig() {
        CheckBoxTreeNode rootNode = (CheckBoxTreeNode) excelTableConfigTree.getModel().getRoot();
        Enumeration<DefaultMutableTreeNode> en = rootNode.preorderEnumeration();
        while (en.hasMoreElements()) {
            CheckBoxTreeNode node = (CheckBoxTreeNode) en.nextElement();
            if (rootNode != node) {
                options.put(node.getUserObject().toString(), node.isSelected());
            }
        }
        filterListener.filterChanged(options);
        closeDialog();
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        setModal(false);
        dispose();
    }
}
