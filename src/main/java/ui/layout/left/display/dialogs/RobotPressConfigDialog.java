package ui.layout.left.display.dialogs;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import sdk.domain.robot.LongTouchRobotCoordinate;
import sdk.domain.screen.AutoClickerDataPackage;
import ui.base.OperationAssemblerPanel;

import javax.swing.*;
import java.awt.*;

public class RobotPressConfigDialog extends OperationAssemblerPanel<AutoClickerDataPackage> {

    private final JTextField coordinateTextField;
    private final JSpinner holdSpinner;


    public RobotPressConfigDialog() {
        holdSpinner = new JSpinner();
        coordinateTextField = new JTextField();
        holdSpinner.setModel(new SpinnerNumberModel(1000, 1, Integer.MAX_VALUE, 100));
        JPanel panel = new JPanel(new GridLayout(2, 2, 5, 5));
        panel.add(new JLabel("坐标名："));
        panel.add(coordinateTextField);
        panel.add(new JLabel("按下时长（毫秒）:"));
        panel.add(holdSpinner);
        add(panel, BorderLayout.CENTER);
    }

    @Override
    public Dimension getPerfectDimension() {
        return new Dimension(500, 100);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        LongTouchRobotCoordinate longTouchRobotCoordinate = JSON.to(LongTouchRobotCoordinate.class, injectedOperation.getOperationObject());
        coordinateTextField.setText(longTouchRobotCoordinate.getCoordinateName());
        holdSpinner.setValue(longTouchRobotCoordinate.getTime());
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        LongTouchRobotCoordinate longTouchRobotCoordinate = JSON.to(LongTouchRobotCoordinate.class, injectedOperation.getOperationObject());
        longTouchRobotCoordinate.setTime((Integer) holdSpinner.getValue());
        longTouchRobotCoordinate.setCoordinateName(coordinateTextField.getText());
        return injectedOperation.setOperationObject(longTouchRobotCoordinate);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}
