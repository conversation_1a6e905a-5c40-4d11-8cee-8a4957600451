package ui.layout.left.display.dialogs;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.domain.Device;
import sdk.entity.CameraDevice;
import sdk.entity.OperationTargetHolder;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.picture.CameraPictureContainer;
import ui.layout.left.display.components.container.picture.PictureContainer;
import ui.layout.left.display.components.container.picture.RoiRect;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * 设置校准ROI对话框
 */
@Slf4j
public class CalibrationRoiSettingDialog extends ConfirmDialog {

    @Getter
    private PictureContainer pictureContainer;

    @Getter
    private RoiRect roi;

    private final JButton clearRoiButton;

    private boolean deviceLoaded;

    private static volatile CalibrationRoiSettingDialog calibrationRoiSettingDialog;

    private static class CalibrationPictureContainer extends CameraPictureContainer {

        public CalibrationPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
            super(clientView, mainModel, device, true);
            setVisionLocationControllerEnabled(false);
            setPlayOrPauseButtonEnabled(false);
        }

        @Override
        public void createMenu() {
            //重写，去除菜单
        }

    }

    public static CalibrationRoiSettingDialog getInstance(JComponent parentComponent,
                                                          String title,
                                                          ClientView clientView,
                                                          MainModel mainModel,
                                                          Device device,
                                                          RoiRect coordinatesRoi) {
        if (calibrationRoiSettingDialog == null) {
            synchronized (CalibrationRoiSettingDialog.class) {
                if (calibrationRoiSettingDialog == null) {
                    calibrationRoiSettingDialog = new CalibrationRoiSettingDialog(parentComponent, title, clientView, mainModel, device, coordinatesRoi);
                }
            }
        }
        calibrationRoiSettingDialog.getButtonConfirm().setEnabled(device != null);
        if (!calibrationRoiSettingDialog.deviceLoaded) {
            calibrationRoiSettingDialog.loadDevice(clientView, mainModel, device, coordinatesRoi);
            PictureContainer container = calibrationRoiSettingDialog.getPictureContainer();
            if (container != null) {
                if (coordinatesRoi != null) {
                    container.getDrawingBoard().setRoi(coordinatesRoi, true);
                }
            }
        }
        return calibrationRoiSettingDialog;
    }

    public void setRoi(RoiRect roiRect) {
        if (roiRect != null) {
//            System.out.println("roiRect:" + roiRect);
            calibrationRoiSettingDialog.getPictureContainer().getDrawingBoard().setRoi(roiRect, true);
        }
    }

    public CalibrationRoiSettingDialog(JComponent parentComponent,
                                       String title,
                                       ClientView clientView,
                                       MainModel mainModel,
                                       Device device,
                                       RoiRect coordinatesRoi) {
        super(parentComponent, title);
        clearRoiButton = new JButton("清除ROI");
        deviceLoaded = false;
        loadDevice(clientView, mainModel, device, coordinatesRoi);
        createView();
        createActions();
    }

    /**
     * 加载设备
     *
     * @param clientView     ClientView
     * @param mainModel      MainModel
     * @param device         设备
     * @param coordinatesRoi 坐标ROI
     */
    private void loadDevice(ClientView clientView, MainModel mainModel, Device device, RoiRect coordinatesRoi) {
        if (device != null) {
            CameraDevice cameraHttpClient = OperationTargetHolder.getCameraDeviceManager();
            JsonResponse<Device> response = cameraHttpClient.registerAndOpenDevice(device);
            if (response.isOk()) {
                pictureContainer = new CalibrationPictureContainer(clientView, mainModel, device);
                pictureContainer.grab();
                deviceLoaded = true;
                add(pictureContainer, BorderLayout.CENTER);
                pictureContainer.addGrabCallback(new PictureContainer.GrabCallback() {
                    @Override
                    public void grabStarted() {

                    }

                    @Override
                    public void grabCompleted() {
                        if (coordinatesRoi != null) {
                            pictureContainer.getDrawingBoard().setRoi(coordinatesRoi, false);
                        }
                    }
                });

            } else {
                dispose();
                SwingUtil.showWebMessageDialog(this, response.getMessage());
            }
        }
    }

    @Override
    public String okText() {
        return "设置";
    }

    @Override
    public void createView() {
        super.createView();
        setSize(960, 720);
        addBottomComponent(clearRoiButton);
        SwingUtil.centerInScreen(this, false);
    }

    @Override
    public void createActions() {
        super.createActions();
        clearRoiButton.addActionListener(e -> {
            setConfirmed(true);
            roi = null;
            dispose();
        });
    }

    @Override
    public void confirm() {
        if (pictureContainer.getDrawingBoard().isAssertRoiTargeted()) {
            roi = pictureContainer.getDrawingBoard().getRoiRect();
            super.confirm();
        } else {
            SwingUtil.showWarningDialog(this, "请画框选择机械臂点击后一定会变化的区域");
        }
    }


    @Override
    public JPanel makeCenterPanel() {
        return pictureContainer;
    }
}
