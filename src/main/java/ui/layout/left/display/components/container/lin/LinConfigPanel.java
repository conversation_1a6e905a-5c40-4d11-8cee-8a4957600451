package ui.layout.left.display.components.container.lin;

import net.miginfocom.swing.MigLayout;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.bus.CanPtsComparison;
import sdk.domain.bus.LinConfig;
import sdk.entity.LinDevice;
import ui.base.BaseView;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;

public class LinConfigPanel extends JPanel implements BaseView {
    private final LinConfig linConfig;
    private JTextField sendMessageIdTextField;
    private JTextField sendMessageTextField;
    private JTextField receiveMessageIdTextField;
    private JTextField receiveMessageTextField;
    private JButton addToScriptButton;
    private final LinDevice linDevice;
    private final MainModel mainModel;

    public LinConfigPanel(MainModel mainModel, LinDevice linDevice, LinConfig linConfig) {
        this.linConfig = linConfig;
        this.linDevice = linDevice;
        this.mainModel = mainModel;
        createView();
        restoreView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());

        // 修改布局约束，不自动换行
        MigLayout migLayout = new MigLayout("",
                "[][grow]",
                "[]10[]10[]"); // 行间距10
        JPanel ptsPanel = new JPanel(migLayout);
        ptsPanel.setBorder(BorderFactory.createTitledBorder("PTS设置"));

        // 组件定义
        JLabel sendMessageIdLabel = new JLabel("发送报文ID:");
        sendMessageIdTextField = new JTextField(5); // 减小ID输入框的宽度
        sendMessageTextField = new JTextField(25);
        JLabel receiveMessageIdLabel = new JLabel("接收报文ID:");
        receiveMessageIdTextField = new JTextField(5); // 减小ID输入框的宽度
        JLabel receiveMessageLabel = new JLabel("接收报文:");
        receiveMessageTextField = new JTextField(25);
        addToScriptButton = SwingUtil.addNewScriptButton("添加到脚本步骤");
        SwingUtil.changeDocumentFilter(receiveMessageTextField, true);

        // 发送报文ID和发送报文在同一行
        JPanel sendPanel = new JPanel(new MigLayout("insets 0"));
        sendPanel.add(sendMessageIdLabel);
        sendPanel.add(sendMessageIdTextField);
        sendPanel.add(new JLabel("发送报文:"));
        sendPanel.add(sendMessageTextField, "growx");
        ptsPanel.add(sendPanel, "span 2, growx, wrap");

        // 接收报文ID和接收报文在同一行
        JPanel receivePanel = new JPanel(new MigLayout("insets 0"));
        receivePanel.add(receiveMessageIdLabel);
        receivePanel.add(receiveMessageIdTextField);
        receivePanel.add(receiveMessageLabel);
        receivePanel.add(receiveMessageTextField, "growx");
        ptsPanel.add(receivePanel, "span 2, growx, wrap");

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.add(addToScriptButton);
        ptsPanel.add(buttonPanel, "span 2, center");

        add(ptsPanel, BorderLayout.CENTER);
    }

    @Override
    public void restoreView() {
        sendMessageIdTextField.setText(linConfig.getPtsConfig().getSendPtsMessageId());
        receiveMessageIdTextField.setText(linConfig.getPtsConfig().getReceivePtsMessageId());
    }

    @Override
    public void createActions() {
        sendMessageIdTextField.addActionListener(e -> updateSendPtsMessage());
        sendMessageIdTextField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateSendPtsMessage();
            }
        });
        receiveMessageIdTextField.addActionListener(e -> updateReceivePtsMessage());
        receiveMessageIdTextField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateReceivePtsMessage();
            }
        });
        addToScriptButton.addActionListener(e -> addToScript());
    }

    private void updateReceivePtsMessage() {
        linConfig.getPtsConfig().setReceivePtsMessageId(receiveMessageIdTextField.getText().trim());
        linDevice.updateConfig(linConfig);
    }

    private void updateSendPtsMessage() {
        linConfig.getPtsConfig().setSendPtsMessageId(sendMessageIdTextField.getText().trim());
        linDevice.updateConfig(linConfig);
    }

    private void addToScript() {
        String sendPtsMessageId = sendMessageIdTextField.getText().trim();
        String sendPtsMessage = sendMessageTextField.getText().trim();
        String receivePtsMessageId = receiveMessageIdTextField.getText().trim();
        String receivePtsMessage = receiveMessageTextField.getText().trim();
        if (receivePtsMessage.isEmpty() || receivePtsMessageId.isEmpty()) {
            SwingUtil.showWarningDialog(this, "请填写接收报文ID或接收报文内容");
            return;
        }
        Operation operation = Operation.buildOperation(linDevice);
        operation.getOperationTarget().setChannel(1);
        operation.setOperationMethod(DeviceMethods.comparisonCanMessage);
        CanPtsComparison canComparison = new CanPtsComparison();
        canComparison.setSendMessageId(sendPtsMessageId);
        canComparison.setSendMessage(sendPtsMessage);
        canComparison.setComparisonMessageId(receivePtsMessageId);
        canComparison.setComparisonMessage(receivePtsMessage);
        operation.setOperationObject(canComparison);
        mainModel.getOperationModel().updateOperation(operation);
    }

}
