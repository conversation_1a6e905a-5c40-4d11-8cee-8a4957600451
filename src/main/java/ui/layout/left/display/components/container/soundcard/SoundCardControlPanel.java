package ui.layout.left.display.components.container.soundcard;

import lombok.extern.slf4j.Slf4j;
import net.miginfocom.swing.MigLayout;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.SoundMonitor;
import sdk.entity.SoundDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.layout.left.display.components.toolkit.monitor.MonitorParameterConfig;
import ui.layout.left.display.components.toolkit.monitor.VolumeMonitorPanel;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.commons.SoundMonitorPanel;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.math.BigDecimal;

/**
 * <AUTHOR> lpf
 * @date : Created in 2024-10-15
 * @description :
 * @modified By :
 * @since : 2024-10-15
 */
@Slf4j
public class SoundCardControlPanel extends DeviceControlPanel implements BaseView {
    private final VolumeMonitorPanel volumeMonitorPanel;
    private JButton beginSoundMonitorButton;
    private JButton endSoundMonitorButton;
    private JButton soundCaptureButton;
    private JButton calibrateButton;
    private final Device device;
    private final MainModel mainModel;
    private JLabel soundCaptureResultLabel;
    private JLabel calibrateResultLabel;
    private JTextField referenceVoltageField;
    private JButton setReferenceButton;

    public SoundCardControlPanel(DeviceContainer deviceContainer, MainModel mainModel) {
        super(deviceContainer, mainModel);
        this.mainModel = mainModel;
        device = deviceContainer.getDevice();
        volumeMonitorPanel = new VolumeMonitorPanel(mainModel, (SoundDevice) device, new MonitorParameterConfig() {
            @Override
            public Integer getChannel() {
                return SoundCardControlPanel.this.getChannel();
            }

            @Override
            public Integer getMaxValue() {
                return 1000;
            }
        });
        volumeMonitorPanel.setVol(true);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(1, 1));
        JPanel centerPanel = new JPanel(new MigLayout("wrap", "[grow,fill]"));

        // 创建监控按钮面板，并添加标题边框
        JPanel monitoringPanel = new JPanel();
        monitoringPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(),
                "过程监控",
                TitledBorder.LEFT,
                TitledBorder.TOP
        ));

        // 使用FlowLayout放置按钮
        monitoringPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        beginSoundMonitorButton = new JButton("开始声音监控");
        endSoundMonitorButton = new JButton("结束声音监控");
        soundCaptureButton = new JButton("采集当前声音");
        calibrateButton = new JButton("校准");
        soundCaptureResultLabel = new JLabel("");
        calibrateResultLabel = new JLabel("");
        
        monitoringPanel.add(beginSoundMonitorButton);
        monitoringPanel.add(endSoundMonitorButton);
        monitoringPanel.add(soundCaptureButton);
        monitoringPanel.add(soundCaptureResultLabel);
        
        if (device.getDeviceModel().equals(DeviceModel.SoundCard.USB4704_DEVICE)) {
            // 添加校准相关组件
            monitoringPanel.add(calibrateButton);
            monitoringPanel.add(calibrateResultLabel);
            
            // 添加设置参考电压的输入框和按钮
            JPanel referencePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
            referencePanel.add(new JLabel("参考电压(V):"));
            referenceVoltageField = new JTextField(5);
            setReferenceButton = new JButton("设置");
            referencePanel.add(referenceVoltageField);
            referencePanel.add(setReferenceButton);
            monitoringPanel.add(referencePanel);
        }

        // 添加到布局
        centerPanel.add(monitoringPanel);
        centerPanel.add(volumeMonitorPanel);

        add(centerPanel);
    }

    @Override
    public void createActions() {
        // 开始声音监控按钮事件处理
        beginSoundMonitorButton.addActionListener((ActionEvent e) -> {
            addBeginMonitorToScript();
        });

        // 结束声音监控按钮事件处理
        endSoundMonitorButton.addActionListener((ActionEvent e) -> {
            JDialog dialog = SoundMonitorPanel.getInstance().toDialog(operationObject -> {
                SoundMonitor soundMonitor = (SoundMonitor) operationObject;
                Operation operation = Operation.buildOperation(device);
                operation.setOperationObject(soundMonitor);
                operation.setOperationMethod(DeviceMethods.endSoundMonitor);
                mainModel.getOperationModel().updateOperation(operation);
            });
            dialog.setVisible(true);
        });

        soundCaptureButton.addActionListener((ActionEvent e) -> {
            OperationResult result = device.callOperationMethod(DeviceMethods.soundCapture);
            String timestamp = new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());
            if (result.isOk()) {
                soundCaptureResultLabel.setText(timestamp + " 音量: " + String.format("%.1f",((BigDecimal) result.getData()).floatValue()) + "dB");
            } else {
                log.error(result.getMessage());
                soundCaptureResultLabel.setText(timestamp + " 采集失败,详情见后台");
            }
        });

        if (device.getDeviceModel().equals(DeviceModel.SoundCard.USB4704_DEVICE)) {
            calibrateButton.addActionListener((ActionEvent e) -> {
                OperationResult result = device.callOperationMethod(DeviceMethods.calibrateReferenceValue);
                String timestamp = new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());
                if (result.isOk()) {
                    calibrateResultLabel.setText(timestamp + " 校准成功: " + String.format("%.2f", ((BigDecimal) result.getData()).floatValue()) + "V");
                } else {
                    log.error(result.getMessage());
                    calibrateResultLabel.setText(timestamp + " 校准失败,详情见后台");
                }
            });

            // 设置参考电压按钮事件处理
            setReferenceButton.addActionListener((ActionEvent e) -> {
                String voltageText = referenceVoltageField.getText().trim();
                String timestamp = new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());

                try {
                    float voltage = Float.parseFloat(voltageText);
                    // 调用设置参考电压的方法
                    OperationResult result = device.callOperationMethod(DeviceMethods.setReferenceValue, voltage);

                    if (result.isOk()) {
                        calibrateResultLabel.setText(timestamp + " 设置参考电压成功: " + String.format("%.2f",voltage) + "V");
                    } else {
                        log.error(result.getMessage());
                        calibrateResultLabel.setText(timestamp + " 设置参考电压失败,详情见后台");
                    }
                } catch (NumberFormatException ex) {
                    calibrateResultLabel.setText(timestamp + " 错误: 请输入有效数字");
                }
            });
        }

    }

    /**
     * 将开始声音监控添加到脚本
     */
    private void addBeginMonitorToScript() {
        Operation operation = Operation.buildOperation(device);
        operation.setOperationMethod(DeviceMethods.beginSoundMonitor);
        // 开始监控可以不需要额外参数
        mainModel.getOperationModel().updateOperation(operation);
    }
}
