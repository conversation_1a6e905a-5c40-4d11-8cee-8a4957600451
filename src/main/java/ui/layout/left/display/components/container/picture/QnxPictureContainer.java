package ui.layout.left.display.components.container.picture;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.*;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.domain.Device;
import sdk.entity.QnxDevice;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Collections;
import java.util.List;

@Slf4j
public class QnxPictureContainer extends PictureContainer implements AppObserver {
    private JTextField qnxPathTextField;
    private JButton qnxImportButton;
    private JButton qnxLoadButton;
    private JButton closeStreamButton; // New button
    private final static String QNX_CONFIG_KEY = "qnxScriptPath";
    private SwingWorker<Void, Void> qnxCommandWorker;
    private SwingWorker<Void, BufferedImage> rtmpStreamWorker;
    private final Java2DFrameConverter converter = new Java2DFrameConverter(); // Can be member
    private Timer refreshStreamTimer;
    // 添加一个暂停标志
    private volatile boolean isPaused = false;
    private final Object pauseLock = new Object();
    private volatile boolean cleanupDone = false;
    private final Object retryLock = new Object();
    private volatile boolean isRetrying = false;
    private volatile int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private Timer retryTimer;

    public QnxPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public QnxPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }

    @Override
    public void createView() {
        // 移除 screenShotButton 初始化
        super.createView();
        // 启用播放/暂停按钮
        setPlayOrPauseButtonVisible(true);
        // 直接将播放/暂停按钮添加到工具箱
        Box toolBox = getToolBox();
        toolBox.removeAll(); // 清除所有组件
        toolBox.add(Box.createHorizontalGlue());
        // 如果PictureContainer有getPlayOrPauseButton方法
        if (getPlayOrPauseButton() != null) {
            toolBox.add(getPlayOrPauseButton());
        }
        toolBox.add(Box.createHorizontalGlue());

        // 确保工具箱可见
        getToolBox().setVisible(true);

        // 创建QNX配置面板
        JPanel qnxConfigPanel = new JPanel(new BorderLayout());
        JPanel innerPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel qnxConfigLabel = new JLabel("QNX配置:");
        qnxPathTextField = new JTextField(70);
        qnxImportButton = new JButton("导入QNX脚本路径");
        qnxLoadButton = new JButton("加载视频流");
        closeStreamButton = new JButton("关闭视频流"); // Initialize new button
        closeStreamButton.setEnabled(false); // Initially disabled

        innerPanel.add(qnxConfigLabel);
        innerPanel.add(qnxPathTextField);
        innerPanel.add(qnxImportButton);
        innerPanel.add(qnxLoadButton);
        innerPanel.add(closeStreamButton); // Add new button to panel

        qnxConfigPanel.add(innerPanel, BorderLayout.CENTER);

        // 创建主面板
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);

        // 设置布局并添加组件
        setLayout(new BorderLayout());
        add(qnxConfigPanel, BorderLayout.NORTH);
        add(panel, BorderLayout.CENTER);

        String qnxFilePath = getDevice().loadConfigByKey(getMainModel().getAppInfo().getProject(), QNX_CONFIG_KEY);
        qnxPathTextField.setText(qnxFilePath);

        // 初始化监控
        setupStreamMonitoring();
    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new PictureRectDrawLabel(getMainModel(), this);//改为相机的画框
    }

    @Override
    protected List<JLabel> toolButtonList() {
        // 获取父类的播放/暂停按钮
        JLabel playOrPauseButton = getPlayOrPauseButton();
        if (playOrPauseButton != null) {
            return Collections.singletonList(playOrPauseButton);
        }
        return Collections.emptyList();
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    // 移除 screenShot 和 initScreenshotTasks 方法，因为不再需要截图功能

    @Override
    public void createActions() {
        super.createActions();
        // 移除 screenShotButton 相关的监听器代码

        // 添加导入按钮的点击事件
        qnxImportButton.addActionListener(e -> {
            // 弹出文件选择对话框
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setDialogTitle("选择QNX配置文件");
            fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);

            // 设置默认目录
            if (qnxPathTextField.getText() != null && !qnxPathTextField.getText().trim().isEmpty()) {
                File currentDir = new File(qnxPathTextField.getText()).getParentFile();
                if (currentDir != null && currentDir.exists()) {
                    fileChooser.setCurrentDirectory(currentDir);
                }
            }

            int result = fileChooser.showOpenDialog(QnxPictureContainer.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                File selectedFile = fileChooser.getSelectedFile();
                String filePath = selectedFile.getAbsolutePath();
                qnxPathTextField.setText(filePath);
                getDevice().updateConfigByKey(getMainModel().getAppInfo().getProject(), QNX_CONFIG_KEY, qnxPathTextField.getText().trim());
            }
        });

        // 添加加载按钮的点击事件
        qnxLoadButton.addActionListener(e -> {
            String filePath = qnxPathTextField.getText();
            if (filePath != null && !filePath.trim().isEmpty()) {
                boolean wasStreaming = false;
                if (qnxCommandWorker != null && !qnxCommandWorker.isDone()) {
                    qnxCommandWorker.cancel(true);
                }
                if (rtmpStreamWorker != null && !rtmpStreamWorker.isDone()) {
                    rtmpStreamWorker.cancel(true);
                    wasStreaming = true; // Mark that we were actively streaming
                }

                // If we were streaming, introduce a short delay before restarting
                if (wasStreaming) {
                    Timer timer = new Timer(500, ae -> startQnxCommandWorker(filePath)); // 500ms delay
                    timer.setRepeats(false);
                    timer.start();
                } else {
                    startQnxCommandWorker(filePath);
                }
            }
        });

        // 添加关闭视频流按钮的点击事件
        closeStreamButton.addActionListener(e -> {
            // 禁用按钮，防止重复点击
            closeStreamButton.setEnabled(false);
            qnxLoadButton.setEnabled(false);

            // 先显示正在关闭的提示
            if (getPicturePanel() != null) {
                getPicturePanel().setText("正在关闭视频流，请稍候...");
            }

            // 使用单独线程安全地取消RTMP流
            new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() throws Exception {
                    try {
                        if (rtmpStreamWorker != null && !rtmpStreamWorker.isDone()) {
                            log.info("用户点击关闭视频流按钮，取消RTMP流。");
                            rtmpStreamWorker.cancel(true);

                            // 等待一段时间确保取消操作完成
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                        return null;
                    } catch (Exception ex) {
                        log.error("取消RTMP流时出错", ex);
                        return null;
                    }
                }

                @Override
                protected void done() {
                    // 延迟重新启用按钮
                    Timer timer = new Timer(1500, event -> {
                        reEnableLoadButtonsAndDisableClose();
                        if (getPicturePanel() != null) {
                            getPicturePanel().clearText();
                        }
                    });
                    timer.setRepeats(false);
                    timer.start();
                }
            }.execute();

            // 使用单独的线程停止设备流，避免UI阻塞
            new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() throws Exception {
                    try {
                        ((QnxDevice) getDevice()).stopStream();
                        return null;
                    } catch (Exception ex) {
                        log.error("停止设备流时出错", ex);
                        return null;
                    }
                }
            }.execute();
        });
    }

    private void startQnxCommandWorker(String filePath) {
        qnxImportButton.setEnabled(false);
        qnxLoadButton.setEnabled(false);
        qnxLoadButton.setText("执行QNX...");

        qnxCommandWorker = new SwingWorker<Void, Void>() {
            private boolean qnxCommandSuccess = false;

            @Override
            protected Void doInBackground() throws Exception {
                if (getPicturePanel() != null && !isCancelled()) {
                    // 检查是否是重新加载，如果grabber不为null则是重新加载
                    getPicturePanel().setText("正在执行QNX命令");
                }
                ((QnxDevice) getDevice()).startStream(filePath);
                log.info("QNX命令执行成功: {}", filePath);
                Thread.sleep(1500);
                qnxCommandSuccess = true;
                return null;
            }

            @Override
            protected void done() {
                try {
                    if (isCancelled()) {
                        log.info("QNX命令操作被取消。");
                        reEnableLoadButtonsAndDisableClose();
                        return;
                    }
                    get(); // Check for exceptions from doInBackground

                    if (qnxCommandSuccess) {
                        log.info("加载视频流...");
                        qnxLoadButton.setText("加载视频流...");
                        closeStreamButton.setEnabled(false); // Enable close button before starting RTMP
                        startRtmpStream(); // Start the RTMP stream worker
                    } else {
                        // This case should ideally be caught by get() throwing an exception
                        log.warn("QNX命令未标记为成功，但未抛出异常。");
                        reEnableLoadButtonsAndDisableClose();
                    }
                } catch (InterruptedException ex) {
                    log.warn("QNX命令操作被中断。", ex);
                    Thread.currentThread().interrupt();
                    reEnableLoadButtonsAndDisableClose();
                } catch (java.util.concurrent.ExecutionException ex) {
                    log.error("QNX命令操作执行期间发生错误。", ex.getCause());
                    SwingUtilities.invokeLater(() -> SwingUtil.showWarningDialog(QnxPictureContainer.this, "QNX命令失败: " + ex.getCause().getMessage()));
                    reEnableLoadButtonsAndDisableClose();
                } catch (Exception ex) {
                    log.error("QNX命令 'done' 方法中发生意外错误。", ex);
                    reEnableLoadButtonsAndDisableClose();
                }
            }
        };
        qnxCommandWorker.execute();
    }


    private void reEnableLoadButtonsAndDisableClose() {
        SwingUtilities.invokeLater(() -> {
            qnxImportButton.setEnabled(true);
            qnxLoadButton.setEnabled(true);
            qnxLoadButton.setText("加载");
            closeStreamButton.setEnabled(false);
        });
    }

    private void startRtmpStream() {
        if (rtmpStreamWorker != null && !rtmpStreamWorker.isDone()) {
            rtmpStreamWorker.cancel(true);
            // 添加短暂延迟，确保之前的worker有时间清理资源
            try {
                Thread.sleep(500); // 增加延迟时间，确保资源完全释放
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        QnxDevice device = (QnxDevice) getDevice();
        if (device == null) {
            log.error("设备为空，无法启动RTMP流");
            return;
        }

        String rtmpUrl = device.getDeviceName();
        if (rtmpUrl == null || rtmpUrl.isEmpty()) {
            log.error("RTMP URL为空，无法启动流");
            return;
        }

        rtmpStreamWorker = new SwingWorker<Void, BufferedImage>() {
            private FFmpegFrameGrabber localGrabber; // Worker-specific grabber
            private volatile boolean cleanupDone = false; // 标记资源是否已清理
            private final Java2DFrameConverter localConverter = new Java2DFrameConverter(); // 每个worker使用自己的转换器

            @Override
            protected Void doInBackground() throws Exception {
                log.info("尝试从 {} 连接并抓取RTMP流", rtmpUrl);
                SwingUtilities.invokeLater(() -> {
                    if (getPicturePanel() != null && !isCancelled()) {
                        // 检查是否是重新加载，如果grabber不为null则是重新加载
                        getPicturePanel().setText("正在连接RTMP流: " + rtmpUrl + "，请勿关闭视频流");
                    }
                });

                try {
                    // Removed defensive cleanup of QnxPictureContainer.this.grabber as it's no longer the pattern.
                    // Each worker manages its own localGrabber.

                    // Always create a new grabber instance
                    FFmpegLogCallback.set();
                    log.info("创建新的 FFmpegFrameGrabber 实例 for {}", rtmpUrl);
                    this.localGrabber = new FFmpegFrameGrabber(rtmpUrl);
                    this.localGrabber.setOption("rw_timeout", "20000000"); // 30 seconds
                    this.localGrabber.setOption("stimeout", "20000000");   // Socket timeout
                    this.localGrabber.setOption("rtmp_live", "live");      // Specify live stream

                    if (isCancelled()) {
                        log.info("RTMP grabber 操作在启动前被取消: {}", rtmpUrl);
                        if (this.localGrabber != null) {
                            try {
                                this.localGrabber.stop(); // Ensure stop before release if not automatic
                                this.localGrabber.release();
                            } catch (FrameGrabber.Exception e) {
                                log.warn("释放在启动前被取消的新 localGrabber 时出错: {}", e.getMessage());
                            }
                            this.localGrabber = null;
                        }
                        return null;
                    }

                    this.localGrabber.start(); // Always start a new instance

                    SwingUtilities.invokeLater(() -> closeStreamButton.setEnabled(true));
                    log.info("RTMP localGrabber.start() 调用完成。");


                    SwingUtilities.invokeLater(() -> {
                        if (getPicturePanel() != null && !isCancelled()) {
                            getPicturePanel().clearText();
                        }
                    });

                    // 添加帧计数器和最后成功帧时间戳
                    int emptyFrameCount = 0;
                    long lastSuccessFrameTime = System.currentTimeMillis();

                    Frame capturedFrame;
                    while (!isCancelled() && (capturedFrame = this.localGrabber.grabImage()) != null) {
                        // 检查是否暂停
                        checkPaused();

                        if (capturedFrame.image != null) {
                            BufferedImage bufferedImage = localConverter.convert(capturedFrame);
                            if (bufferedImage != null) {
                                publish(bufferedImage);
                                // 重置空帧计数和更新最后成功帧时间
                                emptyFrameCount = 0;
                                lastSuccessFrameTime = System.currentTimeMillis();
                            } else {
                                emptyFrameCount++;
                                log.debug("转换后的BufferedImage为null，空帧计数: {}", emptyFrameCount);
                            }
                        } else {
                            emptyFrameCount++;
                            log.debug("抓取的Frame.image为null，空帧计数: {}", emptyFrameCount);
                        }

                        // 检查是否连续出现太多空帧或者长时间没有成功帧
                        if (emptyFrameCount > 30 || (System.currentTimeMillis() - lastSuccessFrameTime > 10000)) {
                            log.warn("检测到视频流异常: 连续空帧数={}, 最后成功帧时间={}",
                                    emptyFrameCount, (System.currentTimeMillis() - lastSuccessFrameTime));
                            throw new Exception("视频流异常: 连续空帧过多或长时间无有效帧");
                        }

                        Thread.sleep(50);
                    }

                    if (!isCancelled()) {
                        log.info("RTMP流正常结束: {}", rtmpUrl);
                        SwingUtilities.invokeLater(() -> {
                            if (getPicturePanel() != null) {
                                getPicturePanel().setText("RTMP流已结束。");
                            }
                        });
                    }
                } catch (FrameGrabber.Exception ex) {
                    if (isCancelled()) {
                        log.info("RTMP流操作被取消 (URL: {}): {}", rtmpUrl, ex.getMessage());
                    } else {
                        log.error("RTMP流错误 (URL: {}), 可能由于超时或连接失败: {}", rtmpUrl, ex.getMessage(), ex);
                        final String errorMessage = "无法连接或处理RTMP流: " + ex.getMessage();
                        SwingUtilities.invokeLater(() -> {
                            if (getPicturePanel() != null) getPicturePanel().setText(errorMessage);
                            if (!isCancelled()) SwingUtil.showWarningDialog(QnxPictureContainer.this, errorMessage);
                        });

                        // 标记需要重试，但不在这里直接重试，让外部监控机制处理
                        synchronized (retryLock) {
                            if (retryCount < MAX_RETRY_COUNT && !isRetrying) {
                                // 设置标志，表示需要重试
                                QnxPictureContainer.this.rtmpStreamWorker = null; // 清除引用，帮助GC
                            }
                        }
                    }
                    throw ex; // Re-throw FrameGrabber.Exception
                } catch (InterruptedException ex) {
                    log.warn("RTMP流处理被中断 (URL: {}): {}", rtmpUrl, ex.getMessage());
                    Thread.currentThread().interrupt();
                    throw ex; // Re-throw
                } catch (Exception ex) {
                    if (isCancelled()) {
                        log.info("RTMP流处理在取消过程中发生异常 (URL: {}): {}", rtmpUrl, ex.getMessage());
                    } else {
                        log.error("处理RTMP流时发生其他意外错误 (URL: {}): {}", rtmpUrl, ex.getMessage(), ex);
                        final String errorMessage = "处理流时发生意外错误: " + ex.getMessage();
                        SwingUtilities.invokeLater(() -> {
                            if (getPicturePanel() != null) getPicturePanel().setText(errorMessage);
                            SwingUtil.showWarningDialog(QnxPictureContainer.this, errorMessage);
                        });

                        // 标记需要重试，但不在这里直接重试，让外部监控机制处理
                        synchronized (retryLock) {
                            if (retryCount < MAX_RETRY_COUNT && !isRetrying) {
                                // 设置标志，表示需要重试
                                QnxPictureContainer.this.rtmpStreamWorker = null; // 清除引用，帮助GC
                            }
                        }
                    }
                    throw ex; // Re-throw
                } finally {
                    log.info("RTMP worker doInBackground finally block for {}.", rtmpUrl);
                    // 安全地停止grabber
                    cleanupResources();
                }
                return null;
            }

            // 添加安全的资源清理方法
            private synchronized void cleanupResources() {
                if (!cleanupDone && this.localGrabber != null) {
                    cleanupDone = true;
                    try {
                        log.info("RTMP worker: 安全停止 localGrabber实例 for {}.", rtmpUrl);

                        // 先停止接收新帧
                        this.localGrabber.stop();

                        // 添加短暂延迟，确保所有处理中的帧都完成
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }

                        // 在释放grabber前先清理转换器资源
                        if (localConverter != null) {
                            // 强制清理转换器中可能存在的缓存
                            localConverter.close(); // 如果Java2DFrameConverter有close方法
                            System.gc();
                        }

                        // 最后释放grabber
                        this.localGrabber.release();
                        this.localGrabber = null;
                    } catch (FrameGrabber.Exception e) {
                        log.warn("停止RTMP localGrabber for {} 时出错: {}", rtmpUrl, e.getMessage());
                    } finally {
                        this.localGrabber = null;
                    }
                }
            }

            // 添加暂停检查方法
            private void checkPaused() throws InterruptedException {
                synchronized (pauseLock) {
                    while (isPaused && !isCancelled()) {
                        try {
                            log.debug("视频流处于暂停状态，等待恢复信号...");
                            pauseLock.wait(1000); // 等待1秒后重新检查

                            // 检查线程是否被中断
                            if (Thread.currentThread().isInterrupted()) {
                                Thread.currentThread().interrupt();
                                throw new InterruptedException("Thread was interrupted during pause");
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.debug("checkPaused方法被中断");
                            throw e;
                        }
                    }
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (getPicturePanel() != null && !chunks.isEmpty() && !isCancelled()) {
                    BufferedImage latestImage = chunks.get(chunks.size() - 1);
                    if (latestImage != null) {
                        getPicturePanel().setImageStream(latestImage);
                    }
                }
            }

            @Override
            protected void done() {
                String rtmpUrlForLog = "";
                if (getDevice() instanceof QnxDevice) {
                    rtmpUrlForLog = getDevice().getDeviceName();
                }

                boolean wasCancelled = isCancelled();
                log.info("RTMP流操作 (URL: {}) {}", rtmpUrlForLog, wasCancelled ? "被取消" : "正常完成");

                // 确保资源被清理，但避免重复清理
                cleanupResources();

                // 添加额外的内存清理
                System.gc();

                // 延迟一段时间后再启用按钮，确保资源完全释放
                Timer timer = new Timer(1000, e -> {
                    SwingUtilities.invokeLater(() -> {
                        reEnableLoadButtonsAndDisableClose();
                    });
                });
                timer.setRepeats(false);
                timer.start();
            }
        };
        rtmpStreamWorker.execute();

        // 启动流监控
        setupStreamMonitoring();
    }

    // Consider adding a method to clean up the grabber when the container is no longer needed
    public void disposeResources() {
        log.info("Disposing QnxPictureContainer resources.");

        // 停止监控定时器
        if (retryTimer != null) {
            retryTimer.stop();
            retryTimer = null;
        }

        // 先停止定时器
        if (refreshStreamTimer != null) {
            refreshStreamTimer.stop();
            refreshStreamTimer = null;
        }

        // 取消命令工作线程
        if (qnxCommandWorker != null && !qnxCommandWorker.isDone()) {
            qnxCommandWorker.cancel(true);
            qnxCommandWorker = null;
        }

        // 安全地取消RTMP流工作线程
        if (rtmpStreamWorker != null && !rtmpStreamWorker.isDone()) {
            rtmpStreamWorker.cancel(true);

            // 等待一段时间确保资源释放
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            rtmpStreamWorker = null;
        }

        // 强制进行垃圾回收
        System.gc();

        // 重置重试状态
        synchronized (retryLock) {
            isRetrying = false;
            retryCount = 0;
        }
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    @Override
    public void grab() {
        log.info("QnxPictureContainer.grab() 被调用，尝试启动视频流");
        String qnxFilePath = qnxPathTextField.getText();
        if (qnxFilePath != null && !qnxFilePath.trim().isEmpty()) {
            startQnxCommandWorker(qnxFilePath);
        } else {
            log.warn("grab() 被调用，但没有配置QNX脚本路径");
            SwingUtil.showWarningDialog(this, "无法启动视频流：未配置QNX脚本路径");
        }
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        super.deviceConnected(device, autoOpenChannel);
        log.info("Qnx设备已连接完毕,开始加载视频流...");

        // 使用SwingWorker异步执行，不阻塞UI
        new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                // 短暂延迟确保UI已完全初始化
                Thread.sleep(500);
                return null;
            }

            @Override
            protected void done() {
                // 在EDT线程中执行UI操作
                SwingUtilities.invokeLater(() -> qnxLoadButton.doClick());
            }
        }.execute();
    }

    @Override
    public void playOrPause() {
        resetVideoStreamPauseTimer();

        if (isStreamPausing()) {
            // 当前是暂停状态，需要恢复
            isPaused = false;
            synchronized (pauseLock) {
                pauseLock.notifyAll(); // 唤醒等待的线程
            }
            // 清除暂停提示文字
            getPicturePanel().clearText();
            log.info("恢复视频流");
        } else {
            // 当前是播放状态，需要暂停
            isPaused = true;
            getPicturePanel().setText("视频流已暂停，点击播放按钮继续");
            log.info("暂停视频流");
        }

        super.playOrPause(); // 调用父类方法切换按钮状态
    }

    private void resetVideoStreamPauseTimer() {
        if (refreshStreamTimer != null) {
            refreshStreamTimer.restart();
        }
    }

    @Override
    protected void startGrab() {
        super.startGrab();
        int timeout = 5 * 60 * 1000;
        refreshStreamTimer = new Timer(timeout, e -> {
            if (isStreamPausing()) {
                return;
            }
            log.info("用户没有操作界面超过{}秒，自动暂停视频流", timeout / 1000);
            // 自动暂停视频流时显示提示文字
            getPicturePanel().setText("视频流已自动暂停，点击播放按钮继续");

            // 设置暂停标志而不是停止流
            isPaused = true;
            super.playOrPause(); // 切换按钮状态
        });
        // refreshStreamTimer.start();
    }

    // 添加自动重试机制
    private void setupStreamMonitoring() {
        // 创建一个定时器，定期检查视频流状态
        if (retryTimer != null) {
            retryTimer.stop();
        }

        retryTimer = new Timer(5000, e -> {
            // 检查是否需要重试
            checkAndRetryStream();
        });
        retryTimer.setRepeats(true);
        retryTimer.start();
    }

    private void checkAndRetryStream() {
        synchronized (retryLock) {
            // 如果已经在重试中，或者已经达到最大重试次数，则不再重试
            if (isRetrying || retryCount >= MAX_RETRY_COUNT || isPaused) {
                return;
            }

            // 检查视频流是否正常
            boolean needsRetry = false;

            // 检查rtmpStreamWorker状态
            if (rtmpStreamWorker == null || rtmpStreamWorker.isDone()) {
                log.warn("检测到视频流工作线程已结束，尝试重新加载...");
                needsRetry = true;
            }

            // 如果需要重试
            if (needsRetry) {
                isRetrying = true;
                retryCount++;

                log.info("开始第 {} 次自动重试加载视频流", retryCount);

                // 在UI上显示重试状态
                SwingUtilities.invokeLater(() -> {
                    if (getPicturePanel() != null) {
                        getPicturePanel().setText("视频流异常，正在进行第 " + retryCount + " 次重试...");
                    }
                });

                // 使用SwingWorker异步执行重试
                new SwingWorker<Void, Void>() {
                    @Override
                    protected Void doInBackground() throws Exception {
                        try {
                            // 先确保之前的资源被清理
                            if (rtmpStreamWorker != null && !rtmpStreamWorker.isDone()) {
                                rtmpStreamWorker.cancel(true);
                                // 等待资源释放
                                Thread.sleep(1000);
                            }

                            // 确保QNX命令工作线程已停止
                            if (qnxCommandWorker != null && !qnxCommandWorker.isDone()) {
                                qnxCommandWorker.cancel(true);
                                Thread.sleep(500);
                            }

                            // 强制垃圾回收
                            System.gc();
                            Thread.sleep(500);

                            return null;
                        } catch (Exception ex) {
                            log.error("准备重试时出错", ex);
                            return null;
                        }
                    }

                    @Override
                    protected void done() {
                        // 在EDT线程中执行UI操作
                        SwingUtilities.invokeLater(() -> {
                            try {
                                // 重新加载视频流
                                String filePath = qnxPathTextField.getText();
                                if (filePath != null && !filePath.trim().isEmpty()) {
                                    log.info("自动重试: 重新加载视频流，路径: {}", filePath);
                                    startQnxCommandWorker(filePath);
                                } else {
                                    log.error("自动重试失败: 文件路径为空");
                                    isRetrying = false;
                                }
                            } finally {
                                // 重置重试标志
                                synchronized (retryLock) {
                                    isRetrying = false;
                                }
                            }
                        });
                    }
                }.execute();
            } else {
                // 如果视频流正常，重置重试计数
                if (retryCount > 0) {
                    log.info("视频流恢复正常，重置重试计数");
                    retryCount = 0;
                }
            }
        }
    }
}


