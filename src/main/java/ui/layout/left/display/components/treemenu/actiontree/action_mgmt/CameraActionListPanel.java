package ui.layout.left.display.components.treemenu.actiontree.action_mgmt;

import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.border.TitledBorder;

/**
 * 相机动作列表面板
 */
public class CameraActionListPanel extends ActionListPanel implements BaseView {

    private final JButton existImageButton;
    private final JButton disallowExistImageButton;

    private final JButton waitImageAppearButton;
    private final JButton waitImageDisappearButton;
    private final MainModel mainModel;

    public CameraActionListPanel(MainModel mainModel) {
        this.mainModel = mainModel;
        existImageButton = new JButton("存在该图片");
        disallowExistImageButton = new JButton("不允许存在该图片");
        waitImageAppearButton = new JButton("等待该图片出现");
        waitImageDisappearButton = new JButton("等待该图片消失");
        createView();
    }

    @Override
    public void createView() {
        setBorder(new TitledBorder("相机方法"));
        add(existImageButton);
        add(disallowExistImageButton);
        add(waitImageAppearButton);
        add(waitImageDisappearButton);
    }
}
