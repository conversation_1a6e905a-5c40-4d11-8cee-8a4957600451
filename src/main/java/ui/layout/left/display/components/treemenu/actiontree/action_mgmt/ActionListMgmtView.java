package ui.layout.left.display.components.treemenu.actiontree.action_mgmt;

import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * 动作列表面板视图
 */
public class ActionListMgmtView extends JPanel implements BaseView {

    private final PowerActionListPanel powerActionListPanel;

    private final CameraActionListPanel cameraActionListPanel;

    private final BrowserActionListPanel browserActionListPanel;


    public ActionListMgmtView(MainModel mainModel) {
        browserActionListPanel = new BrowserActionListPanel(mainModel);
        powerActionListPanel = new PowerActionListPanel(mainModel);
        cameraActionListPanel = new CameraActionListPanel(mainModel);
        createView();
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(5, 1));
        add(browserActionListPanel);
//        add(powerActionListPanel);
//        add(cameraActionListPanel);
    }
}
