package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.Data;

/**
 * Excel测试用例数据模型
 */
@Data 
public class ExcelCaseModel {
    private Integer id;
    private String uuid; // 唯一标识
    private String tableName; // 表名
    private String testCaseID; // 测试用例ID
    private String testKey; // 测试关键字
    private String initialCondition; // 初始条件
    private String action; // 操作步骤
    private String expectedResult; // 期望结果
    private String initTestSequences; // 前置测试序列
    private String actionTestSequences; // 动作测试序列
    private String expectedTestSequences; // 期望测试序列
    private String actualResult; // 实际结果
    private String testResult; // 测试结果
    private String tester; // 测试人员
    private String testTime; // 测试时间
    private String remark; // 备注
    private String targetTestTimes = "0";  // 目标测试次数
    private String testedTimes = "0";  // 已测试次数
    private String testedPassTimes = "0"; // 已通过次数
}
