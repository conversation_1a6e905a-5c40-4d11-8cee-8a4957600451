package ui.layout.left.display.components.container.can;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

@EqualsAndHashCode(callSuper = true)
@Data
public class CanMessageInspectorRequest extends OperationObject {

    private String messageName;
    private String messageData;
    private String timeout;

    @Override
    public String getFriendlyString() {
        if (messageData != null && !messageData.isEmpty()) {
            return String.format("报文名称:%s 数据:%s", messageName, messageData);
        } else {
            return String.format("报文名称:%s", messageName);
        }
    }
}
