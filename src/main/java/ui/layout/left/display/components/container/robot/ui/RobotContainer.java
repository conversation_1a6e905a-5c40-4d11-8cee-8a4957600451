package ui.layout.left.display.components.container.robot.ui;

import lombok.Getter;
import sdk.domain.Device;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.RobotDevice;
import ui.config.json.devices.robot.RobotConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.robot.ui.panels.RobotOperationPanel;
import ui.layout.left.display.components.container.screen.ScreenConfigPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * 机械臂主容器
 */
public class RobotContainer extends DeviceContainer {

    private static final String SCREEN_CONFIG = "显示屏配置";
    private static final String ROBOT_OPERATION = "机械臂操作";
    private static final String ROBOT_ADVANCED_SETTING = "机械臂高级设置";

    /**
     * Tab布局（中间）
     */
    private final JTabbedPane tabbedPane;

    @Getter
    private final ScreenConfigPanel screenConfigPanel; //触摸协议设置面板

    @Getter
    private final RobotOperationPanel robotOperationPanel; //机械臂操作

    @Getter
    private final RobotAdvancedSettingsPanel robotAdvancedSettingsPanel; //高级设置面板

    public RobotContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        String projectName = mainModel.getAppInfo().getProject();
        RobotConfig robotConfig = ((RobotDevice) device).loadConfig(projectName);
        ScreenConfig screenConfig = OperationTargetHolder.getScreenKit().loadConfig(projectName);

        //中间
        tabbedPane = new JTabbedPane();
        screenConfigPanel = new ScreenConfigPanel(clientView, mainModel, this, screenConfig);
        robotOperationPanel = new RobotOperationPanel(mainModel, this, robotConfig, screenConfig);
        robotAdvancedSettingsPanel = new RobotAdvancedSettingsPanel(clientView, mainModel, this, robotConfig);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        super.createView();
        tabbedPane.setBorder(BorderFactory.createEtchedBorder());
        tabbedPane.add(SCREEN_CONFIG, screenConfigPanel);
        tabbedPane.add(ROBOT_OPERATION, robotOperationPanel);
        tabbedPane.add(ROBOT_ADVANCED_SETTING, robotAdvancedSettingsPanel);
        add(tabbedPane, BorderLayout.CENTER);
    }


    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        screenConfigPanel.controlDisplay(isDeviceConnected);
        robotOperationPanel.controlDisplay(isDeviceConnected);
        robotAdvancedSettingsPanel.controlDisplay(isDeviceConnected);

    }

}
