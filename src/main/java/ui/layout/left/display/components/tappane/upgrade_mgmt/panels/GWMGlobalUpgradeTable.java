package ui.layout.left.display.components.tappane.upgrade_mgmt.panels;

import ui.base.BaseView;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.io.File;

/**
 * GWM通用升级工具
 */
public class GWMGlobalUpgradeTable extends JPanel implements BaseView {
    private final JPanel portPanel;
    private final JPanel disPanel;
    private final JPanel buttonPanel;
    private final JComboBox<String> portComboBox;
    private final JComboBox<String> delComboBox;
    private final JComboBox<String> caseComboBox;
    private final JComboBox<String> typeComboBox;
    private final JSpinner numSpinner;
    private final JButton upgradeButton;
    private final JProgressBar progressBar;

    public GWMGlobalUpgradeTable() {
        portPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        disPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        portComboBox = new JComboBox<>();
        delComboBox = new JComboBox<>();
        caseComboBox = new JComboBox<>();
        typeComboBox = new JComboBox<>();
        numSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));
        upgradeButton = new JButton("开始升级");
        progressBar = new JProgressBar();
        createView();
        createActions();
    }

    @Override
    public void createView() {
        JPanel jPanel = new JPanel(new GridLayout(6, 1));
        portPanel.add(new JLabel("选择串口号"));
        portPanel.add(portComboBox);
        portPanel.add(new JLabel("分段擦除"));
        portPanel.add(delComboBox);
        portPanel.add(new JLabel("升级次数"));
        portPanel.add(numSpinner);
        disPanel.add(new JLabel("TestCase"));
        disPanel.add(caseComboBox);
        disPanel.add(new JLabel("显示屏类型"));
        disPanel.add(typeComboBox);
        buttonPanel.add(upgradeButton);
        buttonPanel.add(progressBar);
        jPanel.add(fileChooserPanel("Updater路径:", "txt"));
        jPanel.add(fileChooserPanel("Boot路径:", "bin"));
        jPanel.add(fileChooserPanel("MCU路径:", "s19"));
        jPanel.add(portPanel);
        jPanel.add(disPanel);
        jPanel.add(buttonPanel);
        setLayout(new BorderLayout());
        add(jPanel,BorderLayout.NORTH);
    }

    private JPanel fileChooserPanel(String label, String fileExtension) {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        JLabel jLabel = new JLabel(label);
        JTextField textField = new JTextField(20);
        JButton button = new JButton("选择");
        button.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();

            if (!fileExtension.isEmpty()) {
                FileNameExtensionFilter filter = new FileNameExtensionFilter(
                        label + " Files (*." + fileExtension + ")", fileExtension);
                fileChooser.setFileFilter(filter);
            }

            int result = fileChooser.showOpenDialog(null);

            if (result == JFileChooser.APPROVE_OPTION) {
                File selectedFile = fileChooser.getSelectedFile();
                textField.setText(selectedFile.getAbsolutePath());
            }
        });

        panel.add(jLabel);
        panel.add(textField);
        panel.add(button);

        return panel;
    }

}
