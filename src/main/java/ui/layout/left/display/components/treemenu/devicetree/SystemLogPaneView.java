package ui.layout.left.display.components.treemenu.devicetree;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.SseSession;
import sdk.constants.UrlConstants;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.test_executor.TestExecuteStatusObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.text.BadLocationException;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.LinkedList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ui.utils.SwingUtil.getResourceAsImageIcon;

/**
 * 系统日志面板
 */
@Slf4j
public class SystemLogPaneView extends JPanel implements BaseView, TestExecuteStatusObserver {
    private final static int MAX_LINES = 10000;
    private static final int BUFFER_CAPACITY = 8192;
    private final Pattern pattern = Pattern.compile("\"(.*)\"");
    /**
     * 处理日志消息的主要方法
     */
    private final StringBuilder pendingText = new StringBuilder();

    private final CircularBuffer logBuffer;
    private boolean pauseAutoScroll = false;

    private final ExecutorService executor;
    private final SseSession sseSession;
    private final MainModel mainModel;

    private final JTextArea textArea;

    private final JButton messageButton;
    private final JButton fileButton;
    private final JButton copyButton;
    private final JButton detectButton;
    private final JButton setButton;
    private final JButton triangleButton;
    private final JButton succeedButton;
    private final JButton warningButton;
    private final JButton wrongButton;
    private final JToggleButton pauseButton;
    private final JTextField textField;
    private final JPopupMenu popupMenu;

    private final ImageIcon scaledPauseIconImage;
    private final ImageIcon scaledPauseOffIconImage;
    private final JMenuItem exportLogItem;

    /**
     * 循环缓冲区实现，固定大小，自动移除旧行
     */
    private static class CircularBuffer {
        private final LinkedList<String> lines = new LinkedList<>();
        private final int maxLines;
        private final StringBuilder lineBuffer = new StringBuilder(BUFFER_CAPACITY);
        private int bufferedLines = 0;

        public CircularBuffer(int maxLines) {
            this.maxLines = maxLines;
        }

        public synchronized void append(String line) {
            lineBuffer.append(line).append('\n');
            bufferedLines++;

            if (bufferedLines >= 100 || lineBuffer.length() >= BUFFER_CAPACITY - 1000) {
                flush();
            }
        }

        private void flush() {
            if (lineBuffer.length() > 0) {
                String[] newLines = lineBuffer.toString().split("\n");
                for (String line : newLines) {
                    lines.addLast(line);
                    while (lines.size() > maxLines) {
                        lines.removeFirst();
                    }
                }
                lineBuffer.setLength(0);
                bufferedLines = 0;
            }
        }

        public synchronized String getContent() {
            flush();
            return String.join("\n", lines);
        }

        public synchronized String getFilteredContent(String filterText) {
            flush();
            if (filterText.isEmpty()) {
                return getContent();
            }
            return lines.stream()
                    .filter(line -> line.contains(filterText))
                    .collect(Collectors.joining("\n"));
        }

        public synchronized void clear() {
            lines.clear();
            lineBuffer.setLength(0);
            bufferedLines = 0;
        }
    }

    public SystemLogPaneView(ClientView clientView, MainModel mainModel) {
        this.mainModel = mainModel;
        executor = Executors.newFixedThreadPool(2);
        logBuffer = new CircularBuffer(MAX_LINES);

        popupMenu = new JPopupMenu();
        textArea = new JTextArea();
        textArea.setEditable(false);
        sseSession = new SseSession();
        messageButton = SwingUtil.messageButton();
        fileButton = SwingUtil.fileButton();
        copyButton = SwingUtil.copyButton();
        detectButton = SwingUtil.detectButton();
        setButton = SwingUtil.setButton();
        triangleButton = SwingUtil.triangleButton();
        succeedButton = SwingUtil.succeedButton();
        warningButton = SwingUtil.warningButton();
        wrongButton = SwingUtil.wrongButton();
        pauseButton = new JToggleButton();
        // 首先设置不绘制按钮边框
        pauseButton.setBorderPainted(false);
        pauseButton.setDoubleBuffered(true);
        pauseButton.setContentAreaFilled(false);
        pauseButton.setBorder(BorderFactory.createEmptyBorder());
        textField = new JTextField();
        exportLogItem = new JMenuItem("导出日志");


        ImageIcon pauseIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.CommonLayout.pauseOffIconPath);
        ImageIcon pauseOffIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.CommonLayout.pauseIconPath);

        Image scaledPauseIcon = pauseIcon.getImage().getScaledInstance(18, 18, Image.SCALE_SMOOTH);
        Image scaledPauseOffIcon = pauseOffIcon.getImage().getScaledInstance(18, 18, Image.SCALE_SMOOTH);
        scaledPauseIconImage = new ImageIcon(scaledPauseIcon);
        scaledPauseOffIconImage = new ImageIcon(scaledPauseOffIcon);


        createView();
        createActions();
        registerModelObservers();

        // 启动批量更新日志线程
        startAppenderThread();
    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestExecuteStatusModel().registerObserver(this);
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout(5, 5));
        Box logBox = Box.createHorizontalBox();
        logBox.add(pauseButton);
        logBox.add(new JLabel(" | "));
        logBox.add(fileButton);
        logBox.add(copyButton);
        logBox.add(detectButton);
        logBox.add(setButton);
        logBox.add(new JLabel(" | "));
        logBox.add(triangleButton);
        logBox.add(new JLabel(" | "));
        logBox.add(messageButton);
        logBox.add(succeedButton);
        logBox.add(warningButton);
        logBox.add(wrongButton);
        logBox.add(new JLabel("过滤:"));
        logBox.add(textField);
        setLayout(new BorderLayout());
        JPanel textPanel = new JPanel();
        textPanel.setLayout(new BoxLayout(textPanel, BoxLayout.Y_AXIS));

        JPanel headerPanel = new JPanel();
        headerPanel.setLayout(new FlowLayout(FlowLayout.LEFT, 10, 0));
        ImageIcon clockIcon = getResourceAsImageIcon(ResourceConstant.LeftLayout.clockIconPath, 12.0f);
        ImageIcon infoIcon = getResourceAsImageIcon(ResourceConstant.LeftLayout.messageIconPath, 12.0f);
        String[] headerContent = {"时间", "信息"};

        for (int i = 0; i < headerContent.length; i++) {
            JLabel iconLabel;
            if (i == 0) {
                iconLabel = new JLabel(clockIcon);
                iconLabel.setHorizontalAlignment(SwingConstants.CENTER);
            } else {
                iconLabel = new JLabel(infoIcon);
                iconLabel.setHorizontalAlignment(SwingConstants.CENTER);
            }
            JLabel headerLabel = new JLabel(headerContent[i]);
            headerPanel.add(iconLabel);
            headerPanel.add(headerLabel);
            headerPanel.add(Box.createHorizontalStrut(50));
        }
        headerPanel.setVisible(true);
        textPanel.add(headerPanel, 0);

        pauseButton.setIcon(scaledPauseOffIconImage);
        pauseButton.setSelectedIcon(scaledPauseIconImage);
        popupMenu.add(exportLogItem);
        add(logBox, BorderLayout.NORTH);

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(scrollPane.getPreferredSize().width, 600));
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        textPanel.add(scrollPane);
        add(textPanel, BorderLayout.CENTER);
    }

    private void clearTextArea() {
        logBuffer.clear();
        textArea.setText("");
    }

    @Override
    public void createActions() {
        sseRunLogStreams();

        detectButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                clearTextArea();
            }
        });

        setButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isLeftMouseButton(e)) {
                    popupMenu.show(e.getComponent(), e.getX(), e.getY());
                }
            }
        });

        pauseButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                pauseAutoScroll = !pauseAutoScroll; // 切换暂停状态
                if (pauseAutoScroll) {
                    pauseButton.setIcon(scaledPauseIconImage);
                } else {
                    pauseButton.setIcon(scaledPauseOffIconImage);
                }
            }
        });

        exportLogItem.addActionListener(e -> {
            try {
                File file = new File("log.txt");
                FileWriter writer = new FileWriter(file);
                writer.write(textArea.getText());
                writer.close();
                JOptionPane.showMessageDialog(null, "日志已导出到 " + file.getAbsolutePath());
            } catch (IOException ex) {
                JOptionPane.showMessageDialog(null, "导出日志时发生错误: " + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        });

        textField.addKeyListener(new KeyListener() {
            @Override
            public void keyTyped(KeyEvent e) {
            }

            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    applyFilter(textField.getText().trim());
                }
            }

            @Override
            public void keyReleased(KeyEvent e) {

            }
        });

    }

    /**
     * 处理日志消息的主要方法
     */
    private void handleLogMessage(String message) {
        logBuffer.append(message);
        scheduleAppend(message);
    }


    /**
     * 调度批量更新日志的方法
     */
    private void scheduleAppend(String text) {
        synchronized (pendingText) {
            pendingText.append(text).append(System.lineSeparator());
        }
    }

    /**
     * 启动批量更新日志的线程
     */
    private void startAppenderThread() {
        executor.execute(() -> {
            while (true) {
                try {
                    Thread.sleep(500); // 每500毫秒更新一次
                    final String textToAppend;
                    synchronized (pendingText) {
                        textToAppend = pendingText.toString();
                        pendingText.setLength(0);
                    }
                    if (!textToAppend.isEmpty()) {
                        updateTextArea(textToAppend);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    /**
     * 更新文本显示
     */
    private void updateTextArea(String newText) {
        SwingUtilities.invokeLater(() -> {
            String filterText = textField.getText().trim();
            if (filterText.isEmpty() || newText.contains(filterText)) {
                textArea.append(newText);
                int lineCount = textArea.getLineCount();
                if (lineCount > MAX_LINES) {
                    try {
                        int end = textArea.getLineEndOffset(lineCount - MAX_LINES - 1);
                        textArea.replaceRange("", 0, end);
                    } catch (BadLocationException e) {
                        log.error("Failed to remove old lines", e);
                    }
                }
                if (!pauseAutoScroll) {
                    textArea.setCaretPosition(textArea.getDocument().getLength());
                }
            }
        });
    }


    /**
     * 实现过滤功能
     */
    private void applyFilter(String filterText) {
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                String filteredContent = logBuffer.getFilteredContent(filterText);
                SwingUtilities.invokeLater(() -> {
                    textArea.setText(filteredContent);
                    if (!pauseAutoScroll) {
                        textArea.setCaretPosition(textArea.getDocument().getLength());
                    }
                });
                return null;
            }
        };
        worker.execute();
    }

    public int getLineCount() {
        return textArea.getLineCount();
    }

    public void clearContentIfMax() {
        if (getLineCount() > MAX_LINES) {
            textArea.setText("");
        }
    }


    private void sseRunLogStreams() {
        executor.submit(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl("runLog"), line -> {
                    String handledLine;
                    String s = line.replaceFirst("data:", "");

                    Matcher matcher = pattern.matcher(s);
                    if (matcher.find()) {
                        handledLine = matcher.group(1);
                    } else {
                        handledLine = s;
                    }

                    handleLogMessage(handledLine);
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public void testStarted(String testSuiteName) {
        clearTextArea();
    }

}
