package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.signal_generator.SignalGeneratorContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * 信号发生器控制界面
 */
public class SignalGeneratorTabPaneView extends DeviceTabPaneView {

    public SignalGeneratorTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        DeviceContainer deviceContainer;
        deviceContainer = new SignalGeneratorContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, deviceContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
