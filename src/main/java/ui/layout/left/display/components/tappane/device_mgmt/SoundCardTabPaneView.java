package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.soundcard.SoundContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * 声音采集控制界面
 */
public class SoundCardTabPaneView extends DeviceTabPaneView {

    public SoundCardTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        DeviceContainer deviceContainer = new SoundContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, deviceContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
