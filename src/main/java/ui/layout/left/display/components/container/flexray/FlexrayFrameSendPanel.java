package ui.layout.left.display.components.container.flexray;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import common.utils.StringUtils;
import lombok.Getter;
import sdk.base.operation.Operation;
import sdk.domain.bus.FlexrayMessage;
import ui.base.BaseView;
import ui.base.Filters;
import ui.base.OperationAssemblerPanel;
import ui.base.component.FixedHexTextField;
import ui.layout.left.display.components.container.can.CanMessageDataEditDialog;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.text.AbstractDocument;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.List;
import java.util.Timer;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * FlexRay帧发送面板
 */
public class FlexrayFrameSendPanel extends OperationAssemblerPanel<FlexrayMessage> implements BaseView {

    private static final Integer[] flexrayDataLengthArray = new Integer[]{32};
    private JTextField channelTextField;
    private JComboBox<Option> channelMaskComboBox;
    private JButton dataEditBtn;
    private JComboBox<Integer> dataLengthComboBox;
    private JCheckBox alwaysSendCheckBox;
    private FixedHexTextField dataTextField;
    private JSpinner sendTimesSpinner;
    private JTextField frameIDTextField;
    private JTextField cycleOffsetTextField;
    private JTextField cycleRepetitionTextField;
    private JSpinner everyIntervalSpinner;
    private JSpinner framesPerSendNumSpinner;
    private final int channel;

    public FlexrayFrameSendPanel() {
        this(-1);
    }

    public FlexrayFrameSendPanel(int channel) {
        this.channel = channel;
        createView();
        createActions();
        setDefaultData();
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(1, 1));
        add(setupFrameSendPanel());
    }

    private String dataGenerator(Integer dlc) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < dlc; i++) {
            sb.append("00 ");
        }
        return sb.toString().trim();
    }

    private final ItemListener dlcChangeItemListener = e -> {
        if (e.getStateChange() == ItemEvent.SELECTED) {
            int dlc = (Integer) dataLengthComboBox.getSelectedItem();
            dataTextField.setMask(dlc);
            dataTextField.setText(dataGenerator((dlc)));
        }
    };

    @Override
    public void createActions() {
        dataTextField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyTyped(KeyEvent e) {
                super.keyTyped(e);
                dataTextKeyTypedEventHandler(e);
            }
        });
        dataLengthComboBox.addItemListener(dlcChangeItemListener);
        alwaysSendCheckBox.addActionListener(e -> sendTimesSpinner.setEnabled(!alwaysSendCheckBox.isSelected()));
        dataEditBtn.addActionListener(e -> dataEditEventHandler());
    }

    @Override
    public FlexrayMessage assembleOperationObject() {
        return getCurrentFlexrayMessage();
    }

    private void buildUI(FlexrayMessage flexrayMessage) {
        channelTextField.setText(String.valueOf(flexrayMessage.getChannel()));
        channelMaskComboBox.setSelectedItem(flexrayMessage.getChannelMask());
        dataLengthComboBox.setSelectedItem(flexrayMessage.getDlc());
        dataTextField.setText(StringUtils.byteArrayToHexString(flexrayMessage.getData() == null ? new byte[]{} : flexrayMessage.getData()));
        frameIDTextField.setText(String.format("%X", flexrayMessage.getSlotId()));
        alwaysSendCheckBox.setSelected(flexrayMessage.getSendTimes() == -1 ||
                (flexrayMessage.getDuration() != null && flexrayMessage.getDuration() == -1));
        cycleOffsetTextField.setText(String.valueOf(flexrayMessage.getOffest()));
        cycleRepetitionTextField.setText(String.valueOf(flexrayMessage.getRepetitior()));
        framesPerSendNumSpinner.setValue(flexrayMessage.getFramesPerSendNum());
        sendTimesSpinner.setValue(flexrayMessage.getSendTimes());
        if (flexrayMessage.getSendTimes() != -1) {
            sendTimesSpinner.setEnabled(true);
            sendTimesSpinner.setValue(flexrayMessage.getSendTimes());
        } else {
            sendTimesSpinner.setEnabled(false);
            alwaysSendCheckBox.setEnabled(true);
        }
        everyIntervalSpinner.setValue(flexrayMessage.getPeriod() * 1000.0f);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) {
        FlexrayMessage flexrayMessage = JSONObject.parseObject(JSON.toJSONString(injectedOperation.getOperationObject()), FlexrayMessage.class);
        buildUI(flexrayMessage);
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationObject(assembleOperationObject());
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        String frameId = frameIDTextField.getText().trim();
        String data = dataTextField.getText().trim();
        String offset = cycleOffsetTextField.getText().trim();
        String repetitior = cycleRepetitionTextField.getText().trim();
        if (frameId.isEmpty()) {
            SwingUtil.showWarningDialog(this, "帧ID不能为空");
            return false;
        }
        if (data.isEmpty()) {
            SwingUtil.showWarningDialog(this, "报文不能为空");
            return false;
        }
        if (offset.isEmpty()) {
            SwingUtil.showWarningDialog(this, "cycle offset不能为空");
            return false;
        }
        if (repetitior.isEmpty()) {
            SwingUtil.showWarningDialog(this, "cycle Repetitior不能为空");
            return false;
        }
        return true;
    }

    private static byte[] hexToBytes(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return null;
        }
        hexString = hexString.replaceAll("\\s+", "").toLowerCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] bytes = new byte[length];
        String hexDigits = "0123456789abcdef";
        for (int i = 0; i < length; i++) {
            int pos = i * 2; // 两个字符对应一个byte
            int h = hexDigits.indexOf(hexChars[pos]) << 4; // 注1
            int l = hexDigits.indexOf(hexChars[pos + 1]); // 注2
            if (l == -1) { // 非16进制字符
                return null;
            }
            bytes[i] = (byte) (h | l);
        }
        return bytes;
    }


    public FlexrayMessage getCurrentFlexrayMessage() {
        String frameId = frameIDTextField.getText().trim();
        FlexrayMessage flexrayMessage = new FlexrayMessage();
        flexrayMessage.setSlotId(Integer.parseInt(frameId, 16));
        flexrayMessage.setChannel(Integer.valueOf(channelTextField.getText()));
        Option selectedItem = (Option) (channelMaskComboBox.getSelectedItem());
        if (selectedItem != null) {
            flexrayMessage.setChannelMask(selectedItem.getActualValue());
        }
        flexrayMessage.setOffest(Integer.valueOf(cycleOffsetTextField.getText()));
        flexrayMessage.setRepetitior(Integer.valueOf(cycleRepetitionTextField.getText()));
        Object dlc = dataLengthComboBox.getSelectedItem();
        if (dlc != null) {
            flexrayMessage.setDlc((Integer) dlc);
        }
        flexrayMessage.setData(hexToBytes(dataTextField.getText()));
        float period = Float.parseFloat(everyIntervalSpinner.getValue().toString()) / 1000.0f;
        if (alwaysSendCheckBox.isSelected()) {
//            duration = -1;
            flexrayMessage.setSendTimes(-1);
        } else {
//            duration = ((int) (sendTimesSpinner.getValue())) * period;
            flexrayMessage.setSendTimes((Integer) sendTimesSpinner.getValue());
        }
        flexrayMessage.setFramesPerSendNum((Integer) framesPerSendNumSpinner.getValue());
//        canMessage.setDuration(duration);
        flexrayMessage.setPeriod(period);
        return flexrayMessage;
    }

    private void setDefaultData() {
        dataLengthComboBox.setSelectedItem(32);
        frameIDTextField.setText("");
        dataTextField.setText("00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
        //dataTextField.setEnabled(false);
        sendTimesSpinner.setValue(1);
        alwaysSendCheckBox.doClick();
        everyIntervalSpinner.setValue(10);
    }

    private void fillDlcLengthModel(Integer[] array) {
        ((DefaultComboBoxModel<Integer>) dataLengthComboBox.getModel()).removeAllElements();
        for (int dlc : array) {
            ((DefaultComboBoxModel<Integer>) dataLengthComboBox.getModel()).addElement(dlc);
        }
    }

    private void changeDateLength() {
        Integer oldDLC = (Integer) dataLengthComboBox.getSelectedItem();
        dataLengthComboBox.removeItemListener(dlcChangeItemListener);
        fillDlcLengthModel(flexrayDataLengthArray);
        dataLengthComboBox.addItemListener(dlcChangeItemListener);
        if (((DefaultComboBoxModel<Integer>) dataLengthComboBox.getModel()).getIndexOf(oldDLC) == -1) {
            dataLengthComboBox.setSelectedItem(8);
        } else {
            dataLengthComboBox.removeItemListener(dlcChangeItemListener);
            dataLengthComboBox.setSelectedItem(oldDLC);
            dataLengthComboBox.addItemListener(dlcChangeItemListener);
        }
    }

    private JPanel setupFrameSendPanel() {
        JPanel frameSendPanel = new JPanel();
        GroupLayout layout = new GroupLayout(frameSendPanel);
        frameSendPanel.setLayout(layout);

        // Channel Section
        JLabel channelLabel = new JLabel("通道:");
        channelTextField = new JTextField();
        channelTextField.setEditable(false);
        channelTextField.setText(String.valueOf(channel));
        channelTextField.setColumns(10);

        JLabel channelMaskLabel = new JLabel("通道掩码:");
        List<Option> options = new ArrayList<>();
        options.add(new Option("A", 1));
        options.add(new Option("B", 2));
        options.add(new Option("AB", 3));
        channelMaskComboBox = new JComboBox<>(options.toArray(new Option[0]));

        JLabel dataLengthLabel = new JLabel("数据长度:");
        dataLengthComboBox = new JComboBox<>(flexrayDataLengthArray);
        dataLengthComboBox.setSelectedItem(8);

        // Frame Section
        JLabel frameIDLabel = new JLabel("SoltID: 0x");
        frameIDTextField = new JTextField(10);
        ((AbstractDocument) frameIDTextField.getDocument()).setDocumentFilter(new Filters.HexadecimalDocumentFilter());

        JLabel cycleOffsetLabel = new JLabel("cycle Offset:");
        cycleOffsetTextField = new JTextField(10);

        JLabel cycleRepetitionLabel = new JLabel("cycle Repetition:");
        cycleRepetitionTextField = new JTextField(10);

        // Data Section
        JLabel dataLabel = new JLabel("数据: 0x");
        dataTextField = new FixedHexTextField();
//        ((PlainDocument) dataTextField.getDocument()).setDocumentFilter(new Filters.HexPasteLimiterFilter(96));
        int dlc = (Integer) dataLengthComboBox.getSelectedItem();
        dataTextField.setMask(dlc);
        dataEditBtn = new JButton("编辑");

        // Send Cycle Section
        JLabel sendCycleLabel = new JLabel("发送次数:");
        sendTimesSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));

        alwaysSendCheckBox = new JCheckBox("一直发送");

        JLabel everyIntervalLabel = new JLabel("每次间隔(ms):");
        everyIntervalSpinner = new JSpinner(new SpinnerNumberModel(0f, 0f, null, 1f));

        JLabel framesPerSendNumLabel = new JLabel("每次发送帧数:");
        framesPerSendNumSpinner = new JSpinner(new SpinnerNumberModel(1, 1, null, 1));

        // 设置 GroupLayout
        layout.setAutoCreateGaps(true);
        layout.setAutoCreateContainerGaps(true);

        layout.setHorizontalGroup(
                layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                        .addGroup(layout.createSequentialGroup()
                                .addComponent(channelLabel)
                                .addComponent(channelTextField)
                                .addComponent(channelMaskLabel)
                                .addComponent(channelMaskComboBox)
                                .addComponent(dataLengthLabel)
                                .addComponent(dataLengthComboBox))
                        .addGroup(layout.createSequentialGroup()
                                .addComponent(frameIDLabel)
                                .addComponent(frameIDTextField)
                                .addComponent(cycleOffsetLabel)
                                .addComponent(cycleOffsetTextField)
                                .addComponent(cycleRepetitionLabel)
                                .addComponent(cycleRepetitionTextField))
                        .addGroup(layout.createSequentialGroup()
                                .addComponent(dataLabel)
                                .addComponent(dataTextField)
                                .addComponent(dataEditBtn))
                        .addGroup(layout.createSequentialGroup()
                                .addComponent(sendCycleLabel)
                                .addComponent(sendTimesSpinner)
                                .addComponent(alwaysSendCheckBox)
                                .addComponent(everyIntervalLabel)
                                .addComponent(everyIntervalSpinner)
                                .addComponent(framesPerSendNumLabel)
                                .addComponent(framesPerSendNumSpinner))
        );

        layout.setVerticalGroup(
                layout.createSequentialGroup()
                        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                .addComponent(channelLabel)
                                .addComponent(channelTextField)
                                .addComponent(channelMaskLabel)
                                .addComponent(channelMaskComboBox)
                                .addComponent(dataLengthLabel)
                                .addComponent(dataLengthComboBox))
                        .addGap(15)
                        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                .addComponent(frameIDLabel)
                                .addComponent(frameIDTextField)
                                .addComponent(cycleOffsetLabel)
                                .addComponent(cycleOffsetTextField)
                                .addComponent(cycleRepetitionLabel)
                                .addComponent(cycleRepetitionTextField))
                        .addGap(15)
                        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                .addComponent(dataLabel)
                                .addComponent(dataTextField)
                                .addComponent(dataEditBtn))
                        .addGap(15)
                        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                .addComponent(sendCycleLabel)
                                .addComponent(sendTimesSpinner)
                                .addComponent(alwaysSendCheckBox)
                                .addComponent(everyIntervalLabel)
                                .addComponent(everyIntervalSpinner)
                                .addComponent(framesPerSendNumLabel)
                                .addComponent(framesPerSendNumSpinner))
        );

        return frameSendPanel;
    }


    private int deleteSpace(String text) {
        String reg = "[^\\dA-Fa-f]";
        Matcher matcher = Pattern.compile(reg).matcher(text);
        String str1 = matcher.replaceAll("").trim();
        return str1.length();
    }

    private void dataTextKeyTypedEventHandler(KeyEvent e) {
        // 限制只允许输入 0-9, A-F, a-f, 空格和退格
        boolean limitNum = (e.getKeyChar() >= KeyEvent.VK_0 && e.getKeyChar() <= KeyEvent.VK_9);
        boolean limitLetter = ((int) e.getKeyChar() >= 65 && (int) e.getKeyChar() <= 70) || ((int) e.getKeyChar() >= 97 && (int) e.getKeyChar() <= 102);
        if (!(limitNum || (e.getKeyChar() == KeyEvent.VK_SPACE) || e.getKeyChar() == KeyEvent.VK_BACK_SPACE || limitLetter)) {
            e.consume();
        }
        // 设置允许输入的最大字符长度（32 个 00，即 64 个字符）
        int maxLength = Integer.parseInt(Objects.requireNonNull(dataLengthComboBox.getSelectedItem()).toString()) * 2;

        // 延迟执行文本格式化
        SwingUtilities.invokeLater(() -> {
            if (e.getKeyChar() != KeyEvent.VK_BACK_SPACE) {
                StringBuilder stringBuilder = new StringBuilder(dataTextField.getText().toUpperCase());
                int realLength = deleteSpace(String.valueOf(stringBuilder));
                int pos = dataTextField.getCaretPosition();

                // 删除不必要的空格
                if (pos > 0 && dataTextField.getText().charAt(pos - 1) == ' ') {
                    if (e.getKeyChar() == KeyEvent.VK_SPACE) {
                        stringBuilder.delete(pos - 1, pos);
                    }
                }

                // 限制输入长度
                if (realLength <= maxLength) {
                    if (stringBuilder.length() > 0) {
                        if (stringBuilder.charAt(0) == ' ') {
                            stringBuilder.delete(0, 1);
                        }

                        // 格式化每隔两个字符插入一个空格
                        for (int i = 2; i < stringBuilder.length(); i += 3) {
                            if (stringBuilder.charAt(i) != ' ') {
                                stringBuilder.insert(i, " ");
                            }
                        }
                    }

                    // 更新文本字段并确保光标位置有效
                    dataTextField.setText(String.valueOf(stringBuilder).toUpperCase());
                    dataTextField.setCaretPosition(Math.min(pos, stringBuilder.length()));

                } else {
                    // 控制文本长度并确保光标位置有效
                    if (maxLength / 2 <= 1) {
                        dataTextField.setText(stringBuilder.substring(0, maxLength));
                    } else {
                        dataTextField.setText(stringBuilder.substring(0, maxLength + ((maxLength / 2) - 1)).toUpperCase());
                    }
                    dataTextField.setCaretPosition(Math.min(pos, dataTextField.getText().length()));
                }
            }
        });
    }

    private void dataEditEventHandler() {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                int number = Integer.parseInt(Objects.requireNonNull(dataLengthComboBox.getSelectedItem()).toString());
                StringBuilder text = new StringBuilder();
                if (!dataTextField.getText().isEmpty()) {
                    String[] num = dataTextField.getText().split(" ");
                    for (int i = 0; i < num.length; i++) {
                        if (num[i].trim().isEmpty()) {
                            num[i] = "00";
//                            System.out.println(num[i]);
                        }
                        num[i] = num[i].length() == 1 ? "0" + num[i].trim() : num[i].trim();
                        if (num[i].length() > 2) {
                            num[i] = num[i].substring(0, 2).trim();
                        }
                        text.append(num[i]).append(" ");
                    }
                    if (number > num.length) {
                        for (int i = 0; i < (number - num.length); i++) {
                            text.append("00 ");
                        }
                    }

                    dataTextField.setText(text.toString().toUpperCase());
                }
                CanMessageDataEditDialog dlg = new CanMessageDataEditDialog(FlexrayFrameSendPanel.this, number, dataTextField);
                dlg.setLocationRelativeTo(null);
                dlg.setVisible(true);
            }
        }, 50);

//        if (!dataTextField.getText().equals("")) {
//            String[] numString = dataTextField.getText().split(" ");
//            StringBuilder stringBuilder = new StringBuilder();
//            for (String s : numString) {
//                stringBuilder.append(s.toLowerCase());
//            }

//            byte[] bytes = hexToBytes(stringBuilder.toString());
//            System.out.println(Arrays.toString(bytes));
//        }
    }


    static class Option {
        private final String displayValue;
        @Getter
        private final int actualValue;

        public Option(String displayValue, int actualValue) {
            this.displayValue = displayValue;
            this.actualValue = actualValue;
        }

        @Override
        public String toString() {
            return displayValue;
        }

    }
}
