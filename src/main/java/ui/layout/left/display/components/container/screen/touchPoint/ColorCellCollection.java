package ui.layout.left.display.components.container.screen.touchPoint;

import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.reader.ObjectReader;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.lang.reflect.Type;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Data
@Slf4j
public class ColorCellCollection {

    // 自定义反序列化器
    public static class ColorDeserializer implements ObjectReader<String> {
        @Override
        public String readObject(JSONReader jsonReader, Type type, Object fieldName, long features) {
            // 读取Color对象的JSON表示 {"rgb":-11435968}
            try {
                JSONObject jsonObject = jsonReader.read(JSONObject.class);
                if (jsonObject != null && jsonObject.containsKey("rgb")) {
                    int rgbValue = jsonObject.getIntValue("rgb");
                    Color color = new Color(rgbValue);
                    // 返回颜色的十六进制字符串表示
                    return String.format("#%06x", color.getRGB() & 0xFFFFFF);
                }
                return null;
            } catch (JSONException e) {
                return null;
            }
        }
    }

    private String text;
    @JSONField(deserializeUsing = ColorDeserializer.class)
    private String color; // Represented as hex string, e.g., "#FFA500"
    private List<Cell> cells;

    private Color getRandomColor() {
        Random random = new Random();
        // 生成随机的 RGB 颜色，每个分量范围在 0-255 之间
        int red = random.nextInt(256);
        int green = random.nextInt(256);
        int blue = random.nextInt(256);

        return new Color(red, green, blue);
    }

    public Color getColor() {
        return color != null ? Color.decode(color) : getRandomColor();
//        try {
//            return color != null ? Color.decode(color) : getRandomColor();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return getRandomColor();
//        }
    }

    public void setColor(Color color) {
        // 使用局部变量存储颜色
        Color localColor = color != null ? color : getRandomColor();
        // 将颜色转换为 #RRGGBB 格式，并存储为字符串
        this.color = String.format("#%06x", localColor.getRGB() & 0xFFFFFF);
    }

    public Cell firstCell() {
        return cells == null ? null : cells.get(0);
    }

    public int maxRow() {
        return Collections.max(cells.stream().map(Cell::getRow).collect(Collectors.toList()));
    }

}