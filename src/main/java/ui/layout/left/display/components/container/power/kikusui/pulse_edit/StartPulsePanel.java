package ui.layout.left.display.components.container.power.kikusui.pulse_edit;

import com.alibaba.fastjson2.JSON;
import com.jgoodies.forms.factories.CC;
import com.jgoodies.forms.layout.FormLayout;
import common.constant.ResourceConstant;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import ui.layout.left.display.components.container.power.kikusui.WavePulsePanel;
import ui.layout.left.display.components.container.power.kikusui.entity.KikusuiWavePulse;
import ui.layout.left.display.components.container.power.kikusui.entity.StartPulse;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 菊水电源启动脉冲编辑面板
 */
public class StartPulsePanel extends WavePulsePanel {

    private final JCheckBox constantRandomCheckbox;
    private final JTextField constantValuesTextField;
    private final JLabel voltageRangeLabel = new JLabel("随机电压(V):");
    private final JLabel timeRangeLabel = new JLabel("随机脉冲时间(ms):");
    private final JLabel timesRangeLabel = new JLabel("随机脉冲次数:");
    private final JLabel symbolLabel0 = new JLabel("常量电压随机列表");
    private final JLabel symbolLabel1 = new JLabel("到");
    private final JLabel symbolLabel2 = new JLabel("到");
    private final JLabel symbolLabel3 = new JLabel("到");
    private JSpinner fromVoltageSpinner;
    private JSpinner toVoltageSpinner;
    private JSpinner fromTimeSpinner;
    private JSpinner toTimeSpinner;
    private JSpinner fromCycleSpinner;
    private JSpinner toCycleSpinner;
    private static final String delimeter = "&";

    public StartPulsePanel() {
        constantRandomCheckbox = new JCheckBox(String.format("常量随机(通过%s连接)", delimeter));
        constantValuesTextField = new JTextField(60);
        constantValuesTextField.setEnabled(false);
        setLayout(new BorderLayout());
        createSpinner();
        add(assemblePanel(), BorderLayout.CENTER);
        constantRandomCheckbox.addItemListener(e -> {
            fromVoltageSpinner.setEnabled(!constantRandomCheckbox.isSelected());
            toVoltageSpinner.setEnabled(!constantRandomCheckbox.isSelected());
            constantValuesTextField.setEnabled(constantRandomCheckbox.isSelected());
        });
    }

    public JPanel assemblePanel() {
        JPanel assemblePanel = new JPanel(new BorderLayout());

        JPanel leftPanel = new JPanel(new FormLayout("pref, 100px, 20px, 100px, pref", "30px, 30px, 30px, 30px, 30px"));
        leftPanel.add(constantRandomCheckbox, CC.xy(1, 1));

        leftPanel.add(symbolLabel0, CC.xy(1, 2));
        leftPanel.add(constantValuesTextField, CC.xyw(2, 2, 4));

        leftPanel.add(voltageRangeLabel, CC.xy(1, 3));
        leftPanel.add(fromVoltageSpinner, CC.xy(2, 3));
        leftPanel.add(symbolLabel1, CC.xy(3, 3));
        leftPanel.add(toVoltageSpinner, CC.xy(4, 3));

        leftPanel.add(timeRangeLabel, CC.xy(1, 4));
        leftPanel.add(fromTimeSpinner, CC.xy(2, 4));
        leftPanel.add(symbolLabel2, CC.xy(3, 4));
        leftPanel.add(toTimeSpinner, CC.xy(4, 4));

        leftPanel.add(timesRangeLabel, CC.xy(1, 5));
        leftPanel.add(fromCycleSpinner, CC.xy(2, 5));
        leftPanel.add(symbolLabel3, CC.xy(3, 5));
        leftPanel.add(toCycleSpinner, CC.xy(4, 5));
        JPanel leftParentPanel = new JPanel(new GridBagLayout());
        leftParentPanel.add(leftPanel, new GridBagConstraints());

        Icon icon = SwingUtil.getResourceAsImageIcon(ResourceConstant.DevicePictures.adsStartPulsePicture);
        JLabel imgLabel = new JLabel();
        imgLabel.setIcon(icon);

        JPanel rightPanel = new JPanel(new GridBagLayout());
        Insets insets = new Insets(0, 5, 0, 0);
        int anchor = GridBagConstraints.CENTER;
        int fill = GridBagConstraints.CENTER;
        double weightY = 0;
        double weightX = 0;
        rightPanel.add(imgLabel, new GridBagConstraints(0, 0, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));

        JSplitPane splitPane = SwingUtil.getSplitPane(leftParentPanel, rightPanel);

        assemblePanel.add(getMemoryPanel(), BorderLayout.NORTH);
        assemblePanel.add(splitPane, BorderLayout.CENTER);
        setFont();
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                splitPane.setDividerLocation(0.5);
            }
        });
        return assemblePanel;
    }


    public void setFont() {
        Font font = new Font(null, Font.BOLD, 16);
        voltageRangeLabel.setFont(font);
        timeRangeLabel.setFont(font);
        timesRangeLabel.setFont(font);
        constantRandomCheckbox.setFont(font);
        symbolLabel0.setFont(font);
        symbolLabel1.setFont(font);
        symbolLabel2.setFont(font);
        symbolLabel3.setFont(font);
    }

    public void createSpinner() {
        fromVoltageSpinner = new JSpinner(new SpinnerNumberModel(0, -33, 33, 0.1));
        SwingUtil.setPreferredWidth(fromVoltageSpinner, 150);
        toVoltageSpinner = new JSpinner(new SpinnerNumberModel(12, -33, 33, 0.1));
        SwingUtil.setPreferredWidth(toVoltageSpinner, 150);
        double MAX_TIME_SETTING = 1000000;
        fromTimeSpinner = new JSpinner(new SpinnerNumberModel(90, 0, MAX_TIME_SETTING, 1));
        SwingUtil.setPreferredWidth(fromTimeSpinner, 100);
        toTimeSpinner = new JSpinner(new SpinnerNumberModel(200, 0, MAX_TIME_SETTING, 1));
        SwingUtil.setPreferredWidth(toTimeSpinner, 100);

        fromCycleSpinner = new JSpinner(new SpinnerNumberModel(20, 0, 1000000, 1));
        SwingUtil.setPreferredWidth(fromCycleSpinner, 100);
        toCycleSpinner = new JSpinner(new SpinnerNumberModel(40, 0, 1000000, 1));
        SwingUtil.setPreferredWidth(toCycleSpinner, 100);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

    private void buildUI(KikusuiWavePulse kikusuiWavePulse) {
        StartPulse wavePulse = (StartPulse) kikusuiWavePulse;
        setMemoryPosition(wavePulse.getMemoryPosition());
        constantRandomCheckbox.setSelected(wavePulse.isConstantRandom());
        if (wavePulse.getConstantValues() != null) {
            constantValuesTextField.setText(wavePulse.getConstantValues().stream().map(Object::toString).collect(Collectors.joining(delimeter)));
        }
        fromVoltageSpinner.setValue(wavePulse.getFromVoltage());
        toVoltageSpinner.setValue(wavePulse.getToVoltage());
        fromTimeSpinner.setValue(wavePulse.getFromTime() * 1000);
        toTimeSpinner.setValue(wavePulse.getToTime() * 1000);
        fromCycleSpinner.setValue(wavePulse.getFromCycle());
        toCycleSpinner.setValue(wavePulse.getToCycle());
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) {
        StartPulse wavePulse = JSON.to(StartPulse.class,injectedOperation.getOperationObject());
        buildUI(wavePulse);
    }

    @Override
    public StartPulse assembleOperationObject() {
        StartPulse wavePulse = new StartPulse();
        wavePulse.setMemoryPosition(getMemoryPosition());
        wavePulse.setConstantRandom(constantRandomCheckbox.isSelected());
        wavePulse.setConstantValues(toNumberList(constantValuesTextField.getText()));
        wavePulse.setFromVoltage((Double) fromVoltageSpinner.getValue());
        wavePulse.setToVoltage((Double) toVoltageSpinner.getValue());
        wavePulse.setFromTime((Double) fromTimeSpinner.getValue() / 1000);
        wavePulse.setToTime((Double) toTimeSpinner.getValue() / 1000);
        wavePulse.setFromCycle((Integer) fromCycleSpinner.getValue());
        wavePulse.setToCycle((Integer) toCycleSpinner.getValue());
        return wavePulse;
    }

    private java.util.List<Float> toNumberList(String text) {
        String[] parts = text.split(delimeter);
        java.util.List<Float> list = new ArrayList<>();

        for (String part : parts) {
            try {
                Float number = Float.parseFloat(part); // 这里使用 Double 类型来统一处理数字类型，也可以根据实际需求选择其他数字类型
                list.add(number);
            } catch (NumberFormatException e) {
                // 捕捉格式转换异常，如无法解析为数字，可以根据实际情况处理
            }
        }
        return list;
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationMethod(DeviceMethods.writeStartWavePulse);
        injectedOperation.setOperationObject(assembleOperationObject());
        return injectedOperation;
    }

}
