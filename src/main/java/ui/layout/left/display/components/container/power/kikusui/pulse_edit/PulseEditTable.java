package ui.layout.left.display.components.container.power.kikusui.pulse_edit;

import common.constant.KeyStrokeConstants;
import lombok.Getter;
import ui.base.table.DefaultTable;
import ui.callback.TableMenuCallback;
import ui.layout.left.display.components.container.power.kikusui.entity.CustomizedPulseSegment;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.event.TableModelEvent;
import javax.swing.event.TableModelListener;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumn;
import java.awt.*;
import java.awt.event.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ui.layout.left.display.components.container.power.kikusui.pulse_edit.PulseEditTableHeaderValue.*;
import static ui.layout.left.display.components.container.power.kikusui.pulse_edit.PulseEditTableValue.*;

/**
 * 自定义编辑波形表格
 */
public class PulseEditTable extends DefaultTable<CustomizedPulseSegment>
        implements TableMenuCallback<CustomizedPulseSegment> {
    public static final int MAX_ROW_NUM = 1024;
    private final int maxRowNumber;
    private final DefaultTableModel tableModel;
    private final Color UNEDITABLE_COLOR = new Color(238, 238, 238);
    @Getter
    public Boolean clear = false;
    @Getter
    public Vector<String> ACVector;
    @Getter
    public JComboBoxCellEditor transitionComboBoxCellEditor;
    @Getter
    public JComboBoxCellEditor acComboBoxCellEditor;
    @Getter
    private Boolean clearAmplitudeAndFrequency = false;
    private String lastInterval;
    private final double Biggest_Interval = 99999999.9;
    private Boolean isClearAmplitudeAndFrequency = false;
    private String tableContent;
    private final List<CustomizedPulseSegment> clipBoard = new ArrayList<>();
    private boolean isVoltageSet;
    private MainModel mainModel;

    @Override
    protected String[] getColumns() {
        return new String[0];
    }

    /**
     * 初始化Table内部数据模型tableList
     *
     * @param maxRowNum 最大行数
     */
    private void initTableModelList(int maxRowNum) {
        for (int i = 0; i < maxRowNum; i++) {
            getTableList().add(new CustomizedPulseSegment());
        }
    }

    public PulseEditTable(MainModel mainModel, int maxRowNum, Object[][] data, Object[] columnTitle) {
        super(new DefaultTableModel(data, columnTitle) {
            @Override
            public boolean isCellEditable(int row, int column) {
                if (column == COLUMN_START_V)
                    return getValueAt(row, column - 1) != null && !getValueAt(row, column - 1).equals(TRANSITION_STEP)
                            && !getValueAt(row, column - 1).equals("");

                if (column == COLUMN_START_VPP || column == COLUMN_END_VPP || column == COLUMN_START_HZ || column == COLUMN_END_HZ || column == COLUMN_PHASE_DEG)
                    return getValueAt(row, COLUMN_AC) != null && !getValueAt(row, COLUMN_AC).equals("") && !getValueAt(row, COLUMN_AC).equals(AC_OFF);

                if (row > 0)
                    if (getValueAt(row - 1, 1) == null || getValueAt(row - 1, 1).equals(""))
                        return false;
                return column != 0 && column != 1 && column != COLUMN_WAVEFORM && column != COLUMN_F_SWEEP;
            }
        });
        this.mainModel = mainModel;
        maxRowNumber = maxRowNum;
        tableModel = (DefaultTableModel) getModel();
        tableInit();
        initTableModelList(maxRowNum);
        // 获取表头,设置表头的宽高
        setTableHeaderSize();
        // 设置表格行高
        setRowHeight(30);
        // 获取某一列,设置宽度
        setTableColumnWidth();
        // 设置文字对齐方式:居中显示
        setTextAlign(JLabel.CENTER);
        setComboBoxCellEditor();
        setUneditableColor(0);
        setUneditableColor(1);
        createMenu();
//        createHeadMenu();
        createActions();
        mainModel.getKikusuiModel().updateContent(getCustomizePulseSegmentList());

    }

    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    public void createActions() {
        super.createActions();
        createActionListener();
        createHeaderActionListener();
    }

    @Override
    protected Object[] convertData(CustomizedPulseSegment customizedPulseSegment) {
        return new Object[0];
    }

    @Override
    public void setValueAt(Object aValue, int row, int column) {
        if (aValue != null && !aValue.equals("")) {
            CustomizedPulseSegment customizedPulseSegment = getTableList().get(row);
            if (column == COLUMN_VOLTAGE) {
                if (aValue instanceof Float) {
                    customizedPulseSegment.setVoltage((Float) aValue);
                } else {
                    customizedPulseSegment.setVoltage(Float.parseFloat((String) aValue));
                }
            } else if (column == COLUMN_INTERVAL) {
                if (aValue instanceof Float) {
                    customizedPulseSegment.setInterval((Float) aValue / 1000.0);
                } else {
                    customizedPulseSegment.setInterval(Float.parseFloat((String) aValue) / 1000.0);
                }
            } else if (column == COLUMN_TRANSITION) {
                customizedPulseSegment.setTransition((String) aValue);
            } else if (column == COLUMN_START_V) {
                if (customizedPulseSegment.getTransition() != null) {
                    if (customizedPulseSegment.getTransition().equals("Ramp")) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            if (aValue instanceof String) {
                                customizedPulseSegment.setStartVoltage(Float.parseFloat((String) aValue));
                            } else if (aValue instanceof Float) {
                                customizedPulseSegment.setStartVoltage((Float) aValue);
                            }
                        }
                    } else if (customizedPulseSegment.getTransition().equals("Step")) {
                        customizedPulseSegment.setStartVoltage(0.0f);
                    }
                } else {
                    customizedPulseSegment.setStartVoltage(0.0f);
                }
            } else if (column == COLUMN_AC) {
                customizedPulseSegment.setAc((String) aValue);
                if (aValue != null) {
                    if (aValue.equals(AC_ON)) {
                        customizedPulseSegment.setFSweep("Linear");
                    } else if (aValue.equals(AC_OFF)) {
                        customizedPulseSegment.setFSweep(null);
                    }
                }
            } else if (column == COLUMN_WAVEFORM) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        customizedPulseSegment.setWaveform((String) aValue);
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setWaveform(null);
                    }
                }
            } else if (column == COLUMN_START_VPP) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            customizedPulseSegment.setStartVpp(Float.parseFloat((String) aValue));
                        }
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setStartVpp(0.0f);
                    }
                }
            } else if (column == COLUMN_END_VPP) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            customizedPulseSegment.setEndVpp(Float.parseFloat((String) aValue));
                        }
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setEndVpp(0.0f);
                    }
                }
            } else if (column == COLUMN_START_HZ) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            customizedPulseSegment.setStartHz(Float.parseFloat((String) aValue));
                        }
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setStartHz(0.0f);
                    }
                }
            } else if (column == COLUMN_END_HZ) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            customizedPulseSegment.setEndHz(Float.parseFloat((String) aValue));
                        }
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setEndHz(0.0f);
                    }
                }
            } else if (column == COLUMN_PHASE_DEG) {
                if (customizedPulseSegment.getAc() != null) {
                    if (customizedPulseSegment.getAc().equals(AC_ON)) {
                        if (aValue != null && !aValue.equals("") && !aValue.equals("—")) {
                            customizedPulseSegment.setPhase(Float.parseFloat((String) aValue));
                        }
                    } else if (customizedPulseSegment.getAc().equals(AC_OFF)) {
                        customizedPulseSegment.setPhase(0.0f);
                    }
                }
            }
        }
        super.setValueAt(aValue, row, column);
    }

    @Override
    protected void createMenu() {
        super.createMenu();
        addInsertMenu();
        addDelMenu();
        addCopyRowsMenu();
        addPasteRowsMenu();
        addClearMenu();
    }

    protected void addInsertMenu() {
        makePopupMenu("插入新一行", null, e -> insertRowActivated());
    }

    protected void addCopyRowsMenu() {
        makePopupMenu("复制", KeyStrokeConstants.COPY_KEY_STROKE,
                e -> copyRowsActivated());
    }

    protected void addPasteRowsMenu() {
        makePopupMenu("粘贴", KeyStrokeConstants.PASTE_KEY_STROKE,
                e -> pasteRowsActivated());
    }

    public void insertRowActivated() {
        int currentRow = getSelectedRow();
        int dataLength = getDataLength();
        List<CustomizedPulseSegment> getDataAfterPasteRow = getDataAfterPasteRow(currentRow + 1, dataLength);
//        setCompletedRowsValue(new CustomizedPulseSegment(),currentRow+1);
        for (int i = 0; i < tableModel.getColumnCount(); i++) {
            tableModel.setValueAt("", currentRow + 1, i); // 清空单元格
        }
        insertData(currentRow + 2, getDataAfterPasteRow);
    }


    @Override
    public void clearTable() {
        for (CustomizedPulseSegment customizedPulseSegment : getTableList()) {
            customizedPulseSegment.clear();
        }
        // defaultTableModel.getDataVector().removeAllElements();
        tableModel.getDataVector().clear();
        clear = true;
        tableModel.removeTableModelListener(this);
        updateUI();
        tableModel.fireTableDataChanged();
        tableModel.addTableModelListener(this);
        clear = false;
    }


    @Override
    public boolean clearTableActivated() {
        int result = JOptionPane.showConfirmDialog(null,
                "确定清空所有单元格？（该操作无法恢复，请谨慎！）", "清空表格",
                JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            clearTable();
            return true;
        }
        return false;
    }


    @Override
    public void copyRowsActivated() {
        List<CustomizedPulseSegment> selectedRowsData = getSelectedRowsData();
        CustomizedPulseSegment firstCustomizedPulseSegment = selectedRowsData.get(0);
        boolean isFirstRowCompleted = isCompletedOfCustomizedPulseSegment(firstCustomizedPulseSegment);
        CustomizedPulseSegment lastCustomizedPulseSegment = selectedRowsData.get(selectedRowsData.size() - 1);
        boolean isLastRowCompleted = isCompletedOfCustomizedPulseSegment(lastCustomizedPulseSegment);
        if (isFirstRowCompleted) {
            allowCopy(isLastRowCompleted);
        }
    }

    @Override
    public void pasteRowsActivated() {
        int dataLength = getDataLength();
        int currentRow = getSelectedRow();
        if (isCellEditable(currentRow, 2)) {
            allowPaste(currentRow, dataLength);
        }
    }

    public void insertData(int insertPosition, List<CustomizedPulseSegment> list) {
        for (int i = 0; i < list.size(); i++) {
            int row = insertPosition + i;
            CustomizedPulseSegment customizedPulseSegment = list.get(i);
            if (i == list.size() - 1) {
                if (isCompletedOfCustomizedPulseSegment(customizedPulseSegment)) {
                    setCompletedRowsValue(customizedPulseSegment, row);
                } else if (!isCompletedOfCustomizedPulseSegment(customizedPulseSegment)) {
                    setUncompletedRowsValue(customizedPulseSegment, row);
                }
            } else {
                setCompletedRowsValue(customizedPulseSegment, row);
            }
        }
    }

    public List<CustomizedPulseSegment> getDataAfterPasteRow(int currentRow, int dataLength) {
        List<CustomizedPulseSegment> dataAfterPasteRow = new ArrayList<>();
        selectivelyDeepCopyOfList(dataAfterPasteRow, getTableList(), currentRow, dataLength);
        return dataAfterPasteRow;
    }

    public int getDataLength() {
        int dataLength = 0;
        for (int i = 0; i < getTableList().size(); i++) {
            CustomizedPulseSegment customizedPulseSegment = getTableList().get(i);
            float voltage = customizedPulseSegment.getVoltage();
            double interval = customizedPulseSegment.getInterval();
            String transition = customizedPulseSegment.getTransition();
            String ac = customizedPulseSegment.getAc();
            if (transition != null) {
                dataLength++;
                continue;
            }
            if (ac != null) {
                dataLength++;
                continue;
            }
            if (tableModel.getValueAt(i, 2) == null) break;
            isVoltageSet = tableModel.getValueAt(i, 2).equals("0.0");
            if (voltage == 0 && !isVoltageSet && interval == 0 && transition == null && ac == null) {
                break;
            }
            if (voltage != 0 || isVoltageSet) {
                dataLength++;
                continue;
            }
            if (interval != 0) {
                dataLength++;
            }

        }
        return dataLength;
    }

    //    删除一行
    @Override
    public int deleteRow(int row) {
        tableModel.removeTableModelListener(this);
        if (getValueAt(row, 1) != null) {
            int userChoice = JOptionPane.showConfirmDialog(null,
                    "删除这一行数据", "提示", JOptionPane.YES_NO_OPTION);
            if (userChoice == JOptionPane.YES_OPTION) {
                clearAmplitudeAndFrequency = true;
                updateTableViewWhenDelRow(row);
                clearAmplitudeAndFrequency = false;
                getTableList().get(row).clear();
            }
//            if (!getValueAt(row, 1).equals("") && !getValueAt(row, 2).equals("") && !getValueAt(row, 3).equals("")) {
//                int userChoice = JOptionPane.showConfirmDialog(null, "删除这一行数据", "提示", JOptionPane.YES_NO_OPTION);
//                clearAmplitudeAndFrequency = true;
//                if (userChoice == 0) {
//                    for (int i = 1; i < getColumnCount(); i++)
//                        setValueAt("", row, i);
//                    for (int i = row + 1; i < MAX_ROW_NUM - row; i++) {
//                        int[] findFilledRow = {i, 0};
//                        for (int j = 1; j < getColumnCount(); j++)
//                            if (getValueAt(i, j) != null) findFilledRow[1]++;
//                        if (findFilledRow[1] == 13) for (int k = 1; k < getColumnCount(); k++)
//                            setValueAt(getValueAt(i, k), i - 1, k);
//                        if (findFilledRow[1] != 13) {
//                            for (int k = 1; k < getColumnCount(); k++) {
//                                setValueAt("", i - 1, k);
//                                setValueAt("", i, k);
//                            }
//                            break;
//                        }
//                    }
////                    updateUI();
////
//                }
//                clearAmplitudeAndFrequency = false;
//            }
        }
        tableModel.addTableModelListener(this);
        mainModel.getKikusuiModel().updateContent(getCustomizePulseSegmentList());
        return row;
    }

    @Override
    public void deleteRowsActivated(int[] rows) {
        for (int row : rows) {
            if (isCellEditable(row, 2)) {
                deleteRow(row);
            }
        }
    }

    public void setTextAlign(int position) {
        DefaultTableCellRenderer defaultTableCellRenderer = new DefaultTableCellRenderer();
        defaultTableCellRenderer.setHorizontalAlignment(position);
        setDefaultRenderer(Object.class, defaultTableCellRenderer);
    }

    public void setTableColumnWidth() {
        TableColumn firstColumn = this.getColumnModel().getColumn(0);
        int CELL_WIDTH = 35;
        firstColumn.setPreferredWidth(CELL_WIDTH);
        firstColumn.setMaxWidth(CELL_WIDTH);
        firstColumn.setMinWidth(CELL_WIDTH);
    }

    public void setTableHeaderSize() {
        JTableHeader tableHeader = getTableHeader();
        tableHeader.setReorderingAllowed(true);
        tableHeader.setPreferredSize(new Dimension((int) getPreferredSize().getWidth(), 30));
        // 设置表头不能拖动
        tableHeader.setReorderingAllowed(false);
    }

    public void setUneditableColor(int columnIndex) {
        DefaultTableCellRenderer defaultTableCellRenderer = new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component cell = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                cell.setBackground(UNEDITABLE_COLOR);
                return cell;
            }
        };
        defaultTableCellRenderer.setHorizontalAlignment(JLabel.CENTER);
        getColumnModel().getColumn(columnIndex).setCellRenderer(defaultTableCellRenderer);
    }

    @Override
    public void addRowData(int row, CustomizedPulseSegment segment) {
        setValueAt(String.valueOf(segment.getVoltage()), row, COLUMN_VOLTAGE);
        setValueAt(String.valueOf(segment.getInterval() * 1000), row, COLUMN_INTERVAL);
        setValueAt(String.valueOf(segment.getTransition()), row, COLUMN_TRANSITION);
        setValueAt(String.valueOf(segment.getStartVoltage()), row, COLUMN_START_V);
    }

    protected void buildUI(List<CustomizedPulseSegment> wavePulses) {
        if (wavePulses == null) {
            return;
        }
        for (int row = 0; row < wavePulses.size(); row++) {
            CustomizedPulseSegment segment = wavePulses.get(row);
            if (segment.isValid()) {
                addRowData(row, segment);
            }
        }
        updateUI();
    }

    public List<CustomizedPulseSegment> getCustomizePulseSegmentList() {
        return getTableList().stream().filter(CustomizedPulseSegment::isValid).collect(Collectors.toList());
    }

    public void createActionListener() {
        tableModel.addTableModelListener(new TableModelListener() {
            @Override
            public void tableChanged(TableModelEvent e) {
                if (!getClear()) {
                    int row = e.getFirstRow();
                    int column = e.getColumn();
                    if (row == -1 || column == -1) {
                        return;
                    }
                    Object currentContent = tableModel.getValueAt(row, column);
                    if (column == 2 || column == COLUMN_INTERVAL | column == COLUMN_START_VPP || column == COLUMN_END_VPP || column == COLUMN_START_HZ || column == COLUMN_END_HZ || column == COLUMN_PHASE_DEG) {
                        tableModel.removeTableModelListener(this);
                        if (column == COLUMN_START_VPP || column == COLUMN_END_VPP || column == COLUMN_START_HZ || column == COLUMN_END_HZ || column == COLUMN_PHASE_DEG) {
                            if (getClearAmplitudeAndFrequency()) {
                                isClearAmplitudeAndFrequency = true;
                            }
                            if ((currentContent == null || currentContent.equals("")) && !isClearAmplitudeAndFrequency) {
                                tableModel.setValueAt(0.0, row, column);
                            } else {
                                tableModel.setValueAt(dataValidate((String) currentContent), row, column);
                            }
                        } else {
                            tableModel.setValueAt(dataValidate((String) currentContent), row, column);
                        }
                        tableModel.addTableModelListener(this);
                    }
                    if (column == COLUMN_START_V) {
                        if (!(currentContent.equals("") && currentContent.equals("—"))) {
                            tableModel.removeTableModelListener(this);
                            tableModel.setValueAt(dataValidate(currentContent.toString()), row, column);
                            tableModel.addTableModelListener(this);
                        }
                    }
                    Object voltage = getValueAt(row, 2);
                    Object interval = getValueAt(row, COLUMN_INTERVAL);
                    Object transition = getValueAt(row, COLUMN_TRANSITION);
                    boolean isVolNull = voltage == null || voltage.equals("");
                    boolean isIntervalNull = interval == null || interval.equals("");
                    boolean isTransitionNull = transition == null || transition.equals("");

                    if (!isVolNull && !isIntervalNull && !isTransitionNull) {
                        double time;
                        if (row == 0) {
                            time = 0;
                        } else {
                            String value = getValueAt(row - 1, 1).toString();
                            time = value == null || value.isEmpty() ? 0 : Double.parseDouble(getValueAt(row - 1, 1).toString());
                        }
                        tableModel.removeTableModelListener(this);
                        tableModel.setValueAt(BigDecimal.valueOf(time + Double.parseDouble(interval.toString())), row, 1);
                        if (tableModel.getValueAt(row, COLUMN_AC) == null || tableModel.getValueAt(row, COLUMN_AC).equals("")) {
//                            Todo
                            syncTableListWhenRowAutoFill(row, COLUMN_AC);
                            getAcComboBoxCellEditor().addComboBox(getACVector());
                            tableModel.setValueAt(AC_OFF, row, COLUMN_AC);
                            tableModel.setValueAt("—", row, COLUMN_WAVEFORM);
                            tableModel.setValueAt("—", row, COLUMN_START_VPP);
                            tableModel.setValueAt("—", row, COLUMN_END_VPP);
                            tableModel.setValueAt("—", row, COLUMN_START_HZ);
                            tableModel.setValueAt("—", row, COLUMN_END_HZ);
                            tableModel.setValueAt("—", row, COLUMN_F_SWEEP);
                            tableModel.setValueAt("—", row, COLUMN_PHASE_DEG);
                        }
                        tableModel.addTableModelListener(this);
                        if (column == COLUMN_INTERVAL) {
                            int findFilledRow = 0;
                            for (int i = 0; i < 100; i++) {
                                if (getValueAt(i, 1) == null) {
                                    findFilledRow = i - 1;
                                    break;
                                }
                            }
                            for (int i = 0; i <= findFilledRow; i++) {
                                setValueAt(BigDecimal.valueOf(time + Double.parseDouble(interval.toString())), i, 1);
                            }
                        }
                    } else {
                        tableModel.removeTableModelListener(this);
                        tableModel.setValueAt("", row, 1);
                        tableModel.addTableModelListener(this);
                    }
                    if (column == COLUMN_INTERVAL) {
                        assert interval != null;
                        if (!interval.equals("")) {
                            if (Double.parseDouble(interval.toString()) > Biggest_Interval) {
                                tableModel.removeTableModelListener(this);
                                tableModel.setValueAt(lastInterval, row, COLUMN_INTERVAL);
                                tableModel.addTableModelListener(this);
                            }
                        }
                    }
                    if (column == COLUMN_TRANSITION) {
                        tableModel.removeTableModelListener(this);
                        if (getValueAt(row, COLUMN_TRANSITION) != null && getValueAt(row, COLUMN_TRANSITION).equals(TRANSITION_STEP)) {
                            setValueAt("—", row, COLUMN_START_V);
                        }
                        if (getValueAt(row, COLUMN_TRANSITION) != null && getValueAt(row, COLUMN_TRANSITION).equals(TRANSITION_RAMP)) {
                            if (row == 0) {
                                setValueAt(dataValidate("0"), row, COLUMN_START_V);
                            } else {
                                setValueAt(dataValidate((String) getValueAt(row - 1, COLUMN_VOLTAGE)), row, COLUMN_START_V);
                            }
                        }

                        tableModel.addTableModelListener(this);
                    }
                    if (column == COLUMN_AC) {
                        tableModel.removeTableModelListener(this);
                        if (getValueAt(row, COLUMN_AC) != null && getValueAt(row, COLUMN_AC).equals(AC_ON)) {
                            setValueAt("Sine", row, COLUMN_WAVEFORM);
                            setValueAt("0.0", row, COLUMN_START_VPP);
                            setValueAt("0.0", row, COLUMN_END_VPP);
                            setValueAt("0.0", row, COLUMN_START_HZ);
                            setValueAt("0.0", row, COLUMN_END_HZ);
                            setValueAt("Linear", row, COLUMN_F_SWEEP);
                            setValueAt("0.0", row, COLUMN_PHASE_DEG);
                        }
                        tableModel.addTableModelListener(this);
                    }
                    mainModel.getKikusuiModel().updateContent(getCustomizePulseSegmentList());
                } else {
                    clearTableAndInitTable(this);
                }
            }
        });
        addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                super.keyPressed(e);
                if (e.getKeyCode() == 127) {
                    int row = getSelectedRow();
                    int column = getSelectedColumn();
                    //1.整行删除
                    deleteRowByPressDelete(row, column);
                    //2.按下delete不允许删除
                    forbidDeleteByPressDelete(row, column);
                }
            }
        });
//
        addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                super.mousePressed(e);
                //TODO
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                int row = getSelectedRow();
                int column = getSelectedColumn();
                Object currentContent = getValueAt(row, column);
                if (column == COLUMN_INTERVAL) {
                    if (currentContent == null || currentContent.equals("")) {
                        lastInterval = "0";
                        setValueAt("", row, column);
                    } else {
                        lastInterval = currentContent.toString();
                    }
                }
                if ((column == 0 || column == 1 || column == COLUMN_START_V || column == COLUMN_WAVEFORM || column == COLUMN_F_SWEEP) && currentContent != null)
                    tableContent = currentContent.toString();
                if (getValueAt(row, COLUMN_TRANSITION) != null && getValueAt(row, COLUMN_TRANSITION).equals(TRANSITION_STEP))
                    setValueAt("—", row, COLUMN_START_V);
            }
        });

    }

    public void createHeaderActionListener() {
        getTableHeader().addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                super.mousePressed(e);
                if (SwingUtilities.isRightMouseButton(e)) {
                    getPopupMenu().show(getTableHeader(), e.getX(), e.getY());
                }
            }
        });
    }

    public String dataValidate(String str) {
        if (!Objects.equals(str, "")) {
            if (!str.equals("—")) {
                str = str + ".0";
                str = Pattern.compile("[^0-9.-]").matcher(str).replaceAll("");
                str = Pattern.compile("^0+").matcher(str).replaceAll("0");
                Matcher matcher = Pattern.compile("^(0+)([1-9]*)$").matcher(str);
                while (matcher.find()) {
                    if (!matcher.group(2).isEmpty()) {
                        str = matcher.group(2);
                    }
                }
                str = Pattern.compile("^\\.").matcher(str).replaceAll("");
                Matcher matcher1 = Pattern.compile("-?\\d+\\.\\d*").matcher(str);
                while (matcher1.find()) {
                    str = matcher1.group(0);
                }
                if (str.equals("0")) str = str + ".0";
            }
        }
        return str;
    }

    public void deleteRowByPressDelete(int row, int column) {
        if (getValueAt(row, 1) != null) {
            if (!getValueAt(row, 1).equals("") && !getValueAt(row, 2).equals("") && !getValueAt(row, COLUMN_INTERVAL).equals("")) {
                int userChoice = JOptionPane.showConfirmDialog(null, "删除这一行数据", "提示", JOptionPane.YES_NO_OPTION);
                isClearAmplitudeAndFrequency = true;
                if (userChoice == 0) {
                    for (int i = 1; i < getColumnCount(); i++)
                        setValueAt("", row, i);
                    for (int i = row + 1; i < maxRowNumber - row; i++) {
                        int[] findFilledRow = {i, 0};
                        for (int j = 1; j < getColumnCount(); j++)
                            if (getValueAt(i, j) != null) findFilledRow[1]++;
                        if (findFilledRow[1] == COLUMN_PHASE_DEG) for (int k = 1; k < getColumnCount(); k++)
                            setValueAt(getValueAt(i, k), i - 1, k);
                        if (findFilledRow[1] != COLUMN_PHASE_DEG) {
                            for (int k = 1; k < getColumnCount(); k++) {
                                setValueAt("", i - 1, k);
                                setValueAt("", i, k);
                            }
                            break;
                        }
                    }
                }
                isClearAmplitudeAndFrequency = false;
            } else {
                setValueAt("", row, column);
            }
        }
    }

    public void forbidDeleteByPressDelete(int row, int column) {
        if (column == 0 || column == 1) setValueAt(tableContent, row, column);
        if (column == COLUMN_START_V && getValueAt(row, COLUMN_TRANSITION).equals(TRANSITION_STEP))
            setValueAt(tableContent, row, column);
        if ((column == COLUMN_WAVEFORM || column == COLUMN_F_SWEEP) && getValueAt(row, COLUMN_AC).equals(AC_ON))
            setValueAt(tableContent, row, column);
        if ((column >= COLUMN_WAVEFORM && column <= COLUMN_PHASE_DEG) && getValueAt(row, COLUMN_AC).equals(AC_OFF))
            setValueAt(tableContent, row, column);

    }

    public void clearTableAndInitTable(TableModelListener tableModelListener) {
        tableModel.removeTableModelListener(tableModelListener);
        tableInit();
//        pulseEditTable.getComboBoxCellEditor().getComboBoxList().clear();
        getTransitionComboBoxCellEditor().initComboBox(maxRowNumber);
        getAcComboBoxCellEditor().initComboBox(maxRowNumber);
        tableModel.addTableModelListener(tableModelListener);
    }

    public void tableInit() {
//        设置表格的列数和行数
        int COL_NUM = 14;
        tableModel.setColumnCount(COL_NUM);
        tableModel.setRowCount(maxRowNumber);
//        设置行号
        for (int i = 1; i <= tableModel.getRowCount(); i++) {
            tableModel.setValueAt(i, i - 1, 0);
        }
    }

    public void setComboBoxCellEditor() {
        // 设置下拉菜单
        Vector<String> transitionVector = new Vector<>();
        transitionVector.add(TRANSITION_STEP);
        transitionVector.add(TRANSITION_RAMP);
        ACVector = new Vector<>();
        ACVector.add(AC_ON);
        ACVector.add(AC_OFF);
//        setComboBox(COLUMN_TRANSITION , transitionVector, tableModel);
        transitionComboBoxCellEditor = new JComboBoxCellEditor(transitionVector, tableModel);
        getColumnModel().getColumn(COLUMN_TRANSITION).setCellEditor(transitionComboBoxCellEditor);
        acComboBoxCellEditor = new JComboBoxCellEditor(ACVector, tableModel);
        getColumnModel().getColumn(COLUMN_AC).setCellEditor(acComboBoxCellEditor);
//        setComboBox(COLUMN_AC, ACVector, tableModel);
    }

    public void updateTableListWhenDelRow(int row) {
        int dataLength = getDataLength();
//        当前行被删除 从row+1开始截取
        List<CustomizedPulseSegment> dataAfterPasteRow = getDataAfterPasteRow(row + 1, dataLength);
        for (int i = 0; i < dataAfterPasteRow.size(); i++) {
            getTableList().set(row + i, dataAfterPasteRow.get(i));
        }
        getTableList().set(dataLength - 1, new CustomizedPulseSegment());
    }

    public void updateTableViewWhenDelRow(int row) {
        List<JComboBox<String>> acComboBoxList = getAcComboBoxCellEditor().getComboBoxList();
        List<JComboBox<String>> transitionComboBoxList = getTransitionComboBoxCellEditor().getComboBoxList();
        if (acComboBoxList.get(row) != null) acComboBoxList.set(row, null);
        if (transitionComboBoxList.get(row) != null) transitionComboBoxList.set(row, null);
        int columnCount = getColumnCount();
        for (int i = 1; i < columnCount; i++) {
            setValueAt("", row, i);
        }
        for (int i = row + 1; i <= MAX_ROW_NUM; i++) {
//                    getAcComboBoxCellEditor().getComboBoxList().set(i-1,)
            acComboBoxList.set(i - 1, acComboBoxList.get(i));
            transitionComboBoxList.set(i - 1, transitionComboBoxList.get(i));
            int[] findFilledRow = {i, 0};
            for (int j = 1; j < columnCount; j++)
                if (getValueAt(i, j) != null) findFilledRow[1]++;
            if (findFilledRow[1] == 13) for (int k = 1; k < getColumnCount(); k++)
                setValueAt(getValueAt(i, k), i - 1, k);
            if (findFilledRow[1] != 13) {
                for (int k = 1; k < columnCount; k++) {
//                            未填完整的一行 保留已填入的数据
                    if (getValueAt(i, k) != null) {
                        setValueAt(getValueAt(i, k), i - 1, k);
                    } else {
                        setValueAt("", i - 1, k);
                    }
                    setValueAt("", i, k);
                    if (acComboBoxList.get(row) != null) acComboBoxList.set(row, null);
                    if (transitionComboBoxList.get(row) != null) transitionComboBoxList.set(row, null);
                }
                break;
            }
        }
        updateUI();
    }

    public boolean isCompletedOfCustomizedPulseSegment(CustomizedPulseSegment customizedPulseSegment) {
        float voltage = customizedPulseSegment.getVoltage();
        double interval = customizedPulseSegment.getInterval();
        String transition = customizedPulseSegment.getTransition();
        return voltage != 0.0 && interval != 0.0 && transition != null;
    }

    public void allowCopy(boolean isLastRowCompleted) {
        int[] selectedRows = getSelectedRows();
        int[] selectedCompletedRow;
        String tip;
        if (!isLastRowCompleted) {
            selectedCompletedRow = new int[selectedRows.length - 1];
            System.arraycopy(selectedRows, 0, selectedCompletedRow, 0, selectedCompletedRow.length);
        } else {
            selectedCompletedRow = selectedRows;
        }
        if (selectedCompletedRow.length == 1) {
            tip = "复制第" + (selectedCompletedRow[0] + 1) + "行的数据？";
        } else {
            tip = "复制第" + (selectedCompletedRow[0] + 1) + "行到第" + (selectedCompletedRow[selectedCompletedRow.length - 1] + 1) + "行的数据？";
        }
        int result = JOptionPane.showConfirmDialog(null,
                tip, "复制",
                JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            clipBoard.clear();
//            只复制填写完整的行
//TODO 深拷贝
            List<CustomizedPulseSegment> deepCopySource = getSelectedRowsData();
            selectivelyDeepCopyOfList(clipBoard, deepCopySource, 0, deepCopySource.size());
            if (!isLastRowCompleted) clipBoard.remove(clipBoard.size() - 1);
        }
    }

    public void allowPaste(int currentRow, int dataLength) {
        List<CustomizedPulseSegment> getDataAfterPasteRow = getDataAfterPasteRow(currentRow, dataLength);
//      插入剪切板内容
        insertData(currentRow, clipBoard);
//      插入指定的行后面的内容
        insertData(currentRow + clipBoard.size(), getDataAfterPasteRow);
        mainModel.getKikusuiModel().updateContent(getCustomizePulseSegmentList());
    }

    public void setCompletedRowsValue(CustomizedPulseSegment customizedPulseSegment, int row) {
        setValueAt(String.valueOf(customizedPulseSegment.getVoltage()), row, COLUMN_VOLTAGE);
        setValueAt(String.valueOf((customizedPulseSegment.getInterval() * 1000)), row, COLUMN_INTERVAL);
        setValueAt(String.valueOf(customizedPulseSegment.getTransition()), row, COLUMN_TRANSITION);
        if (Objects.equals(String.valueOf(customizedPulseSegment.getTransition()), "Step")) {
            setValueAt("—", row, COLUMN_START_V);
        } else {
            setValueAt(String.valueOf(customizedPulseSegment.getStartVoltage()), row, COLUMN_START_V);
        }
        if (customizedPulseSegment.getAc() == null || Objects.equals(customizedPulseSegment.getAc(), "off")) {
            setValueAt("off", row, COLUMN_AC);
            setValueAt("—", row, COLUMN_WAVEFORM);
            setValueAt("—", row, COLUMN_START_VPP);
            setValueAt("—", row, COLUMN_END_VPP);
            setValueAt("—", row, COLUMN_START_HZ);
            setValueAt("—", row, COLUMN_END_HZ);
            setValueAt("—", row, COLUMN_F_SWEEP);
            setValueAt("—", row, COLUMN_PHASE_DEG);
        } else {
            setValueAt(customizedPulseSegment.getAc(), row, COLUMN_AC);
            setValueAt(String.valueOf(customizedPulseSegment.getWaveform()), row, COLUMN_WAVEFORM);
            setValueAt(String.valueOf(customizedPulseSegment.getStartVpp()), row, COLUMN_START_VPP);
            setValueAt(String.valueOf(customizedPulseSegment.getEndVpp()), row, COLUMN_END_VPP);
            setValueAt(String.valueOf(customizedPulseSegment.getStartHz()), row, COLUMN_START_HZ);
            setValueAt(String.valueOf(customizedPulseSegment.getEndHz()), row, COLUMN_END_HZ);
            setValueAt(String.valueOf(customizedPulseSegment.getFSweep()), row, COLUMN_F_SWEEP);
            setValueAt(String.valueOf(customizedPulseSegment.getPhase()), row, COLUMN_PHASE_DEG);
        }
    }

    public void setUncompletedRowsValue(CustomizedPulseSegment customizedPulseSegment, int row) {
        if (customizedPulseSegment.getVoltage() != 0 || isVoltageSet) {
            setValueAt(String.valueOf(customizedPulseSegment.getVoltage()), row, COLUMN_VOLTAGE);
        }
        if (customizedPulseSegment.getInterval() != 0) {
            setValueAt(String.valueOf((customizedPulseSegment.getInterval() * 1000)), row, COLUMN_INTERVAL);
        }
        if (customizedPulseSegment.getTransition() != null) {
            setValueAt(customizedPulseSegment.getTransition(), row, COLUMN_TRANSITION);
            if (Objects.equals(String.valueOf(customizedPulseSegment.getTransition()), "Step")) {
                setValueAt("—", row, COLUMN_START_V);
            } else {
                setValueAt(String.valueOf(customizedPulseSegment.getStartVoltage()), row, COLUMN_START_V);
            }
        }
        if (customizedPulseSegment.getAc() != null) {
            setValueAt(customizedPulseSegment.getAc(), row, COLUMN_AC);
            if (Objects.equals(customizedPulseSegment.getAc(), "on")) {
                setValueAt(String.valueOf(customizedPulseSegment.getWaveform()), row, COLUMN_WAVEFORM);
                setValueAt(String.valueOf(customizedPulseSegment.getStartVpp()), row, COLUMN_START_VPP);
                setValueAt(String.valueOf(customizedPulseSegment.getEndVpp()), row, COLUMN_END_VPP);
                setValueAt(String.valueOf(customizedPulseSegment.getStartHz()), row, COLUMN_START_HZ);
                setValueAt(String.valueOf(customizedPulseSegment.getEndHz()), row, COLUMN_END_HZ);
                setValueAt(String.valueOf(customizedPulseSegment.getFSweep()), row, COLUMN_F_SWEEP);
                setValueAt(String.valueOf(customizedPulseSegment.getPhase()), row, COLUMN_PHASE_DEG);
            } else if (Objects.equals(customizedPulseSegment.getAc(), "off")) {
                setValueAt("—", row, COLUMN_WAVEFORM);
                setValueAt("—", row, COLUMN_START_VPP);
                setValueAt("—", row, COLUMN_END_VPP);
                setValueAt("—", row, COLUMN_START_HZ);
                setValueAt("—", row, COLUMN_END_HZ);
                setValueAt("—", row, COLUMN_F_SWEEP);
                setValueAt("—", row, COLUMN_PHASE_DEG);
            }
        }

    }

    public void syncTableListWhenRowAutoFill(int row, int column) {
        if (column == COLUMN_AC)
            if (tableModel.getValueAt(row, COLUMN_AC) == null || tableModel.getValueAt(row, COLUMN_AC).equals("") || tableModel.getValueAt(row, COLUMN_AC).equals(AC_OFF)) {
                getTableList().get(row).setAc("off");
                getTableList().get(row).setFSweep(null);
            }
    }

    public List<CustomizedPulseSegment> selectivelyDeepCopyOfList(List<CustomizedPulseSegment> target, List<CustomizedPulseSegment> source, int startPosition, int length) {
        for (int i = startPosition; i < length; i++) {
            CustomizedPulseSegment customizedPulseSegment = source.get(i);
            CustomizedPulseSegment copyCustomizedPulseSegment = new CustomizedPulseSegment();
            copyCustomizedPulseSegment.setVoltage(customizedPulseSegment.getVoltage());
            copyCustomizedPulseSegment.setInterval(customizedPulseSegment.getInterval());
            copyCustomizedPulseSegment.setTransition(customizedPulseSegment.getTransition());
            copyCustomizedPulseSegment.setAc(customizedPulseSegment.getAc());
            copyCustomizedPulseSegment.setStartVoltage(customizedPulseSegment.getStartVoltage());
            copyCustomizedPulseSegment.setWaveform(customizedPulseSegment.getWaveform());
            copyCustomizedPulseSegment.setStartVpp(customizedPulseSegment.getStartVpp());
            copyCustomizedPulseSegment.setEndVpp(customizedPulseSegment.getEndVpp());
            copyCustomizedPulseSegment.setStartHz(customizedPulseSegment.getStartHz());
            copyCustomizedPulseSegment.setEndHz(customizedPulseSegment.getEndHz());
            copyCustomizedPulseSegment.setFSweep(customizedPulseSegment.getFSweep());
            copyCustomizedPulseSegment.setPhase(customizedPulseSegment.getPhase());
            target.add(copyCustomizedPulseSegment);
        }
        return target;
    }
}
