package ui.layout.left.display.components.container.serial.command;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * getCommandStringList()方法：读取SSCOM命令字符串配置文件，并匹配得到有效命令字符串封装成commandString对象
 * saveCommandStringList()方法：添加命令字符串配置文件头部说明，并按照相应格式写入命令字符串，另存为ini文件
 */
@Slf4j
public class CommandStringList {
    /**
     * 读取文件，匹配在用命令字符串，封装成命令字符串对象
     *
     * @param path w文件路径
     * @return 命令字符串对象的集合
     */

    public static List<CommandString> getCommandStringList(String path) {
        List<CommandString> commandStringList = new ArrayList<>();
        if (path != null) {
            Path p = Paths.get(path);
            // 若文件不存在，创建文件并把一些必要的格式内容写入到文件中
            if (!Files.exists(p)) {
                try {
                    Files.createFile(p);
                    try (   //创建一个文件字节输出流
                            OutputStream outputStream = Files.newOutputStream(p);
                            //把原始的字节输出流，按照指定的字符集编码转换成字符输出转换流
                            Writer outputStreamWriter = new OutputStreamWriter(outputStream, "GBK");
                            //把字符输出流包装成缓冲字符输出流
                            BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter)) {
                        bufferedWriter.append(";删除本文件可以恢复默认值。\n" + ";这是SSCOM的设置保存文件,您在程序中设置好的串口参数和字符串数据都会自动保存,请最好不要用外部编辑器改动本文件!\n" + ";如果文件被修改后程序不能打开,请删除本文件,程序将会自动生成一个新的ini文件.\n" + ";靠行首的半角分号是注释符号\n" + ";每行都以回车结束\n" + "\n" + ";\"=\"后面的H表示这是个HEX数据串\n" + ";\"=\"后面的A表示这是个ASC字符串\n" + ";Nx表示第几条定义的字符串(1<x<=N)\n");
                        for (int i = 0; i < 99; i++) {
                            bufferedWriter.append("N").append(String.valueOf(100 + i + 1)).append("=0").append(",").append(",").append("1000\n").append("N").append(String.valueOf(i + 1)).append("=H").append(",\n\n");
                        }
                    }
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }

            try (    //得到文件的原始字节流
                     InputStream inputStream = Files.newInputStream(p);//文件编码为"ANSI"
                     //把原始的字节输入流按照指定的字符集编码转换成字符输入流
                     Reader reader = new InputStreamReader(inputStream, "GBK");
                     //把字符输入流包装成缓冲字符输入流
                     BufferedReader bufferedReader = new BufferedReader(reader)) {
                String line;
                List<String> validString = new ArrayList<>();
                //匹配有效命令行
                while ((line = bufferedReader.readLine()) != null) {
                    if (!(Pattern.matches(";.*", line) | (Pattern.matches("N\\d+=,.*", line)))) {
                        validString.add(line);
                    }
                }
                //去除空字符串
                for (int i = validString.size() - 1; i >= 0; i--) {
                    if (validString.get(i).isEmpty()) {
                        validString.remove(validString.get(i));
                    }
                }
                //清洗后的集合
                List<String> cleanValidString = new ArrayList<>();
                for (int i = 0; i < validString.size(); i += 2) {
                    cleanValidString.add(validString.get(i) + ";" + validString.get(i + 1));
                }
                //储存命令对象的集合
                for (String s : cleanValidString) {
                    CommandString commandString = new CommandString(s);
                    commandStringList.add(commandString);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            for (int i = 0; i < 99; i++) {
                CommandString commandString = new CommandString();
                commandStringList.add(commandString);
            }
        }
        return commandStringList;
    }

    /**
     * 保存修改后的table数据
     *
     * @param commandStrings table命令字符串
     */
    public static void saveCommandStringList(List<String> commandStrings, String path) {
        try (   //创建一个文件字节输出流
                OutputStream outputStream = Files.newOutputStream(Paths.get(path));
                //把原始的字节输出流，按照指定的字符集编码转换成字符输出转换流
                Writer outputStreamWriter = new OutputStreamWriter(outputStream, "GBK");
                //把字符输出流包装成缓冲字符输出流
                BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter)
        ) {
            //匹配与保存ini文件头部字符串
            bufferedWriter.append(";删除本文件可以恢复默认值。\n" +
                    ";这是SSCOM的设置保存文件,您在程序中设置好的串口参数和字符串数据都会自动保存,请最好不要用外部编辑器改动本文件!\n" +
                    ";如果文件被修改后程序不能打开,请删除本文件,程序将会自动生成一个新的ini文件.\n" +
                    ";靠行首的半角分号是注释符号\n" +
                    ";每行都以回车结束\n" +
                    "\n" +
                    ";\"=\"后面的H表示这是个HEX数据串\n" +
                    ";\"=\"后面的A表示这是个ASC字符串\n" +
                    ";Nx表示第几条定义的字符串(1<x<=N)\n");
            //写入命令行字符串
            for (String commandString : commandStrings) {
                bufferedWriter.append(commandString);
            }
            bufferedWriter.append(";end");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 把配置数据以JSON的格式进行保存
     */
    public static void saveCommandStringListAsJson(List<Command> commands, String path) {
        try (   //创建一个文件字节输出流
                OutputStream outputStream = Files.newOutputStream(Paths.get(path));
                //把原始的字节输出流，按照指定的字符集编码转换成字符输出转换流
                Writer outputStreamWriter = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
                //把字符输出流包装成缓冲字符输出流
                BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter)
        ) {
            String s = JSON.toJSONString(commands);
            bufferedWriter.append(s);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
