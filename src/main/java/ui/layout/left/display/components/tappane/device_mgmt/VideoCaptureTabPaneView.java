package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.picture.PictureContainer;
import ui.layout.left.display.components.container.picture.PictureViewContainer;
import ui.layout.left.display.components.container.picture.VideoCaptureContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * @author: QinHao
 * @description:
 * @date: 2024/12/26 14:55
 */
public class VideoCaptureTabPaneView extends DeviceTabPaneView {
    private boolean firstOpen = true;

    public VideoCaptureTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        PictureContainer pictureContainer = new VideoCaptureContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, pictureContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }

    public int addPort(Device device) {
        String tabName = device.getFriendlyName();
        if (firstOpen) {
            PictureViewContainer pictureViewContainer = new PictureViewContainer(getClientView(), getMainModel(), device);
            addTab("查看图像模板", pictureViewContainer);
            firstOpen = false;
        }
        int index = indexOfTab(tabName);
        if (index == -1) {
            addTabHook(tabName, device);
            index = getTabCount() - 1;
        }
        setSelectedIndex(index);
        return index;
    }
}
