package ui.layout.left.display.components.container.picture;

import com.alibaba.fastjson2.JSON;
import org.jetbrains.annotations.NotNull;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.ImageMethods;
import sdk.domain.image.CameraSettings;
import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.Timer;
import java.util.TimerTask;

import static sdk.base.BaseHttpClient.executeOperation;

/**
 * @author: <PERSON><PERSON>ao
 * @description: 相机参数控制面板
 * @date: 2024/7/4 16:57
 */
public class CameraParameterPanel extends JPanel implements BaseView {
    private static final int MAX_EXPOSURE_TIME = 1000000;
    private static final String STOP_GRABBING = "停止采集";
    private static final String START_GRABBING = "开始采集";
    private Timer exposureTimeUpdateTimer;
    private final JComboBox<String> exposureAutoComboBox;
    private final JSpinner autoExposureTimeLowerSpinner;
    private final JSpinner autoExposureTimeUpperSpinner;
    private final JCheckBox reverseXCheckBox;
    private final JCheckBox reverseYCheckBox;
    private final JButton grabbingSwitchButton;
    private final PictureContainer container;
    private final JSlider exposureTimeSlider;
    private final JLabel exposureTimeLabel;
    private final JPanel exposureTimePanel;
    private final JPanel exposureTimeSliderPanel;
    private final JCheckBox exposureCheckBox;
    private final MainModel mainModel;


    public CameraParameterPanel(PictureContainer container, MainModel mainModel) {
        this.container = container;
        this.mainModel = mainModel;
        exposureAutoComboBox = createExposureAutoComboBox();
        autoExposureTimeLowerSpinner = createSpinner(44, 44, 50000, 100);
        autoExposureTimeUpperSpinner = createSpinner(50000, 44, 1000000, 1000);
        grabbingSwitchButton = new JButton(STOP_GRABBING);
        reverseXCheckBox = new JCheckBox("水平镜像");
        reverseYCheckBox = new JCheckBox("垂直镜像");
        reverseYCheckBox.setEnabled(false);
        exposureCheckBox = new JCheckBox("曝光手动设置");
        exposureTimeSliderPanel = new JPanel();
        exposureTimePanel = new JPanel();
        exposureTimeSlider = new JSlider(1, 1000, 50);
        exposureTimeLabel = new JLabel("");

        Dimension componentSize = new Dimension(160, 30);
        // 设置统一的组件尺寸
        exposureAutoComboBox.setMaximumSize(componentSize);
        autoExposureTimeLowerSpinner.setMaximumSize(componentSize);
        autoExposureTimeUpperSpinner.setMaximumSize(componentSize);
        grabbingSwitchButton.setMaximumSize(componentSize);

        createView();
        createActions();
    }

    private JComboBox<String> createExposureAutoComboBox() {
        JComboBox<String> comboBox = new JComboBox<>();
        comboBox.addItem("关闭");
        comboBox.addItem("一次");
        comboBox.addItem("连续");
        return comboBox;
    }

    private JSpinner createSpinner(int value, int min, int max, int step) {
        return new JSpinner(new SpinnerNumberModel(value, min, max, step));
    }

    @Override
    public void createView() {
        // 使用垂直BoxLayout作为主布局
        setLayout(new BoxLayout(this, BoxLayout.Y_AXIS));

        // 创建带左侧竖杠的标签
        JLabel cameraExposureLabel = createMenuLabel("相机参数");
        JLabel cameraGrabbingLabel = createMenuLabel("采集控制");

        // 创建较小的灰色字体
        Font smallFont = new Font(null, Font.PLAIN, 12);
        Color grayColor = new Color(101, 101, 101);

        // 创建各行面板，使用FlowLayout(FlowLayout.LEFT)确保组件左对齐
        JLabel autoExposureLabel = new JLabel("自动曝光：");
        autoExposureLabel.setFont(smallFont);
        autoExposureLabel.setForeground(grayColor);

        // 曝光时间滑块面板
        exposureTimeSliderPanel.setLayout(new BoxLayout(exposureTimeSliderPanel, BoxLayout.X_AXIS));
        JLabel percentLabel = new JLabel("%");
        exposureTimeSlider.setPreferredSize(new Dimension(120, 30));
        exposureTimeSlider.setMinimumSize(new Dimension(120, 30));

        exposureTimeSliderPanel.add(exposureTimeSlider);
        exposureTimeLabel.setText(exposureTimeSlider.getValue() / 10 + "");
        exposureTimeSliderPanel.add(exposureTimeLabel);
        exposureTimeSliderPanel.add(percentLabel);

        // 曝光时间上下限面板
        exposureTimePanel.setLayout(new BoxLayout(exposureTimePanel, BoxLayout.Y_AXIS));
        exposureTimePanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        // 曝光时间上下限标签 设置小字体，且颜色灰一些
        JLabel lowerLabel = new JLabel("曝光时间下限");
        lowerLabel.setFont(smallFont);
        lowerLabel.setForeground(grayColor);
        JLabel upperLabel = new JLabel("曝光时间上限");
        upperLabel.setFont(smallFont);
        upperLabel.setForeground(grayColor);
        // 垂直添加所有面板
        exposureTimePanel.add(lowerLabel);
        exposureTimePanel.add(autoExposureTimeLowerSpinner);
        exposureTimePanel.add(upperLabel);
        exposureTimePanel.add(autoExposureTimeUpperSpinner);
        // 设置所有面板左对齐
        lowerLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        autoExposureTimeLowerSpinner.setAlignmentX(Component.LEFT_ALIGNMENT);
        upperLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        autoExposureTimeUpperSpinner.setAlignmentX(Component.LEFT_ALIGNMENT);

        // 添加所有面板到主面板
        add(cameraExposureLabel);
        add(autoExposureLabel);
        add(exposureAutoComboBox);
        add(reverseXCheckBox);
        add(reverseYCheckBox);
        add(exposureTimeSliderPanel);
        add(exposureCheckBox);
        add(exposureTimePanel);
        add(Box.createVerticalStrut(10));
        add(cameraGrabbingLabel);
        add(grabbingSwitchButton);
        add(Box.createVerticalStrut(10));

        // 设置所有面板的对齐方式为左对齐
        autoExposureLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        exposureAutoComboBox.setAlignmentX(Component.LEFT_ALIGNMENT);
        reverseXCheckBox.setAlignmentX(Component.LEFT_ALIGNMENT);
        reverseYCheckBox.setAlignmentX(Component.LEFT_ALIGNMENT);
        exposureTimeSliderPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        exposureCheckBox.setAlignmentX(Component.LEFT_ALIGNMENT);
        exposureTimePanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        grabbingSwitchButton.setAlignmentX(Component.LEFT_ALIGNMENT);

        // 默认隐藏相关面板
        exposureTimePanel.setVisible(false);
        exposureTimeSliderPanel.setVisible(false);
        exposureCheckBox.setVisible(false);

    }

    @NotNull
    private JLabel createMenuLabel(String text) {
        JLabel cameraExposureLabel = new JLabel(text) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g); // 默认内容
                // 绘制蓝色竖杠 (3px宽)
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setColor(new Color(30, 144, 255));
                g2.setStroke(new BasicStroke(3f));
                // 调整参数实现间距
                int lineX = 0;
                int verticalMargin = getHeight() / 4; // 上下边距
                g2.drawLine(lineX, verticalMargin,
                        lineX, getHeight() - verticalMargin);
                g2.dispose();
            }
        };
        // 通过边框留白让文字右移
        cameraExposureLabel.setBorder(BorderFactory.createEmptyBorder(0, 4, 0, 0));
        cameraExposureLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        return cameraExposureLabel;
    }

    @Override
    public void createActions() {
        exposureAutoComboBox.addItemListener(e -> {
            if (exposureAutoComboBox.getSelectedIndex() != 0) {
                exposureTimeSliderPanel.setVisible(true);
                exposureCheckBox.setVisible(true);
            } else {
                exposureTimeSliderPanel.setVisible(false);
                exposureCheckBox.setVisible(false);
            }
        });
        reverseXCheckBox.addItemListener(e -> executeReverseOperation(reverseXCheckBox.isSelected(), ImageMethods.setReverseX));
        reverseYCheckBox.addItemListener(e -> executeReverseOperation(reverseYCheckBox.isSelected(), ImageMethods.setReverseY));
        grabbingSwitchButton.addActionListener(e -> toggleGrabbing());
        exposureCheckBox.addItemListener(e -> toggleExposurePanels(exposureCheckBox.isSelected()));
        exposureTimeSlider.addChangeListener(e -> {
            if (exposureTimeUpdateTimer != null) {
                exposureTimeUpdateTimer.cancel();
            }
            exposureTimeUpdateTimer = new Timer();
            exposureTimeUpdateTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    updateExposureTime();
                }
            }, 500);
        });
        autoExposureTimeLowerSpinner.addChangeListener(e -> updateCameraSettings());
        autoExposureTimeUpperSpinner.addChangeListener(e -> updateCameraSettings());
    }

    private void executeReverseOperation(boolean isSelected, OperationMethod method) {
        Operation operation = Operation.buildOperation(container.getDevice());
        operation.setOperationObject(isSelected);
        operation.setOperationMethod(method);
        executeOperation(operation);
    }

    private void toggleGrabbing() {
        Operation operation = Operation.buildOperation(container.getDevice());
        if (grabbingSwitchButton.getText().equals(STOP_GRABBING)) {
            operation.setOperationMethod(ImageMethods.stopGrabbing);
            grabbingSwitchButton.setText(START_GRABBING);
            reverseYCheckBox.setEnabled(true);
        } else {
            operation.setOperationMethod(ImageMethods.startGrabbing);
            grabbingSwitchButton.setText(STOP_GRABBING);
            reverseYCheckBox.setEnabled(false);
        }
        executeOperation(operation);
    }

    private void toggleExposurePanels(boolean isSelected) {
        exposureTimePanel.setVisible(isSelected);
        exposureTimeSliderPanel.setVisible(!isSelected);
    }

    private void updateExposureTime() {
        int value = exposureTimeSlider.getValue();
        exposureTimeLabel.setText(String.valueOf((double) value / 10));
        int autoExposureTime = value * MAX_EXPOSURE_TIME / 1000;
        CameraSettings cameraSettings = CameraSettings.builder()
                .autoExposureMode(exposureAutoComboBox.getSelectedIndex())
                .autoExposureTimeLower(autoExposureTime)
                .autoExposureTimeUpper(autoExposureTime)
                .build();
        executeCameraSettingsOperation(cameraSettings);
    }

    private void updateCameraSettings() {
        CameraSettings cameraSettings = CameraSettings.builder()
                .autoExposureMode(exposureAutoComboBox.getSelectedIndex())
                .autoExposureTimeLower((int) autoExposureTimeLowerSpinner.getValue())
                .autoExposureTimeUpper((int) autoExposureTimeUpperSpinner.getValue())
                .build();
        executeCameraSettingsOperation(cameraSettings);
    }

    private void executeCameraSettingsOperation(CameraSettings cameraSettings) {
        Operation operation = Operation.buildOperation(container.getDevice());
        operation.setOperationMethod(ImageMethods.setExposureAutoMode);
        operation.setOperationObject(cameraSettings);
        executeOperation(operation);
        getCameraParameters();
    }

    public void getCameraParameters() {
        Operation operation = Operation.buildOperation(container.getDevice());
        operation.setOperationMethod(ImageMethods.getCameraParameters);
        OperationResult operationResult = executeOperation(operation);
        if (operationResult.isOk()) {
            //将operationResult.getData()的json转换为CameraSettings对象
            CameraSettings cameraParameters = JSON.to(CameraSettings.class, operationResult.getData());
            mainModel.getTestScriptEventModel().updateCameraParameters(String.valueOf(cameraParameters.getFrameRate()), String.valueOf(cameraParameters.getExposureTime()));
        }
    }
}