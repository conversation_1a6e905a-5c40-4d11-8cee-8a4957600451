package ui.layout.left.display.components.container.screen.touchPoint;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class PointUtils {
    /**
     * @param resolutionWidth       分辨率宽度
     * @param resolutionHeight      分辨率高度
     * @param screenWidth           屏幕宽度
     * @param screenHeight          屏幕高度
     * @param screenCoordinatePoint 屏幕坐标点
     * @return 屏幕坐标转换为分辨率坐标
     */
    public static Point getResolutionCoordinate(int resolutionWidth,
                                                int resolutionHeight,
                                                BigDecimal screenWidth,
                                                BigDecimal screenHeight,
                                                Point screenCoordinatePoint) {
        BigDecimal horizontalPixelsPerMillimetre = BigDecimal.valueOf(resolutionWidth).divide(screenWidth, 24, RoundingMode.FLOOR);
        BigDecimal verticalPixelsPerMillimetre = BigDecimal.valueOf(resolutionHeight).divide(screenHeight, 24, RoundingMode.FLOOR);
        Point resolutionCoordinatePoint = new Point();
        resolutionCoordinatePoint.setY(screenCoordinatePoint.getX().multiply(verticalPixelsPerMillimetre));
        resolutionCoordinatePoint.setX(screenCoordinatePoint.getY().multiply(horizontalPixelsPerMillimetre));
        return resolutionCoordinatePoint;
    }
}
