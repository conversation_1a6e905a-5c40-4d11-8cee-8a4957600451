package ui.layout.left.display.components.container.serial.tcpserver;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.SseSession;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.entity.SerialDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.DecimalFormat;

/**
 * @author: Qin, Hao
 * @description: TCP控制面板
 * @date: 2024/6/20 12:11
 */
@Slf4j
public class TCPControlPanel extends JPanel implements BaseView {
    private static final long serialVersionUID = 1L;
    private final JTextField ipTextField;
    private final JSpinner portSpinner;
    private final JTextField mustTextField;
    private final JTextField forbidTextField;
    private final JButton connectButton;
    private final JButton mustAddToScriptButton;
    private final JButton forbidAddToScriptButton;
    private final JTextArea msgTextArea;
    private final JPopupMenu popupMenu;
    private final JCheckBox autoScrollCheckBox;
    private final JScrollPane scrollPane;
    private final JButton pwmButton;
    private final JTextField pwmTextField;
    private final JButton pwmAddToScriptButton;
    private final JComboBox<String> pwmComboBox;
    private final MainModel mainModel;
    private final SerialDevice serialDevice;
    private final DeviceContainer deviceContainer;
    private final SseSession sseSession;
    private static final String TCP_MSG = "tcpMessage";
    private static final int MAX_LENGTH = 5000;
    public static boolean running = false;


    public TCPControlPanel(MainModel mainModel, DeviceContainer deviceContainer) {
        this.mainModel = mainModel;
        this.deviceContainer = deviceContainer;
        serialDevice = (SerialDevice) deviceContainer.getDevice();
        ipTextField = new JTextField();
        mustTextField = new JTextField();
        forbidTextField = new JTextField();
        portSpinner = new JSpinner(new SpinnerNumberModel(8082, 1, null, 1));
        connectButton = new JButton("打开");

        mustAddToScriptButton = SwingUtil.getDebugButton();
        forbidAddToScriptButton = SwingUtil.getAddToScriptButton();

        autoScrollCheckBox = new JCheckBox("自动滚动");
        pwmButton = new JButton("调试");
        pwmTextField = new JTextField();
        pwmAddToScriptButton = new JButton(">>添加到步骤");
        pwmComboBox = new JComboBox<>(new String[]{"大于", "小于", "等于"});
        msgTextArea = new JTextArea();
        sseSession = new SseSession();
        popupMenu = new JPopupMenu();
        scrollPane = new JScrollPane(msgTextArea);
        createView();
        createActions();
        updateMsgTextArea();
        setAutoScroll();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout(0, 0));

        JPanel panel = new JPanel();
        panel.setBorder(new TitledBorder(new LineBorder(new Color(128, 128, 128)), "参数设置", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
        FlowLayout flowLayout = (FlowLayout) panel.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        add(panel, BorderLayout.NORTH);

        JLabel lblNewLabel = new JLabel("IP地址");
        panel.add(lblNewLabel);
        ipTextField.setText("127.0.0.1");
        panel.add(ipTextField);
        ipTextField.setColumns(15);

        JLabel lblNewLabel_1 = new JLabel("端口号");
        panel.add(lblNewLabel_1);

        JSpinner.NumberEditor editor = new JSpinner.NumberEditor(portSpinner);
        DecimalFormat format = editor.getFormat();
        format.setGroupingUsed(false);
        portSpinner.setEditor(editor);
        panel.add(portSpinner);
        panel.add(connectButton);

        msgTextArea.setEditable(false);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);

        msgTextArea.setLineWrap(true);
        add(scrollPane, BorderLayout.CENTER);
        addMenu();
        JPanel panel_1 = new JPanel();
        add(panel_1, BorderLayout.SOUTH);
        panel_1.setLayout(new BorderLayout(0, 0));

        JPanel panel_5 = new JPanel();
        panel_5.setBorder(new TitledBorder(new LineBorder(new Color(128, 128, 128)), "过程检测", TitledBorder.LEADING, TitledBorder.TOP, null, Color.BLACK));
        panel_1.add(panel_5);
        panel_5.setLayout(new GridLayout(2, 1, 0, 0));

        JPanel panel_2 = new JPanel();
        panel_5.add(panel_2);
        FlowLayout flowLayout_1 = (FlowLayout) panel_2.getLayout();
        flowLayout_1.setAlignment(FlowLayout.LEFT);

        JLabel lblNewLabel_5 = new JLabel("必须出现");
        panel_2.add(lblNewLabel_5);

        panel_2.add(mustTextField);
        mustTextField.setColumns(50);
        SwingUtil.changeDocumentFilter(mustTextField, true);

        panel_2.add(mustAddToScriptButton);

        JPanel panel_3 = new JPanel();
        panel_5.add(panel_3);
        FlowLayout flowLayout_2 = (FlowLayout) panel_3.getLayout();
        flowLayout_2.setAlignment(FlowLayout.LEFT);

        JLabel lblNewLabel_6 = new JLabel("禁止出现");
        panel_3.add(lblNewLabel_6);

        panel_3.add(forbidTextField);
        forbidTextField.setColumns(50);
        SwingUtil.changeDocumentFilter(forbidTextField, true);

        panel_3.add(forbidAddToScriptButton);

        JPanel panel_4 = new JPanel();
        panel_1.add(panel_4, BorderLayout.NORTH);

        autoScrollCheckBox.setSelected(true);
        panel_4.add(autoScrollCheckBox);

        JPanel panel_6 = new JPanel();
        panel_4.setBorder(new TitledBorder(new LineBorder(new Color(128, 128, 128)), "PWM", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
        panel_6.setLayout(new FlowLayout(FlowLayout.LEFT));
        panel_6.add(new JLabel("条件:"));
        panel_6.add(pwmComboBox);
        panel_6.add(new JLabel("PWM值："));
        panel_6.add(pwmTextField);
        panel_6.add(pwmButton);
        panel_6.add(pwmAddToScriptButton);
        panel_1.add(panel_6, BorderLayout.SOUTH);

    }

    private void updateMsgTextArea() {
        new Thread(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl(TCP_MSG),
                        line -> {
                            clearMaxLength();
                            String jsonContent = line.substring(line.indexOf("{"));
                            TCPShowMessages tcpShowMessages = JSONObject.parseObject(jsonContent, TCPShowMessages.class);
                            msgTextArea.append(tcpShowMessages.getMessage() + "\n");
                            setAutoScroll();
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }).start();
    }

    private void addMenu() {
        JMenuItem clearMenuItem = new JMenuItem("清空");
        clearMenuItem.addActionListener(new ClearActionListener());
        popupMenu.add(clearMenuItem);
        setComponentPopupMenu(popupMenu);
    }

    private class ClearActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            msgTextArea.setText("");
        }
    }

    @Override
    public void createActions() {
        connectButton.addActionListener(e -> {
            //ip地址校验
            if (!SwingUtil.checkIp(ipTextField)) {
                SwingUtil.showWebMessageDialog(this, "请输入正确的IP地址");
                return;
            }
            TCPSetting tcpSetting = new TCPSetting();
            tcpSetting.setServerIp(ipTextField.getText());
            tcpSetting.setServerPort((Integer) portSpinner.getValue());
            Operation operation = Operation.buildOperation(serialDevice);
            operation.setOperationObject(tcpSetting);
            boolean isConnected = connectButton.getText().equals("关闭");
            operation.setOperationMethod(isConnected ? DeviceMethods.stopTcpServer : DeviceMethods.buildTcpServer);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isOk()) {
                connectButton.setText(isConnected ? "打开" : "关闭");
                running = !running;
            } else {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        });
        autoScrollCheckBox.addActionListener(e -> setAutoScroll());
        pwmButton.addActionListener(e -> {
        });
        pwmAddToScriptButton.addActionListener(e -> {
        });
    }

    private void clearMaxLength() {
        if (msgTextArea.getLineCount() > MAX_LENGTH) {
            msgTextArea.setText("");
        }
    }

    public void setAutoScroll() {
        if (autoScrollCheckBox.isSelected()) {
            msgTextArea.setCaretPosition(msgTextArea.getDocument().getLength());
        }
    }

}
