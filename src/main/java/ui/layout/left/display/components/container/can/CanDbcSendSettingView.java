package ui.layout.left.display.components.container.can;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import javafx.application.Platform;
import javafx.embed.swing.JFXPanel;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.desay.sv.api.callback.JavaFXReturnListener;
import org.desay.sv.api.model.CANMessageR;
import org.desay.sv.api.model.ConfigureSignalStep;
import org.desay.sv.controller.SendDBCController;
import sdk.base.SseSession;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.ChannelSwitch;
import sdk.domain.Device;
import sdk.domain.bus.CanConfig;
import sdk.domain.bus.CanMessage;
import sdk.domain.bus.DbcConfig;
import sdk.entity.CanDevice;
import sdk.entity.ElectricRelayDevice;
import ui.layout.left.display.components.container.can.model.DbcPathModel;
import ui.layout.left.display.components.container.can.model.SignalStepConfiguration;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import java.awt.*;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class CanDbcSendSettingView extends BaseCanDbcSettingView implements DbcFileObserver {

    @Getter
    private SendDBCController controller;
    private final ExecutorService executorService;
    private final SseSession sseSession;
    private final CanConfig canConfig;
    private final CanContainer canContainer;
    private boolean skipControllerUpdate = false; //防止重复调用标识
    private static boolean javafxInitialized = false;
    private ElectricRelayDevice electricRelayDevice;

    public CanDbcSendSettingView(CanContainer canContainer, MainModel mainModel, CanConfig canConfig, int channel, String savePath,  DbcPathModel model) {
        super(canContainer, channel, canConfig,model);
        this.canConfig = canConfig;
        this.canContainer = canContainer;
        sseSession = new SseSession();

        JFXPanel jfxPanel = new JFXPanel();
        initializeJavaFX();
        centerPanel.add(jfxPanel, BorderLayout.CENTER);
        executorService = Executors.newFixedThreadPool(4, r -> {
            Thread thread = new Thread(r);
            thread.setName(String.format("CanDbcSendThread-%d-%d", channel, thread.getId()));
            return thread;
        });

        DbcFileManager.getInstance().addObserver(channel, canContainer.getDevice().getDeviceName(), this);
        executorService.submit(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl("sendFinish" + channel),
                        hexId -> {
                            try {
                                String msg = hexId.substring(hexId.lastIndexOf(":") + 1).replace("\"", "");
                                if (!StringUtils.isEmpty(msg)) {
                                    String hexString = "0x" + Long.toHexString(Long.parseLong(msg)).toUpperCase();
                                    controller.sendFinish(hexString);
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        executorService.submit(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl("sendError" + channel),
                        hexId -> {
                            try {
                                String msg = hexId.substring(hexId.lastIndexOf(":") + 1).replace("\"", "");
                                if (!StringUtils.isEmpty(msg)) {
                                    String hexString = "0x" + Long.toHexString(Long.parseLong(msg)).toUpperCase();
                                    controller.sendError(hexString);
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        JavaFXReturnListener javaFXReturnListener = new JavaFXReturnListener() {
            @Override
            public void onSendMessagesSelected(List<CANMessageR> list) {
                for (CANMessageR msg : list) {
                    ((CanDevice) canContainer.getDevice()).sendCanMessage(channel, toMessage(msg, channel));
                }
            }

            @Override
            public void onAddScriptMessagesSelected(List<CANMessageR> list) {
                for (CANMessageR msg : list) {
                    Operation operation = Operation.buildOperation(canContainer.getDevice());
                    operation.getOperationTarget().setChannel(channel);
                    operation.setOperationMethod(DeviceMethods.sendCanMessage);
                    operation.setOperationObject(toMessage(msg, channel));
                    mainModel.getOperationModel().updateOperation(operation);
                }
            }

            @Override
            public void onSendMessagesSelectedStop(List<CANMessageR> list) {
                for (CANMessageR msg : list) {
                    ((CanDevice) canContainer.getDevice()).stopCanMessage(channel, Integer.decode(msg.getIdHex()));
                }
            }

            @Override
            public void onSelectDbcFile(List<String> filePaths) {
                skipControllerUpdate = true;
                updateUIComponents(filePaths);
                updateConfigAsync();
                DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), filePaths);
            }

            @Override
            public String[] fetchRunningCanMessages(int channel) {
                OperationResult result = ((CanDevice) canContainer.getDevice()).fetchRunningCanMessage(channel);
                if (result.isOk()) {
                    JSONArray array = (JSONArray) result.getData();
                    return toStringIds(array.toJavaList(Integer.class));
                }
                return null;
            }

            @Override
            public void changeCanConfigE2EType(String e2eType) {
                Map<Integer, String> e2eTypes = canConfig.getE2eTypes();
                e2eTypes.put(channel, e2eType);
                canContainer.getDevice().updateConfig(canConfig);
            }


            @Override
            public boolean registerRelayDevice() {
                Set<Device> devices = Device.getConnectedDevices("relayType");
                if (CollectionUtil.isNotEmpty(devices)) {
                    electricRelayDevice = (ElectricRelayDevice) devices.toArray()[0];
                    return true;
                } else {
                    SwingUtil.showWarningDialog(null, "请先添加继电器设备！");
                    return false;
                }
            }

            @Override
            public void sendRelayValue(Map<String, Boolean> relayStates) {
                for (String key : relayStates.keySet()) {
                    String channelString = key.replaceAll("[^0-9]", ""); // 只保留数字部分
                    if (!channelString.isEmpty()) {
                        int channel = Integer.parseInt(channelString);
                        Boolean switchState = relayStates.get(key);
                        if (switchState != null) {
                            ChannelSwitch channelSwitch = new ChannelSwitch();
                            channelSwitch.setChannel(channel);
                            channelSwitch.setSwitchOn(switchState);
                            electricRelayDevice.switchRelay(channelSwitch);
                        }
                    } else {
                        log.error("无效的通道键: {}", key);
                    }
                }
            }

            /**
             * 更新指定通道的E2E(端到端)全局状态配置
             * 该方法用于设置CAN通道的E2E功能全局启用状态，并将配置更新到设备
             *
             * @param e2eGlobalStatus E2E全局状态开关
             *        - true: 启用该通道的E2E功能
             *        - false: 禁用该通道的E2E功能
             */
            @Override
            public void updateE2EGlobalStatus(Boolean e2eGlobalStatus) {
                // 从CAN配置中获取当前所有通道的E2E状态映射表
                Map<Integer, Boolean> e2eTypes = canConfig.getE2eGlobalStatus();

                // 更新目标通道的E2E状态
                e2eTypes.put(channel, e2eGlobalStatus);

                // 将更新后的配置应用到设备
                canContainer.getDevice().updateConfig(canConfig);
            }

            /**
             * 配置信号步进参数
             * <p>
             * 该方法用于处理信号步进配置请求，将传入的ConfigureSignalStep对象转换为SignalStepConfiguration对象，
             * 然后构建相应的操作对象并更新到操作模型中。
             *
             * @param step 包含信号步进配置信息的对象
             */
            @Override
            public void configureSignalStep(ConfigureSignalStep step) {
                // 创建新的信号步进配置对象
                SignalStepConfiguration signalStepConfiguration = new SignalStepConfiguration();

                // 使用BeanUtil工具将ConfigureSignalStep对象的属性复制到SignalStepConfiguration对象中
                BeanUtil.copyProperties(step, signalStepConfiguration);

                // 构建操作对象，基于当前CAN容器的设备信息
                Operation operation = Operation.buildOperation(canContainer.getDevice());

                // 设置操作方法为信号步进对话框方法
                operation.setOperationMethod(DeviceMethods.signalStepDialog);

                // 将信号步进配置对象设置为操作对象
                operation.setOperationObject(signalStepConfiguration);

                // 将构建好的操作对象更新到主模型的操作模型中
                mainModel.getOperationModel().updateOperation(operation);
            }
        };

        javafxApplication(jfxPanel, channel, mainModel, savePath, javaFXReturnListener);
    }

    private CanMessage toMessage(CANMessageR msg, int channel) {
        CanMessage canMessage = new CanMessage();
        canMessage.setDlc(msg.getDlc());
        canMessage.setCanFd("CANFD".equals(msg.getEventType()) || "CANFD加速".equals(msg.getEventType()));
        canMessage.setChannel(channel);
        canMessage.setArbitrationId(Integer.decode(msg.getIdHex()));
        canMessage.setSendMethod("正常发送");
//        float duration = msg.getSendCount() * msg.getInterval() / 1000.0f;
        canMessage.setSendTimes(msg.getSendCount() == 0 ? -1 : msg.getSendCount());
        canMessage.setPeriod(msg.getInterval() / 1000.0f);
        canMessage.setE2eEnabled(msg.isOpenE2E());
        canMessage.setE2eType(msg.getE2eType());
        canMessage.setE2eError(msg.getE2eError());
        canMessage.setSendMode(msg.getSendMode());
        if (msg.isLoop()) {//循环发送开关
            canMessage.setLoop(true);
            canMessage.setLoopDatas(msg.getLoopDatas());
        } else {
            canMessage.setData(msg.getData());
        }
        return canMessage;
    }

    public void javafxApplication(JFXPanel jfxPanel, int channel, MainModel mainModel, String savePath, JavaFXReturnListener listener) {
        Platform.runLater(() -> {
            try {
                URL url = Thread.currentThread().getContextClassLoader().getResource("fxml/sendDBC.fxml");
                if (url == null) {
                    throw new RuntimeException("FXML resource not found");
                }
                FXMLLoader fxmlLoader = new FXMLLoader(url);
                fxmlLoader.setControllerFactory(clazz -> {
                    if (clazz == SendDBCController.class) {
                        SendDBCController controller = new SendDBCController();
                        controller.setDeviceAliasName(canContainer.getDevice().getAliasName());
                        controller.setDbcPaths(getDbcPaths());
                        controller.setSavePath(savePath);
                        controller.setChannelNum(channel);//控制通道变量0-CAN1 1-CAN2
                        controller.setReturnListener(listener);
                        DbcConfig dbcConfig = canConfig.getDbcConfigs().get(channel + "");
                        if (dbcConfig != null) {
                            controller.setDbcPaths(dbcConfig.getDbcPaths());
                        }
                        return controller;
                    }
                    try {
                        return clazz.newInstance();
                    } catch (Exception exception) {
                        throw new RuntimeException(exception);
                    }
                });
                Scene scene = new Scene(fxmlLoader.load());
                jfxPanel.setScene(scene);
                this.controller = fxmlLoader.getController(); // 保存对 controller 的引用
                String key = canContainer.getDevice().getAliasName() + "-" + channel;
                mainModel.getCanModel().getSendDBCControllerHashMap().put(key, controller);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    public void initializeJavaFX() {
        if (!javafxInitialized) {
            new JFXPanel(); // 初始化 JavaFX 环境
            Platform.setImplicitExit(false);
            javafxInitialized = true;
        }
    }


    /**
     * 释放资源并清理所有相关组件
     */
    public void dispose() {
        // 关闭线程池
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow(); // 立即中断所有线程
        }
        // 关闭 SSE 会话
        sseSession.closeAll();
        // 移除观察者
        DbcFileManager.getInstance().removeObserver(channel, canContainer.getDevice().getDeviceName(), this);
        model.removeListener(this::updateUIComponents);
    }

    @Override
    protected void onLoadDbcClicked(int index,String path) {
        Platform.runLater(() -> {
            controller.loadDBC(index,path);
        });
        // 更新 DBC 文件路径
        updateConfigAsync();
        //需要通知另一个面板添加DBC路径
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }

    private void updateConfigAsync() {
        executorService.submit(() -> {
            Map<String, DbcConfig> dbcConfigs = canConfig.getDbcConfigs();
            String chan = String.valueOf(channel);
            DbcConfig dbcConfig = dbcConfigs.get(chan);
            if (dbcConfig == null) {
                dbcConfig = new DbcConfig();
                dbcConfig.setDbcPaths(getDbcPaths());
                dbcConfigs.put(chan, dbcConfig);
            } else {
                dbcConfig.setDbcPaths(getDbcPaths());
            }
            OperationResult result = canContainer.getDevice().updateConfig(canConfig);
            // 处理结果
            if (!result.isOk()) {
                log.error("Failed to update config: {}", result.getMessage());
            }
        });
    }

    @Override
    protected void removeLastDbc(int index) {
        Platform.runLater(() -> {
            controller.removeDBC(index);
            updateConfigAsync();
        });
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }
    private String[] toStringIds(List<Integer> runningCanMessage) {
        String[] strIds = new String[runningCanMessage.size()];
        for (int i = 0; i < strIds.length; i++) {
            strIds[i] = Integer.toHexString(runningCanMessage.get(i));
        }
        return strIds;
    }

    @Override
    public void onDbcFileChanged(List<String> dbcFilePaths) {
        try {
            if (!skipControllerUpdate && controller != null) {
                Platform.runLater(() -> {
                    controller.changeDBCPath(dbcFilePaths);
                });
            }
        }finally {
            skipControllerUpdate = false;
        }
    }
}
