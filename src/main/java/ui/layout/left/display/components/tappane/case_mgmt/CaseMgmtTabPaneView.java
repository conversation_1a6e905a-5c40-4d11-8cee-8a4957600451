package ui.layout.left.display.components.tappane.case_mgmt;

import lombok.Getter;
import ui.base.BaseView;
import ui.base.TestView;
import ui.entry.ClientView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTabPaneView;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestScriptCaseTabPaneView;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.plaf.basic.BasicTabbedPaneUI;

/**
 * Excel和脚本用例管理面板
 */
public class CaseMgmtTabPaneView extends JTabbedPane implements BaseView {

    @Getter
    private final ExcelCaseTabPaneView excelCaseTabPaneView; //Excel用例视图
    @Getter
    private final TestScriptCaseTabPaneView testScriptCaseTabPaneView; //脚本用例视图

    private final MainModel mainModel;
    private final ClientView clientView;

    public CaseMgmtTabPaneView(ClientView clientView, MainModel mainModel) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        testScriptCaseTabPaneView = new TestScriptCaseTabPaneView(mainModel);
        excelCaseTabPaneView = new ExcelCaseTabPaneView(clientView, mainModel);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setUI(new BasicTabbedPaneUI() {
            @Override
            protected int calculateTabAreaHeight(int tabPlacement, int horizRunCount, int maxTabHeight) {
                return 0;
            }
        });
//        JTabbedPane tabbedPane = new VerticalLabelTabbedPane();
//        tabbedPane.setTabPlacement(RIGHT);
//        tabbedPane.addTab("动作序列编辑器", new JPanel());
//        tabbedPane.addTab("测试案例", excelCaseTabPaneView);
//        tabbedPane.setSelectedIndex(1);
//        setTabPlacement(JTabbedPane.BOTTOM);
        addTab("脚本步骤", testScriptCaseTabPaneView);
        addTab("动作序列", excelCaseTabPaneView);
    }

    @Override
    public void setSelectedIndex(int index) {
        mainModel.getAppModel().switchView(index == 0 ? TestView.OPERATION_STEP : TestView.ACTION_SEQUENCE);
        // 将动作序列or脚本视图的信息传入operationModel中，在进行updateOperation时进行判断
        mainModel.getOperationModel().setTestView(index == 0 ? TestView.OPERATION_STEP : TestView.ACTION_SEQUENCE);
        super.setSelectedIndex(index);
    }

    @Override
    public void createActions() {
        addChangeListener((e) -> {
            int tabbedIndex = getSelectedIndex();
            loadRightPanelView(tabbedIndex);
        });
    }

    private void loadRightPanelView(int tabbedIndex) {
        clientView.setRightPanelViewContent(tabbedIndex);
    }

    /**
     * 获取TestScriptCaseTabPaneView实例
     * @return TestScriptCaseTabPaneView实例
     */
    public TestScriptCaseTabPaneView getTestScriptCaseTabPaneView() {
        return testScriptCaseTabPaneView;
    }
}
