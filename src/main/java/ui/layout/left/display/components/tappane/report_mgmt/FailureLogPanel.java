package ui.layout.left.display.components.tappane.report_mgmt;

import common.constant.ResourceConstant;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.layout.left.display.components.tappane.report_mgmt.panels.DeviceReportTable;
import ui.model.MainModel;
import ui.model.test_executor.TestExecuteStatusObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

public class FailureLogPanel extends JPanel implements BaseView, TestExecuteStatusObserver {

    private final MainModel mainModel;

    private final DeviceReportTable deviceReportTable;
    private final ClientView clientView;
    private final JButton clearButton;

    public FailureLogPanel(ClientView clientView, MainModel mainModel) {
        this.clientView = clientView;
        this.mainModel = mainModel;
        deviceReportTable = new DeviceReportTable(mainModel);
        clearButton = new JButton("清空");
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestExecuteStatusModel().registerObserver(this);
    }

    @Override
    public void createView() {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.add("设备数据", deviceReportTable.addScrollRowHeader());
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
        JPanel bottomPanel = new JPanel(new FlowLayout());
        bottomPanel.add(clearButton, FlowLayout.LEFT);
        clearButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.ActionSequence.clearLogIconPath));
        add(bottomPanel, BorderLayout.SOUTH);
    }

    @Override
    public void createActions() {
        clearButton.addActionListener(e -> clearDeviceReport());
    }

    private void clearDeviceReport() {
        deviceReportTable.clearTable();
    }


}
