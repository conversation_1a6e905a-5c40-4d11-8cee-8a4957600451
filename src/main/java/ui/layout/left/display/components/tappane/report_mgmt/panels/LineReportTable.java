package ui.layout.left.display.components.tappane.report_mgmt.panels;

import common.utils.DateUtils;
import sdk.domain.screen.report.ScreenLineSummary;
import sdk.domain.screen.report.TouchReport;
import ui.base.table.DefaultTable;
import ui.base.renderer.LineWrapCellRenderer;
import ui.base.table.TableCell;
import ui.layout.left.display.components.tappane.report_mgmt.TouchReportListener;

import java.text.NumberFormat;
import java.util.List;

/**
 * 点击报点报告表格
 */
public class LineReportTable extends DefaultTable<ScreenLineSummary> implements TouchReportListener {
    private static int index = 0;
    private static final TableCell<String> timeStamp = new TableCell<>(index++, "时间戳");
    private static final TableCell<String> reportSummary = new TableCell<>(index++, "滑动总结");
    private static final TableCell<String> robotPoint = new TableCell<>(index++, "机械臂测试点");
    private static final TableCell<String> robotPointOfPixel = new TableCell<>(index++, "转换像素点");
    private static final TableCell<String> touchPoint = new TableCell<>(index++, "串口报点");
    private static final TableCell<String> failReason = new TableCell<>(index++, "异常原因");
    private static final TableCell<String> distanceDeviation = new TableCell<>(index++, "距离偏差");
    private static final TableCell<String> angleDeviation = new TableCell<>(index++, "线性度偏差");
    private static final TableCell<String> originalData = new TableCell<>(index, "原始数据");

    private static final NumberFormat numberFormat = NumberFormat.getPercentInstance();

    public LineReportTable() {
        super(0);
        numberFormat.setMinimumFractionDigits(2);
        createView();
        createActions();
    }

    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    protected String[] getColumns() {
        return new String[]{
                timeStamp.getColumnName(),
                reportSummary.getColumnName(),
                robotPoint.getColumnName(),
                robotPointOfPixel.getColumnName(),
                touchPoint.getColumnName(),
                failReason.getColumnName(),
                distanceDeviation.getColumnName(),
                angleDeviation.getColumnName(),
                originalData.getColumnName(),
        };
    }

    @Override
    protected Object[] convertData(ScreenLineSummary report) {
        return new Object[]{
                "",
                "",
                report.getTestPoint() != null ? report.getTestPoint().getRobotPoint() : "",
                report.getTestPoint() != null ? report.getTestPoint().getPixelPoint() : "",
                report.getReportPoint() != null ? report.getReportPoint().getPoint() : "",
                report.getDescription(),
                report.getDistanceDeviation(),
                report.getLinearDeviation() != null ? numberFormat.format(1 - report.getLinearDeviation()) : "",
                report.getReportPoint() != null ? report.getReportPoint().getRefLog() : ""
        };
    }

    @Override
    protected void setPreferredColumn() {
        getColumnModel().getColumn(reportSummary.getColumnIndex()).setPreferredWidth(100);
        getColumnModel().getColumn(robotPoint.getColumnIndex()).setPreferredWidth(35);
        getColumnModel().getColumn(robotPointOfPixel.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(touchPoint.getColumnIndex()).setPreferredWidth(25);
        getColumnModel().getColumn(failReason.getColumnIndex()).setPreferredWidth(75);
        getColumnModel().getColumn(distanceDeviation.getColumnIndex()).setPreferredWidth(25);
        getColumnModel().getColumn(angleDeviation.getColumnIndex()).setPreferredWidth(25);
        getColumnModel().getColumn(originalData.getColumnIndex()).setCellRenderer(new LineWrapCellRenderer());
    }

    private boolean isReportRateValid(TouchReport touchReport) {
        return touchReport.getLowerReportQuantityLimit() > 0 && touchReport.getUpperReportQuantityLimit() > 0;
    }

    private String combinedReportSummary(TouchReport touchReport) {
        return String.format("<html>滑动:%s<br/>线性度:%.3f<br/>实际报点数量:%s<br/>%s%s</html>",
                touchReport.getReportName(),
                touchReport.getLinearRatio(),
                touchReport.getReportQuantity(),
                isReportRateValid(touchReport) ?
                        String.format("要求报点范围:%d~%d<br/>", touchReport.getLowerReportQuantityLimit(), touchReport.getUpperReportQuantityLimit()) :
                        "",
                touchReport.getReportSummary() == null ? "" : touchReport.getReportSummary().replaceAll("\n", "<br/>"));
    }

    @Override
    public void report(TouchReport touchReport) {
        if (!touchReport.isOk()) {
            addRowData(new ScreenLineSummary(), new Object[]{
                    DateUtils.getNow(),
                    combinedReportSummary(touchReport),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    String.join("\n", touchReport.getOriginalDataList())
            });
            List<ScreenLineSummary> screenClickSummaryList = ((TouchReport.LineReport) touchReport).getTouchSummaryList();
            for (ScreenLineSummary screenLineSummary : screenClickSummaryList) {
                if (!screenLineSummary.isOk()) {
                    addRowData(screenLineSummary);
                }
            }
        }
    }
}
