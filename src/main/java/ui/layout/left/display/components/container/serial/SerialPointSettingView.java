package ui.layout.left.display.components.container.serial;

import lombok.Getter;
import sdk.domain.screen.ScreenConfig;
import ui.base.BaseView;
import ui.config.json.devices.serial.SerialConfig;
import ui.layout.left.display.components.container.robot.ui.ScreenCheckPanel;
import ui.layout.left.display.components.container.screen.ScreenParamsPanel;
import ui.layout.left.display.components.container.screen.touchPoint.TouchPointProtocolSettingsPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * @author: QinHao
 * @description: 串口报点设置界面
 * @date: 2024/1/19 10:04
 * @version: 1.0
 */
public class SerialPointSettingView extends JPanel implements BaseView {
    private final ScreenCheckPanel screenCheckPanel;
    @Getter
    private final ScreenParamsPanel screenParamsPanel;
    private final TouchPointProtocolSettingsPanel touchPointProtocolSettingsPanel;
    private final PointCheckButtonPanel pointCheckButtonPanel;

    public SerialPointSettingView(
            MainModel mainModel,
            SerialContainer serialContainer,
            ScreenConfig screenConfig,
            SerialConfig serialConfig
    ) {
        screenParamsPanel = new ScreenParamsPanel(mainModel, screenConfig);
        screenCheckPanel = new ScreenCheckPanel(screenConfig);
        touchPointProtocolSettingsPanel = new TouchPointProtocolSettingsPanel(mainModel, screenConfig);
        pointCheckButtonPanel = new PointCheckButtonPanel(serialContainer, mainModel, screenConfig);

        createView();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        Box box = Box.createVerticalBox();

        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));

        contentPanel.add(screenParamsPanel);
        contentPanel.add(screenCheckPanel);
        contentPanel.add(touchPointProtocolSettingsPanel);
        contentPanel.add(pointCheckButtonPanel);

        JScrollPane scrollPane = new JScrollPane(contentPanel);
        scrollPane.getVerticalScrollBar().setUnitIncrement(20);
        scrollPane.getVerticalScrollBar().setBlockIncrement(100);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS); // 始终显示垂直滚动条
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER); // 不显示水平滚动条
        box.add(scrollPane);
        add(box, BorderLayout.CENTER);
    }


}
