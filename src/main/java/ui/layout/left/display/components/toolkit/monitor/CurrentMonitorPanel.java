package ui.layout.left.display.components.toolkit.monitor;

import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.monitor.MonitorDataPackage;
import sdk.entity.interfaces.IMonitorable;
import ui.base.BaseView;
import ui.model.MainModel;

/**
 * 电流监控面板
 */
public class CurrentMonitorPanel extends DeviceDataMonitorPanel implements BaseView {

    private final MonitorParameterConfig monitorParameterConfig;
    private final IMonitorable device;

    public CurrentMonitorPanel() {
        this(null, null, null);
    }

    public CurrentMonitorPanel(MainModel mainModel, IMonitorable device, MonitorParameterConfig monitorParameterConfig) {
        super(mainModel, DeviceMonitorConstant.currentMonitor, monitorParameterConfig);
        this.device = device;
        this.monitorParameterConfig = monitorParameterConfig;
        createView();
        createActions();
    }

    public void operationStepAdded(Operation operation) {

    }

    @Override
    public void createActions() {
        super.createActions();
        if (device != null) {
            Integer channel = monitorParameterConfig == null ? null : monitorParameterConfig.getChannel();
            setDataMonitorHandler(new DataMonitorHandler() {
                @Override
                public OperationResult monitor(MonitorDataPackage monitorDataPackage) {
                    return device.monitorCurrent(channel, monitorDataPackage);
                }

                @Override
                public Operation addToScript(MonitorDataPackage monitorDataPackage) {
                    Operation operation = Operation.buildOperation((Device) device);
                    if (((Device) device).isMultiChannels()) {
                        operation.getOperationTarget().setChannel(channel);
                    }
                    operation.setOperationMethod(DeviceMethods.monitorCurrent);
                    operation.setOperationObject(monitorDataPackage);
                    operationStepAdded(operation);
                    return operation;
                }
            });
        }
    }

}
