package ui.layout.left.display.components.container.power.kikusui.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TimeLevelResetPulse extends KikusuiWavePulse {

    @JSONField(ordinal = 1)
    private double highVoltage;

    @JSONField(ordinal = 2)
    private double lowVoltage;

    @JSONField(ordinal = 3)
    private double highPulseDuration; //秒

    @JSONField(ordinal = 4)
    private double lowPulseDuration; //秒

}
