package ui.layout.left.display.components.treemenu.actiontree.dialogs.windows;

import lombok.Getter;
import net.miginfocom.swing.MigLayout;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.constants.methods.CommonMethods;
import sdk.domain.PointInt;
import ui.base.BaseView;
import ui.layout.left.display.components.treemenu.actiontree.ActionDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

@Getter
public class WindowsUIDialog extends ActionDialog implements BaseView {

    private final MainModel mainModel;
    private final JTabbedPane tabbedPane = new JTabbedPane();
    private volatile static WindowsUIDialog dialog;
    private final static Map<String, Integer> paneIndexMap = new HashMap<>();
    private final JButton addToScriptButton;
    private JTextField hwndTextField;
    private JSpinner xSpinner;
    private JSpinner ySpinner;

    public static WindowsUIDialog getInstance(MainModel mainModel) {
        if (dialog == null) {
            dialog = new WindowsUIDialog(mainModel);
        }
        return dialog;
    }

    public WindowsUIDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        addToScriptButton = SwingUtil.getAddToScriptButton();
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setTitle("windows ui操作");
        setPreferredSize(new Dimension(400, 300));
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        addFirstTab();
        addSecondTab();
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
        add(addToScriptButton, BorderLayout.SOUTH);
        setLocationRelativeTo(null);
        pack();
    }

    private void addFirstTab() {
        JPanel bringToFrontTab = new JPanel();
        bringToFrontTab.setLayout(new FlowLayout(FlowLayout.LEFT));
        JLabel hwndLabel = new JLabel("句柄：");
        hwndTextField = new JTextField(15);
        bringToFrontTab.add(hwndLabel);
        bringToFrontTab.add(hwndTextField);
        tabbedPane.addTab(CommonMethods.pcBringToFront.getMethodName(), bringToFrontTab);
        paneIndexMap.put(CommonMethods.pcBringToFront.getMethodName(), 0);
    }

    private void addSecondTab() {
        // Second tab
        JPanel pcClickTab = new JPanel(new MigLayout("wrap 2", "[right][grow]"));

        JLabel xLabel = new JLabel("x：");
        SpinnerModel xModel = new SpinnerNumberModel(0, Integer.MIN_VALUE, Integer.MAX_VALUE, 1);
        xSpinner = new JSpinner(xModel);

        JLabel yLabel = new JLabel("y：");
        SpinnerModel yModel = new SpinnerNumberModel(0, Integer.MIN_VALUE, Integer.MAX_VALUE, 1);
        ySpinner = new JSpinner(yModel);

        pcClickTab.add(xLabel);
        pcClickTab.add(xSpinner, "growx");
        pcClickTab.add(yLabel);
        pcClickTab.add(ySpinner, "growx");
        tabbedPane.addTab(CommonMethods.pcClick.getMethodName(), pcClickTab);
        paneIndexMap.put(CommonMethods.pcClick.getMethodName(), 1);
    }

    @Override
    public void createActions() {
        addToScriptButton.addActionListener(e -> {
            int index = tabbedPane.getSelectedIndex();
            for (Map.Entry<String, Integer> entry : paneIndexMap.entrySet()) {
                if (entry.getValue() == index) {
                    if (entry.getKey().equals(CommonMethods.pcBringToFront.getMethodName())) {
                        Operation operation = new Operation();
                        operation.setOperationMethod(CommonMethods.pcBringToFront);
                        operation.setOperationObject(hwndTextField.getText());
                        mainModel.getOperationModel().updateOperation(operation);
                    } else if (entry.getKey().equals(CommonMethods.pcClick.getMethodName())) {
                        Operation operation = new Operation();
                        operation.setOperationMethod(CommonMethods.pcClick);
                        operation.setOperationObject(new PointInt((int) xSpinner.getValue(), (int) ySpinner.getValue()));
                        mainModel.getOperationModel().updateOperation(operation);
                    }
                }
            }
        });
    }

    @Override
    public void applyOperationMethod(OperationMethod operationMethod) {
        Integer index = paneIndexMap.getOrDefault(operationMethod.getMethodName(), 0);
        dialog.tabbedPane.setSelectedIndex(index);
    }
}
