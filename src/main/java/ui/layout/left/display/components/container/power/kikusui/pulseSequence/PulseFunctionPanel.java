package ui.layout.left.display.components.container.power.kikusui.pulseSequence;

import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.CheckPointInfo;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.PowerDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.power.kikusui.entity.VoltageTrigger;
import ui.layout.left.display.components.toolkit.monitor.CurrentMonitorPanel;
import ui.layout.left.display.components.toolkit.monitor.MonitorParameterConfig;
import ui.layout.left.display.components.toolkit.monitor.VoltageMonitorPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

public class PulseFunctionPanel extends JPanel implements BaseView {
    private final JButton currentMonitorButton;
    private final JButton setConstantVoltageButton;

    private final JButton voltageTriggerButton;
    private final JButton voltageMonitorButton;
    private final MainModel mainModel;
    private final PowerDevice powerDevice;
    private JDialog currentMonitorDialog;
    private JDialog voltageMonitorDialog;

    private final DeviceContainer deviceContainer;

    public PulseFunctionPanel(MainModel mainModel, DeviceContainer deviceContainer) {
        this.powerDevice = (PowerDevice) deviceContainer.getDevice();
        this.deviceContainer = deviceContainer;
        this.mainModel = mainModel;

        setConstantVoltageButton = new JButton("设置恒定电压");
        currentMonitorButton = new JButton("电流监控");
        voltageMonitorButton = new JButton("电压监控");
        voltageTriggerButton = new JButton("电压触发检测");
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(1, 3));
        add(setConstantVoltageButton);
        add(currentMonitorButton);
        add(voltageMonitorButton);
        add(voltageTriggerButton);
    }

    @Override
    public void createActions() {
        setConstantVoltageButton.addActionListener(e -> showVoltageDialog());
        currentMonitorButton.addActionListener(e -> currentMonitor());
        voltageMonitorButton.addActionListener(e -> voltageMonitor());
        voltageTriggerButton.addActionListener(e -> voltageTrigger());
    }

    private void showVoltageDialog() {
        ConstantVoltageDialog dialog = new ConstantVoltageDialog(mainModel, powerDevice);
        dialog.setVisible(true);

    }

    private void currentMonitor() {
        if (currentMonitorDialog == null) {
            currentMonitorDialog = new JDialog();
            CurrentMonitorPanel currentMonitorPanel = new CurrentMonitorPanel(mainModel, powerDevice, new MonitorParameterConfig() {
                @Override
                public Integer getChannel() {
                    return null;
                }

                @Override
                public Integer getMaxValue() {
                    return 60;
                }

            }) {
                @Override
                public void operationStepAdded(Operation operation) {
                    currentMonitorDialog.setVisible(false);
                }
            };
            currentMonitorPanel.setOnlyDC(true);
            currentMonitorDialog.add(currentMonitorPanel);
            currentMonitorDialog.setLocationRelativeTo(deviceContainer);
            currentMonitorDialog.setModal(true);
            currentMonitorDialog.pack();
        }
        currentMonitorDialog.setVisible(true);
    }

    private void voltageMonitor() {
        if (voltageMonitorDialog == null) {
            voltageMonitorDialog = new JDialog();
            VoltageMonitorPanel voltageMonitorPanel = new VoltageMonitorPanel(mainModel, powerDevice, new MonitorParameterConfig() {
                @Override
                public Integer getChannel() {
                    return null;
                }

                @Override
                public Integer getMaxValue() {
                    return 60;
                }
            }) {
            };
            voltageMonitorPanel.setVol(true);
            voltageMonitorDialog.add(voltageMonitorPanel);
            voltageMonitorDialog.setLocationRelativeTo(deviceContainer);
            voltageMonitorDialog.setModal(true);
            voltageMonitorDialog.pack();
        }
        voltageMonitorDialog.setVisible(true);
    }

    /**
     * 电压触发设置
     */
    private void voltageTrigger() {
        VoltageTriggerDialog voltageTriggerDialog = new VoltageTriggerDialog(null);
        voltageTriggerDialog.setVisible(true);

//        List<Double> voltageTriggerList = new ArrayList<>();
//        while (true) {
//            String voltageList = JOptionPane.showInputDialog("触发检查的电压(V)", "");
//            if (voltageList == null) {
//                break;
//            }
//            voltageList = voltageList.trim();
//            if (voltageList.isEmpty()) {
//                continue;
//            }
//            String[] voltageStringList = voltageList.split("\\s+");
//            for (String s : voltageStringList) {
//                voltageTriggerList.add(Double.parseDouble(s));
//            }
//            break;
//        }
        if (voltageTriggerDialog.isConformStatus()) {
            ScreenConfig screenConfig = OperationTargetHolder.getScreenKit().loadConfig(mainModel.getAppInfo().getProject());
            screenConfig.setSerialAliasName(voltageTriggerDialog.getVoltageTrigger().getPortName());
            CheckPointInfo checkPointInfo = new CheckPointInfo(screenConfig);
            VoltageTrigger voltageTrigger = voltageTriggerDialog.getVoltageTrigger().withCheckPointInfo(checkPointInfo);
            Operation operation = Operation.buildOperation(powerDevice);
            operation.setOperationMethod(DeviceMethods.kikusuiTriggerAction);
            operation.setOperationObject(voltageTrigger);
            mainModel.getOperationModel().updateOperation(operation);
        }

    }
}
