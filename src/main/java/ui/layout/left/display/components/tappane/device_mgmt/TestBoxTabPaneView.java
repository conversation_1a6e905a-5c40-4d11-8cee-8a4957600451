package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.constants.DeviceModel;
import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.testbox.test.RZCUTestBoxContainer;
import ui.layout.left.display.components.container.testbox.test.TestBoxContainer;
import ui.layout.left.display.components.container.testbox.test.light.LightTestBoxContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 15:23
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
public class TestBoxTabPaneView extends DeviceTabPaneView {
    private DeviceContainer testBoxContainer;
    public TestBoxTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        if (device.getDeviceModel().equals(DeviceModel.TestBox.TEST_BOX)) {
             testBoxContainer = new TestBoxContainer(getClientView(), getMainModel(), device);
        } else if (device.getDeviceModel().equals(DeviceModel.TestBox.LIGHT_TEST_BOX)) {
            testBoxContainer = new LightTestBoxContainer(getClientView(), getMainModel(), device);
        } else {
            testBoxContainer = new RZCUTestBoxContainer(getClientView(), getMainModel(), device);
        }
        setDeviceContainer(tabName, testBoxContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
