package ui.layout.left.display.components.container.testbox.test;

import lombok.Getter;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/14 16:38
 * @description :
 * @modified By :
 * @since : 2023/6/14
 **/
public class TriStateOutputPane implements LayoutGenerator {
    private final ConfigBoardCardPane configBoardCardPane;
    private final PanelGenerator generator;
    @Getter
    private final List<TriStateComboBox> triStateComboBoxList = new ArrayList<>();

    public TriStateOutputPane(ConfigBoardCardPane configBoardCardPane, PanelGenerator generator) {
        this.configBoardCardPane = configBoardCardPane;
        this.generator = generator;
    }

    public JPanel getTriStateOutputPane() {
        return generator.generatePanel(64, "CH", "三态输出设定", 0, this);
    }

    @Override
    public CustomizeGridLayout formLayout() {
        return new CustomizeGridLayout(13, 5);
    }

    @Override
    public String labelGenerator(int channel) {
        channel = channel + 1;
        String channelText = String.valueOf(channel);
        if (channel < 10) {
            channelText = "0" + channelText;
        }
        return channelText;
    }

    @Override
    public JComponent componentGenerator(int channel) {
        TriStateComboBox triStateComboBox = new TriStateComboBox(configBoardCardPane, channel + 1);
        triStateComboBoxList.add(triStateComboBox);
        return triStateComboBox;
    }
}
