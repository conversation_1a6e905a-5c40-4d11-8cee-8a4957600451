package ui.layout.left.display.components.container.testbox.test;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceType;
import sdk.entity.PowerDevice;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;

import javax.swing.*;
import java.awt.*;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

import static ui.utils.SwingUtil.setPanelBorder;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/5 9:48
 * @description :
 * @modified By :
 * @since : 2023/7/5
 **/
@Slf4j
public class TestBoxPowerPane implements LayoutGenerator {
    private final DeviceContainer deviceContainer;
    private final PanelGenerator generator;
    @Getter
    private final List<SpinnerSliderListener> changeListenerList;

    private final DecimalFormat decimalFormat;
    private final ClientView clientView;
    @Getter
    private DefaultChangeValueButton switchButton;

    public TestBoxPowerPane(ClientView clientView, DeviceContainer deviceContainer, PanelGenerator generator) {
        this.clientView = clientView;
        this.deviceContainer = deviceContainer;
        this.generator = generator;
        changeListenerList = new ArrayList<>();
        decimalFormat = new DecimalFormat("0.0");
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
    }

    public JPanel getTestBoxPowerPane() {
        JPanel panel = generator.generatePanel(1, "电压", null, 0, this);
        JLabel label = new JLabel("电源开关:");
        label.setFont(new Font(null, Font.BOLD, 12));
        label.setForeground(Color.BLUE);
        switchButton = new DefaultChangeValueButton("打开", "关闭") {
            @Override
            protected void changeValueListener(boolean isOpen) {
                setOutput(isOpen);
            }
        };
        JPanel rootPanel = new JPanel(new BorderLayout());
        JPanel buttonPanel = new JPanel();
        buttonPanel.add(label);
        buttonPanel.add(switchButton);
        rootPanel.add(panel, BorderLayout.CENTER);
        rootPanel.add(buttonPanel, BorderLayout.EAST);
        setPanelBorder(rootPanel, "电源设定");
        return rootPanel;
    }


    @Override
    public CustomizeGridLayout formLayout() {
        return new CustomizeGridLayout(1, 1);
    }

    @Override
    public String labelGenerator(int channel) {
        return "";
    }

    @Override
    public JComponent componentGenerator(int channel) {
        SpinnerNumberModel voltageSpinnerModel = new SpinnerNumberModel(12.0, 1.0, 24.0, 0.1);
        JSpinner jSpinner = new JSpinner(voltageSpinnerModel);
        JSlider jSlider = new JSlider(JSlider.HORIZONTAL, 1, 24, 12);
        Hashtable<Integer, JComponent> hashtable = new Hashtable<>();
        hashtable.put(1, new JLabel("1"));      //  0  刻度位置，显示 "Start"
        hashtable.put(12, new JLabel("12"));    //  10 刻度位置，显示 "Middle"
        hashtable.put(24, new JLabel("24"));    //  20 刻度位置，显示 "End"
        jSlider.setLabelTable(hashtable);
        jSlider.setPreferredSize(new Dimension(380, 50));
        jSlider.setMajorTickSpacing(1);
        jSlider.setMinorTickSpacing(1);
        jSlider.setPaintLabels(true);
        jSlider.setPaintTicks(true);
        SpinnerSliderChangeListener spinnerSliderChangeListener = new SpinnerSliderChangeListener(jSpinner, jSlider) {
            @Override
            public void sliderChangeEvent() {
                if (!firstConnectDevice) {
                    //设定电压
//                float value = Float.parseFloat(jSpinner.getValue().toString());
//                float value = ((Double)jSpinner.getValue()).floatValue();
                    float value = Float.parseFloat(jSpinner.getValue().toString());
                    setVoltage(value);
                }
            }
        };
        changeListenerList.add(spinnerSliderChangeListener);
        return new SpinnerSlider(jSpinner, jSlider, spinnerSliderChangeListener, false);
    }

    public boolean fetchOutput() {
        int deviceIndex = deviceContainer.getDevice().getDeviceIndex();
        Boolean output = clientView.getDeviceTree().executeDevice(DeviceType.DEVICE_POWER, deviceIndex, device -> {
            PowerDevice powerDevice = (PowerDevice) device;
            if (device != null) {
                OperationResult operationResult = powerDevice.fetchOutput();
                return operationResult.isOk();
            }
            return false;
        });
        return output != null && output;
    }


    public double fetchVoltage() {
        //TODO:读电压的数据
        double defaultVoltage = 12.0f;
        int deviceIndex = deviceContainer.getDevice().getDeviceIndex();
        Double voltage = clientView.getDeviceTree().executeDevice(DeviceType.DEVICE_POWER, deviceIndex, device -> {
            PowerDevice powerDevice = (PowerDevice) device;
            OperationResult operationResult = powerDevice.fetchVoltage();
            if (operationResult.isOk()) {
                double voltageValue = Double.parseDouble(decimalFormat.format(operationResult.getData()));
                return voltageValue > 0 ? voltageValue : defaultVoltage;
            }
            return null;
        });
        return voltage == null ? defaultVoltage : voltage;
    }

    private void setOutput(boolean selected) {
        int deviceIndex = deviceContainer.getDevice().getDeviceIndex();
        clientView.getDeviceTree().executeDevice(DeviceType.DEVICE_POWER, deviceIndex, device -> {
            PowerDevice powerDevice = (PowerDevice) device;
            if (selected) {
                log.debug("{} output on", powerDevice.getDeviceName());
                powerDevice.outputOn();
            } else {
                log.debug("{} output off", powerDevice.getDeviceName());
                powerDevice.outputOff();
            }
            return null;
        });
    }

    private void setVoltage(float value) {
        int deviceIndex = deviceContainer.getDevice().getDeviceIndex();
        clientView.getDeviceTree().executeDevice(DeviceType.DEVICE_POWER, deviceIndex, device -> {
            PowerDevice powerDevice = (PowerDevice) device;
            powerDevice.setVoltage(1, value);
            return null;
        });
    }

}
