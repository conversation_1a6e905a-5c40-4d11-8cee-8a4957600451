package ui.layout.left.display.components.container.serial;

import sdk.domain.MessageText;
import sdk.entity.SerialDevice;
import ui.base.BaseView;
import ui.config.json.devices.serial.SerialConfig;
import ui.config.json.devices.serial.SerialMessage;
import ui.entry.ClientView;

import javax.swing.*;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.util.Objects;

/**
 * 串口高级设置面板
 */
public class SerialAdvancedSettingsPanel extends JPanel implements BaseView {
    //    private final JCheckBox isEnableTouchPointTransferCheckbox;
    private final JTextField sendTextField;
    private final JCheckBox isHexCheckBox;
    private final JCheckBox isSendCheckBox;
    private final JButton sendButton;
    private final SerialConfig serialConfig;
    private final SerialDevice serialDevice;
    private final ClientView clientView;
    private final JComboBox<String> logSaveTypeCombo;
    private final JTextField logMaxFileSizeField;
    private final JLabel logMaxFileSizeLabel;

    private final JComboBox<String> logSaveTimePeriodCombo;
    private final JCheckBox useCustomLogPathCheckBox;
    private final JTextField customLogPathField;
    private final JButton choosePathButton;
    private final JSpinner receiveTimeoutSpinner;

    public SerialAdvancedSettingsPanel(ClientView clientView, SerialDevice serialDevice, SerialConfig serialConfig) {
        this.clientView = clientView;
        this.serialDevice = serialDevice;
        this.serialConfig = serialConfig;
        sendTextField = new JTextField();
        isHexCheckBox = new JCheckBox();
        isSendCheckBox = new JCheckBox();
        logSaveTypeCombo = new JComboBox<>(new String[]{"按时间保存", "按大小保存", "按脚本保存"});
        sendButton = new JButton("发送");
        logMaxFileSizeField = new JTextField(5);
        logMaxFileSizeLabel = new JLabel("MB");
        logSaveTimePeriodCombo = new JComboBox<>(new String[]{"天", "小时"});

        useCustomLogPathCheckBox = new JCheckBox("使用自定义日志路径");
        customLogPathField = new JTextField(20);
        choosePathButton = new JButton("选择路径");
//        isEnableTouchPointTransferCheckbox = new JCheckBox("是否启用报点实时传输");
        receiveTimeoutSpinner = new JSpinner(new SpinnerNumberModel(2, 0.1, 10000.0, 0.1));
        createView();
        createActions();
        restoreView();
    }

    @Override
    public void restoreView() {
//        isEnableTouchPointTransferCheckbox.setSelected(serialConfig.isEnableTouchDataTransfer());
        for (SerialMessage serialMessage : serialConfig.getTextMapSendWhenOpen().values()) {
            sendTextField.setText(serialMessage.getText());
            isHexCheckBox.setSelected(serialMessage.isHex());
            isSendCheckBox.setSelected(serialMessage.isValid());
        }
        logSaveTypeCombo.setSelectedIndex(serialConfig.getLogSaveType());
        if (serialConfig.getLogSaveTimePeriod() != null) {
            logSaveTimePeriodCombo.setSelectedItem(serialConfig.getLogSaveTimePeriod().equals("hour") ? "小时" : "天");
        }
        if (serialConfig.getLogMaxFileSize() != null) {
            if (serialConfig.getLogMaxFileSize().endsWith("MB")) {
                logMaxFileSizeField.setText(serialConfig.getLogMaxFileSize().substring(0, serialConfig.getLogMaxFileSize().length() - 2));
            }
        }
        useCustomLogPathCheckBox.setSelected(serialConfig.isUseCustomLogPath());
        if (serialConfig.getCustomLogPath() != null) {
            customLogPathField.setText(serialConfig.getCustomLogPath());
        }
        receiveTimeoutSpinner.setValue(serialConfig.getReceiveTimeout());
        updateLogMaxFileSizeVisibility();
        updateLogSaveTimePeriodVisibility();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());

        Box mainBox = Box.createVerticalBox();
        Box box1 = Box.createHorizontalBox();
//        box1.add(isEnableTouchPointTransferCheckbox, SwingUtil.getRowGridBagConstraints(0, 0, 0));

        JPanel openAndSendPanel = new JPanel();
        openAndSendPanel.add(new JLabel("打开即发送:"));
        openAndSendPanel.add(sendTextField);
        sendTextField.setColumns(30);
        openAndSendPanel.add(new JLabel("是否十六进制:"));
        openAndSendPanel.add(isHexCheckBox);
        openAndSendPanel.add(new JLabel("是否发送:"));
        openAndSendPanel.add(isSendCheckBox);
        openAndSendPanel.add(sendButton);

        logSaveTypeCombo.setSelectedIndex(0);
        JPanel logPanel = new JPanel();
/*        logPanel.add(new JLabel("自动保存日志方式:"));
        logPanel.add(logSaveTypeCombo);
        logPanel.add(logMaxFileSizeField);
        logPanel.add(logMaxFileSizeLabel);
        logPanel.add(logSaveTimePeriodCombo);*/

        JPanel logSavePath = new JPanel();
        // 功能移动至串口常规操作页面
/*        logSavePath.add(useCustomLogPathCheckBox);
        logSavePath.add(customLogPathField);
        // 增加选择路径按钮
        logSavePath.add(choosePathButton);*/

        logMaxFileSizeField.setVisible(false);
        logMaxFileSizeLabel.setVisible(false);
        logSaveTimePeriodCombo.setVisible(false);
        Box box2 = Box.createHorizontalBox();
        JPanel receiveTimeoutPanel = new JPanel(new FlowLayout());

        receiveTimeoutPanel.add(new JLabel("接收超时(秒):"));
        receiveTimeoutPanel.add(receiveTimeoutSpinner);
        box2.add(receiveTimeoutPanel);
        mainBox.add(box1);
        mainBox.add(openAndSendPanel);
        mainBox.add(logPanel);
        mainBox.add(logSavePath);
        mainBox.add(box2);
        add(mainBox, BorderLayout.NORTH);

    }

    @Override
    public void createActions() {
        sendButton.addActionListener(e -> {
            MessageText messageText = new MessageText();
            messageText.setHex(isHexCheckBox.isSelected());
            messageText.setSendText(sendTextField.getText().trim());
            serialDevice.send(messageText);
        });
        sendTextField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                super.focusLost(e);
                serialConfig.getSerialMessage(0).setText(sendTextField.getText());
                serialDevice.updateConfig(serialConfig);
            }
        });
        isHexCheckBox.addItemListener(e -> {
            serialConfig.getSerialMessage(0).setHex(isHexCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
        });
        isSendCheckBox.addItemListener(e -> {
            serialConfig.getSerialMessage(0).setValid(isSendCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
        });
//        isEnableTouchPointTransferCheckbox.addItemListener(e -> {
//            serialConfig.setEnableTouchDataTransfer(isEnableTouchPointTransferCheckbox.isSelected());
//            serialDevice.updateConfig(serialConfig);
//        });
        logSaveTypeCombo.addActionListener(e -> {
            int selectedIndex = logSaveTypeCombo.getSelectedIndex();
            logSaveTimePeriodCombo.setVisible(selectedIndex != 2);
            updateLogMaxFileSizeVisibility();
            updateLogSaveTimePeriodVisibility();
            serialConfig.setLogSaveType(selectedIndex);
            serialDevice.updateConfig(serialConfig);
        });
        logSaveTimePeriodCombo.addActionListener(e -> {
            String selectedPeriod = Objects.requireNonNull(logSaveTimePeriodCombo.getSelectedItem()).toString().equals("小时") ? "hour" : "day";
            serialConfig.setLogSaveTimePeriod(selectedPeriod);
            serialDevice.updateConfig(serialConfig);
        });

        logMaxFileSizeField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                try {
                    int size = Integer.parseInt(logMaxFileSizeField.getText());
                    serialConfig.setLogMaxFileSize(size + "MB");
                    serialDevice.updateConfig(serialConfig);
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(clientView, "请输入有效的文件大小。", "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        });

        useCustomLogPathCheckBox.addItemListener(e -> {
            serialConfig.setUseCustomLogPath(useCustomLogPathCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
        });
        customLogPathField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                serialConfig.setCustomLogPath(customLogPathField.getText().trim());
                serialDevice.updateConfig(serialConfig);
            }
        });

        choosePathButton.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
            int option = fileChooser.showOpenDialog(clientView);
            if (option == JFileChooser.APPROVE_OPTION) {
                customLogPathField.setText(fileChooser.getSelectedFile().getAbsolutePath());
                serialConfig.setCustomLogPath(customLogPathField.getText().trim());
                serialDevice.updateConfig(serialConfig);
            }
        });
        receiveTimeoutSpinner.addChangeListener(e -> {
            float timeout = ((Number) receiveTimeoutSpinner.getValue()).floatValue();
            serialConfig.setReceiveTimeout(timeout);
            serialDevice.updateConfig(serialConfig);
        });
    }

    private void updateLogMaxFileSizeVisibility() {
        boolean isSizeBased = logSaveTypeCombo.getSelectedIndex() == 1;
        logMaxFileSizeField.setVisible(isSizeBased);
        logMaxFileSizeLabel.setVisible(isSizeBased);
    }

    private void updateLogSaveTimePeriodVisibility() {
        boolean isTimeBased = logSaveTypeCombo.getSelectedIndex() == 0;
        logSaveTimePeriodCombo.setVisible(isTimeBased);
    }

}
