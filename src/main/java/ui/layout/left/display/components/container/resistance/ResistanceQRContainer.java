package ui.layout.left.display.components.container.resistance;

import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.ResistanceQRDevice;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * @author: <PERSON><PERSON>ao
 * @description: 程控电阻仪
 * @date: 2024/7/30 14:04
 */
public class ResistanceQRContainer extends DeviceContainer {

    private final JSpinner resistanceSpinner;
    private final JLabel showResistanceLabel;
    private final JButton setResistanceButton;
    private final JButton addSetResistanceToScriptButton;
    private final ResistanceQRDevice resistanceQRDevice;
    private final JButton queryButton;
    private final JSpinner stepSpinner;
    private final JButton decrementButton;
    private final JLabel resistanceLabel;
    private final JButton incrementButton;


    public ResistanceQRContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        resistanceQRDevice = (ResistanceQRDevice) device;
        resistanceSpinner = new JSpinner();
        showResistanceLabel = new JLabel("0");
        queryButton = new JButton("查询阻值");
        stepSpinner = new JSpinner();
        decrementButton = new JButton("-");
        resistanceLabel = new JLabel("0");
        incrementButton = new JButton("+");
        setResistanceButton = SwingUtil.getDebugButton();
        addSetResistanceToScriptButton = SwingUtil.getAddToScriptButton();
        createView();
        createActions();

    }

    public void createView() {
        setLayout(new BorderLayout(0, 0));

        JPanel panel = new JPanel();
        add(panel, BorderLayout.NORTH);
        panel.setLayout(new GridLayout(0, 1, 0, 0));

        JPanel panel_1 = new JPanel();
        FlowLayout flowLayout = (FlowLayout) panel_1.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        panel.add(panel_1);

        JLabel lblNewLabel = new JLabel("电阻设置：");
        panel_1.add(lblNewLabel);

        resistanceSpinner.setModel(new SpinnerNumberModel(0.000, null, null, 100));
        panel_1.add(resistanceSpinner);
        panel_1.add(setResistanceButton);
        panel_1.add(addSetResistanceToScriptButton);

        JLabel lblNewLabel_2 = new JLabel("Ω");
        panel_1.add(lblNewLabel_2);

        JLabel lblNewLabel_5 = new JLabel("当前阻值：");
        panel_1.add(lblNewLabel_5);

        panel_1.add(showResistanceLabel);

        JLabel lblNewLabel_2_1 = new JLabel("Ω");
        panel_1.add(lblNewLabel_2_1);

        panel_1.add(queryButton);

        JPanel panel_2 = new JPanel();
        FlowLayout flowLayout_1 = (FlowLayout) panel_2.getLayout();
        flowLayout_1.setAlignment(FlowLayout.LEFT);
        panel.add(panel_2);

        JLabel lblNewLabel_1 = new JLabel("步进值：");
        panel_2.add(lblNewLabel_1);
        stepSpinner.setModel(new SpinnerNumberModel(0.000, null, null, 100));
        panel_2.add(stepSpinner);

        JLabel lblNewLabel_3 = new JLabel("Ω");
        panel_2.add(lblNewLabel_3);

        panel_2.add(decrementButton);

        JLabel lblNewLabel_4 = new JLabel("当前阻值：");
        panel_2.add(lblNewLabel_4);

        panel_2.add(resistanceLabel);

        JLabel lblNewLabel_6 = new JLabel("Ω");
        panel_2.add(lblNewLabel_6);

        panel_2.add(incrementButton);

    }

    public void createActions() {
        queryButton.addActionListener(e -> queryResistance());
        setResistanceButton.addActionListener(e -> setResistance(false));
        addSetResistanceToScriptButton.addActionListener(e -> setResistance(true));
        incrementButton.addActionListener(e -> adjustResistance(true));
        decrementButton.addActionListener(e -> adjustResistance(false));
    }

    private void setResistance(boolean addToScript) {
        double resistanceValue = ((Number) resistanceSpinner.getValue()).doubleValue();
        float resistance = (float) resistanceValue;
        Operation operation = Operation.buildOperation(resistanceQRDevice);
        operation.setOperationMethod(DeviceMethods.resistanceSet);
        operation.setOperationObject(resistance);
        if (addToScript) {
            getMainModel().getOperationModel().updateOperation(operation);
        } else {
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                SwingUtil.showWarningDialog(this, operationResult.getMessage());
            }
        }
    }

    private void adjustResistance(boolean increment) {
        double stepValue = ((Number) stepSpinner.getValue()).doubleValue();
        float step = (float) stepValue;
        if (step > 0) {
            Operation operation = Operation.buildOperation(resistanceQRDevice);
            operation.setOperationMethod(DeviceMethods.resistanceSetStep);
            operation.setOperationObject(increment ? step : -step);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isOk()) {
                queryResistance();
            } else {
                SwingUtil.showWarningDialog(this, operationResult.getMessage());
            }
        }
    }

    private void queryResistance() {
        // 查询阻值
        Operation operation = Operation.buildOperation(resistanceQRDevice);
        operation.setOperationMethod(DeviceMethods.resistanceGet);
        OperationResult operationResult = BaseHttpClient.executeOperation(operation);
        if (operationResult.isOk()) {
            showResistanceLabel.setText(String.valueOf(operationResult.getData()));
            resistanceLabel.setText(String.valueOf(operationResult.getData()));
        } else {
            SwingUtil.showWarningDialog(this, operationResult.getMessage());
        }
    }
}
