package ui.layout.left.display.components.container.testbox.test;

import lombok.Getter;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

import static ui.layout.left.display.components.container.testbox.test.PWMInputPane.makeLabel;


/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/12 18:54
 * @description :
 * @modified By :
 * @since : 2023/6/12
 **/
public class CompositePanel extends JPanel {
    @Getter
    private final JSpinner freqSpinner;
    @Getter
    private final JSlider freqSlider;
    @Getter
    private final JSpinner dutySpinner;
    @Getter
    private final JSlider dutySlider;

    private final ConfigBoardCardPane configBoardCardPane;

    private final int channel;
    @Getter
    private SpinnerSliderChangeListener freqChangeListener;
    @Getter
    private SpinnerSliderChangeListener dutyChangeListener;
    @Getter
    private final List<SpinnerSliderListener> changeListenerList = new ArrayList<>();

    public CompositePanel(ConfigBoardCardPane configBoardCardPane, int channel) {
        this.configBoardCardPane = configBoardCardPane;
        this.channel = channel;
        JPanel freqPanel = new JPanel();
        freqPanel.add(makeLabel("频率(Hz)"));
        SpinnerNumberModel freqSpinnerModel = new SpinnerNumberModel(1000.0, 0.0, 10000.0, 0.1);
        freqSpinner = new JSpinner(freqSpinnerModel);
        JSpinner.NumberEditor editor = (JSpinner.NumberEditor) freqSpinner.getEditor();
        editor.getTextField().setHorizontalAlignment(SwingConstants.RIGHT);
        freqSlider = new JSlider(JSlider.HORIZONTAL, 0, 10000, 1000);
        freqSlider.setMajorTickSpacing(200);
        freqSlider.setMinorTickSpacing(50);
        SpinnerSliderChangeListener freqChangeListener = new SpinnerSliderChangeListener(freqSpinner, freqSlider) {
            @Override
            public void sliderChangeEvent() {
                if (!firstConnectDevice) {
                    setPWMOutput();
                }
            }
        };
        freqPanel.add(new SpinnerSlider(freqSpinner, freqSlider, freqChangeListener, true));

        JPanel dutyPanel = new JPanel();
        dutyPanel.add(makeLabel("占空比(%)"));
        SpinnerNumberModel dutySpinnerNumberModel = new SpinnerNumberModel(100.0, 0.0, 100.0, 0.1);
        dutySpinner = new JSpinner(dutySpinnerNumberModel);
        dutySlider = new JSlider(JSlider.HORIZONTAL, 0, 100, 100);
        dutySlider.setMajorTickSpacing(20);
        dutySlider.setMinorTickSpacing(5);
        SpinnerSliderChangeListener dutyChangeListener = new SpinnerSliderChangeListener(dutySpinner, dutySlider) {
            @Override
            public void sliderChangeEvent() {
                if (!firstConnectDevice) {
                    setPWMOutput();
                }
            }
        };
        dutyPanel.add(new SpinnerSlider(dutySpinner, dutySlider, dutyChangeListener, true));
        add(freqPanel);
        add(dutyPanel);
        changeListenerList.add(freqChangeListener);
        changeListenerList.add(dutyChangeListener);
    }

    private void setPWMOutput() {
//        float freqValue = ((Double)freqSpinner.getValue()).floatValue();
//        float dutyValue = ((Double)dutySpinner.getValue()).floatValue();
        float freqValue = Float.parseFloat(freqSpinner.getValue().toString());
        float dutyValue = Float.parseFloat(dutySpinner.getValue().toString());
//        float freqValue = BigDecimal.valueOf((double) freqSpinner.getValue()).floatValue();
//        float dutyValue = BigDecimal.valueOf((double) dutySpinner.getValue()).floatValue();
        PWMEntity pwmEntity = new PWMEntity(freqValue, dutyValue);
//        System.out.println("setPWMOutput CHANNEL===" + channel);
        configBoardCardPane.setPWMOutput(channel, pwmEntity);
    }

}

