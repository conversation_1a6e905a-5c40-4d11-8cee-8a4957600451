package ui.layout.left.display.components.container.electric_relay;

import common.constant.DeviceConstants;
import llm.SystemPrompt;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.ChannelSwitch;
import sdk.domain.Device;
import sdk.entity.ElectricRelayDevice;
import ui.config.json.devices.electric_relay.ElectricRelayConfig;
import ui.config.json.devices.electric_relay.ElectricRelayDisplayConfig;
import ui.config.json.devices.electric_relay.ElectricRelayService;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ElectricRelayContainer extends DeviceContainer {
    private static final Log log = LogFactory.getLog(ElectricRelayContainer.class);
    private final ElectricRelayDevice electricRelayDevice;
    private final ElectricRelayConfig electricRelayConfig;
    private JTextField delayTimeField; // 统一延迟时间输入框
    private final ElectricRelayDisplayConfig instance; // 持久化文件操作
    private volatile Boolean relayStatus = false;
    // 添加成员变量存储按钮引用
    private final Map<Integer, JToggleButton> channelButtons = new ConcurrentHashMap<>();

    public ElectricRelayContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
//        electricRelayConfig = ((ElectricRelayDevice) device).loadConfig(mainModel.getAppInfo().getProject());

        // 从数据库获取数据
        electricRelayConfig = ElectricRelayService.getRelayTextConfig(mainModel.getAppInfo().getProject(), device.getAliasName());
        electricRelayDevice = (ElectricRelayDevice) device;
        instance = ElectricRelayDisplayConfig.getInstance();
        // 添加主容器，使用BorderLayout
        setLayout(new BorderLayout());

        JScrollPane scrollPane = createChannelScrollPane();
        add(scrollPane, BorderLayout.CENTER);

        // 创建延迟时间设置面板
        JPanel delayPanel = createDelayPanel();
        add(delayPanel, BorderLayout.SOUTH);

        createActions();
    }

    private JPanel createDelayPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createTitledBorder("延迟时间设置"));
        panel.add(new JLabel("统一延迟时间(ms):"));
        delayTimeField = new JTextField(10);
        delayTimeField.setText(instance.loadDelayTime(electricRelayDevice.getAliasName())); // 默认值
        panel.add(delayTimeField);
        return panel;
    }

    @Override
    public void createActions() {
        // 添加焦点监听器
        delayTimeField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                String text = delayTimeField.getText();
                // 保存到配置文件且传到后端
                instance.saveSyncDelayTime(electricRelayDevice.getAliasName(), text);
            }
        });

        // 添加回车键提交
//        delayTimeField.addActionListener(e -> delayTimeField.transferFocus());
    }

    /**
     * 当设备连接成功时回调此方法
     *
     * @param device          已连接的设备对象
     * @param autoOpenChannel 是否自动打开通信通道，true表示自动打开
     */

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        // 设备连接状态 判断是否传入脚本序列
        relayStatus = true;
        // 设备连接修改数据库状态
        electricRelayConfig.setConnectedStatus(DeviceConstants.CONNECTED_STATUS_1);
        electricRelayConfig.setSwitchOn(DeviceConstants.SWITCH_OFF);
        ElectricRelayService.updateConnectSwitchStatus(electricRelayConfig);

        // 更新系统提示中的继电器配置
        SystemPrompt.getInstance().setElectricRelayConfig(electricRelayConfig.getConfig());
    }

    /**
     * 当设备断开连接时回调此方法
     *
     * @param device 已断开连接的设备对象
     */
    @Override
    public void deviceDisconnected(Device device) {
        // 设备连接状态 判断是否传入脚本序列
        relayStatus = false;

        // 设备断开连接修改数据库状态
        electricRelayConfig.setConnectedStatus(DeviceConstants.CONNECTED_STATUS_0);
        ElectricRelayService.updateRelayStatusConfig(electricRelayConfig);
        SystemPrompt.getInstance().setElectricRelayConfig("");
    }

    /**
     * 设备断开连接时回调此方法
     */
    @Override
    public void deviceRemoved(Device device) {
        // 设备断开连接修改数据库状态 开关全部为关闭状态
        electricRelayConfig.setConnectedStatus(DeviceConstants.CONNECTED_STATUS_0);
        electricRelayConfig.setSwitchOn(DeviceConstants.SWITCH_OFF);
        ElectricRelayService.updateConnectSwitchStatus(electricRelayConfig);
    }

    /**
     * 创建一个用于显示频道列表的滚动面板
     * 该函数负责创建并返回一个JScrollPane对象，该对象用于容纳频道列表组件，
     * 并提供滚动功能以便在有限的空间内浏览大量频道。
     *
     * @return JScrollPane 返回一个配置好的滚动面板，用于显示频道列表
     */
    private JScrollPane createChannelScrollPane() {
        Box box = Box.createVerticalBox();
        if (electricRelayConfig.getPhyText() == null || electricRelayConfig.getPhyText().isEmpty()) {
            // 如果列表为空，使用默认值初始化
            List<String> defaultPhyText = new ArrayList<>(Collections.nCopies(16, ""));
            for (int i = 1; i <= 16; i++) {
                switch (i) {
                    case 1:
                        defaultPhyText.set(0, "点火KL15");
                        break;
                    case 2:
                        defaultPhyText.set(i - 1, "蓄电池KL30");
                        break;
                    case 3:
                        defaultPhyText.set(i - 1, "ACC");
                        break;
                    default:
                        defaultPhyText.set(i - 1, ""); // 其他通道默认为空
                }
            }
            electricRelayConfig.setPhyText(defaultPhyText);
            saveConfig();
        } else if (electricRelayConfig.getPhyText().size() < 16) {
            for (int i = electricRelayConfig.getPhyText().size(); i < 16; i++) {
                electricRelayConfig.getPhyText().add("");
            }
        }

        for (int i = 1; i <= 16; i++) {
            String phyLabel = electricRelayConfig.getPhyText().get(i - 1);
            box.add(createRow(i, phyLabel));
        }

        // 添加通道面板到中间区域
        return new JScrollPane(box);
    }

    private JPanel createRow(int channelOrder, String phyLabel) {
        JPanel panel = new JPanel();
        panel.add(new JLabel(String.format("通道%2d", channelOrder)));

        JTextField phyTextField = new JTextField(5);
        panel.add(phyTextField);

        // 先设置文本
        phyTextField.setText(phyLabel);

        JToggleButton switchButton = new JToggleButton();
        // 存储按钮供外部使用
        channelButtons.put(channelOrder, switchButton);
        switchButton.setText(DeviceConstants.SWITCH_OFF);
        switchButton.addActionListener(e -> {
            // 立即更新UI状态
            if (switchButton.isSelected()) {
                switchButton.setText(DeviceConstants.SWITCH_ON);
            } else {
                switchButton.setText(DeviceConstants.SWITCH_OFF);
            }

            // 异步处理数据库和继电器操作
            new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() throws Exception {
                    // 更新数据库
                    updatePhyText(channelOrder, phyTextField.getText(), switchButton.getText());

                    // 发送继电器控制命令
                    ChannelSwitch channelSwitch = new ChannelSwitch();
                    channelSwitch.setChannel(channelOrder);
                    channelSwitch.setSwitchOn(switchButton.isSelected());
                    channelSwitch.setPhyLabel(phyTextField.getText());
                    channelSwitch.setDelayTime(delayTimeField.getText()); // 设置延迟时间
                    electricRelayDevice.switchRelay(channelSwitch);
                    return null;
                }
            }.execute();
        });



        // 再添加监听器
        phyTextField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updatePhyText(channelOrder, phyTextField.getText(), switchButton.getText());
            }
        });

        panel.add(switchButton);

        // 添加到脚本按钮
        JButton addToScriptButton = SwingUtil.getAddToScriptButton();
        panel.add(addToScriptButton);
        addToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(electricRelayDevice);
            ChannelSwitch channelSwitch = new ChannelSwitch();
            channelSwitch.setChannel(channelOrder);
            channelSwitch.setSwitchOn(switchButton.isSelected());
            channelSwitch.setPhyLabel(phyTextField.getText());
            channelSwitch.setDelayTime(delayTimeField.getText()); // 设置延迟时间
            operation.setOperationMethod(DeviceMethods.switchRelay);
            operation.setOperationObject(channelSwitch);
            operation.setFriendlyOperationObject(channelSwitch.getPhyLabel());
            getMainModel().getOperationModel().updateOperation(operation);
        });

        return panel;
    }

    private void updatePhyText(int channelOrder, String newText, String switchText) {
        electricRelayConfig.getPhyText().set(channelOrder - 1, newText);
        electricRelayConfig.setChannel(channelOrder);
        electricRelayConfig.setSwitchOn(switchText);
        saveConfig();
    }

    /**
     * 保存继电器文本配置信息
     */
    private void saveConfig() {
        // 配置文件该为数据库保存
//        electricRelayDevice.updateConfig(electricRelayConfig);
        // 更新配置对象中的别名信息
        electricRelayConfig.setAliasName(electricRelayDevice.getAliasName());

        // 调用服务层方法更新继电器文本配置
        ElectricRelayService.updateRelayTextConfig(electricRelayConfig);

        // 设备断开不在线时，更新系统提示中的继电器配置
        if (relayStatus) {
            // 更新系统提示中的继电器配置
//            System.out.println("更新系统提示中的继电器配置");
            SystemPrompt.getInstance().setElectricRelayConfig(electricRelayConfig.getConfig());
        }
    }

    /**
     * 外部触发通道开关
     *
     * @param channel 通道号 (1-16)
     */
    public void triggerChannel(int channel) {
        log.info("外部触发通道开关");
        SwingUtilities.invokeLater(() -> {
            JToggleButton button = channelButtons.get(channel);
            if (button != null) {
                button.doClick();
            }
        });
    }
}
