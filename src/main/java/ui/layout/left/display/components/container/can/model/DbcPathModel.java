package ui.layout.left.display.components.container.can.model;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class DbcPathModel {

    private final List<String> paths = new CopyOnWriteArrayList<>();
    private final List<ChangeListener> listeners = new CopyOnWriteArrayList<>();

    public interface ChangeListener {
        void onPathsChanged(List<String> newPaths);
    }

    public void addListener(ChangeListener listener) {
        listeners.add(listener);
        listener.onPathsChanged(new ArrayList<>(paths));
    }

    public void removeListener(ChangeListener listener) {
        listeners.remove(listener);
    }

    public void setPaths(List<String> newPaths) {
        if (!newPaths.equals(paths)) {
            paths.clear();
            paths.addAll(newPaths);
            notifyListeners();
        }
    }

    public List<String> getPaths() {
        return new ArrayList<>(paths);
    }

    private void notifyListeners() {
        List<String> snapshot = new ArrayList<>(paths);
        listeners.forEach(listener ->
                listener.onPathsChanged(snapshot)
        );
    }
}
