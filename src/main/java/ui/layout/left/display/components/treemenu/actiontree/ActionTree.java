package ui.layout.left.display.components.treemenu.actiontree;

import common.constant.AppConstants;
import sdk.constants.methods.CommonMethods;
import sdk.constants.methods.DeviceMethods;
import sdk.constants.methods.ImageMethods;
import ui.base.treelist.AbstractTree;
import ui.base.treelist.AbstractTreeNode;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.browser.*;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.codeEditor.ScriptEditorDialog;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.commons.VariableTriggerDialog;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.windows.WindowsUIDialog;
import ui.model.MainModel;

import java.awt.dnd.DragSourceAdapter;
import java.awt.dnd.DragSourceDragEvent;
import java.awt.dnd.DragSourceEvent;

public class ActionTree extends AbstractTree {

    public ActionTree(MainModel mainModel) {
        createDragGestureListener(new NodeTransferable(), new DragSourceAdapter() {
            // 可以在这里管理拖动过程中的变化，比如更改icon等
            @Override
            public void dragEnter(DragSourceDragEvent dragSourceDragEvent) {
                // 当拖动进入某个区域时阻止
            }

            @Override
            public void dragExit(DragSourceEvent dse) {
                // 当拖动离开某个区域时发生
            }

            @Override
            public void dragOver(DragSourceDragEvent dragSourceDragEvent) {
                // 可以在此动态更改拖动时的图像或者其他效果
            }
        });
        setRootName(AppConstants.ACTION_MANAGER);
        setEnableRootMenu(false);
        setRootVisible(false);

        ActionDialog defaultDialog = new ActionDialog();

        ActionNode powerNode = new ActionNode("程控电源操作");
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.outputOn, defaultDialog));
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.outputOff, defaultDialog));
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.setVoltage, defaultDialog));
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.fetchVoltage, defaultDialog));
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.fetchCurrent, defaultDialog));
        powerNode.addNode(new ActionItem.PowerAction(DeviceMethods.staticCurrentAcquire, defaultDialog));

        ActionNode pulseNode = new ActionNode("脉冲电源操作");
        pulseNode.addNode(new ActionItem(DeviceMethods.loadWavePulse, defaultDialog));
        pulseNode.addNode(new ActionItem(DeviceMethods.writeCustomizeWavePulse, defaultDialog));
        pulseNode.addNode(new ActionItem(DeviceMethods.writeStartWavePulse, defaultDialog));
        pulseNode.addNode(new ActionItem(DeviceMethods.writeTimeLevelResetPulse, defaultDialog));
        pulseNode.addNode(new ActionItem(DeviceMethods.writeVoltageLevelResetPulse, defaultDialog));
        pulseNode.addNode(new ActionItem(DeviceMethods.writePowerTemporarilyInterruptPulse, defaultDialog));

        ActionNode robotNode = new ActionNode("机械臂操作");
        robotNode.addNode(new ActionItem(DeviceMethods.returnHome, defaultDialog));
        robotNode.addNode(new ActionItem(DeviceMethods.enableRobot, defaultDialog));
        robotNode.addNode(new ActionItem(DeviceMethods.swipeByName, defaultDialog));
        robotNode.addNode(new ActionItem(DeviceMethods.longTouch, defaultDialog));

        ActionNode androidNode = new ActionNode("Android操作");
        androidNode.addNode(new ActionItem(DeviceMethods.click, defaultDialog));
        androidNode.addNode(new ActionItem(DeviceMethods.swipe, defaultDialog));


        ActionNode imageRecognizeNode = new ActionNode("图像操作");
        imageRecognizeNode.addNode(new ActionItem(ImageMethods.mustAppearMethod, defaultDialog));
        imageRecognizeNode.addNode(new ActionItem(ImageMethods.mustDisappearMethod, defaultDialog));
        imageRecognizeNode.addNode(new ActionItem(ImageMethods.waitAppearMethod, defaultDialog));
        imageRecognizeNode.addNode(new ActionItem(ImageMethods.waitDisappearMethod, defaultDialog));

        ActionNode pythonScriptNode = new ActionNode("Python操作");
        pythonScriptNode.addNode(new ActionItem(CommonMethods.pythonEdit, ScriptEditorDialog.getInstance(mainModel)));

        ActionNode browserNode = new ActionNode("浏览器操作");
        browserNode.addNode(new ActionItem(CommonMethods.browserOpenUrl, BrowserOpenUrlDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserClick, BrowserClickDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserInputText, BrowserInputTextDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserCheckText, BrowserCheckTextDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserSwitchToTab, BrowserSwitchTabDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserCloseTab, BrowserCloseTabDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserPageUp, BrowserPageUpDialog.getInstance(mainModel)));
        browserNode.addNode(new ActionItem(CommonMethods.browserPageDown, BrowserPageDownDialog.getInstance(mainModel)));

        ActionNode winUINode = new ActionNode("Windows操作");
        winUINode.addNode(new ActionItem(CommonMethods.pcBringToFront, WindowsUIDialog.getInstance(mainModel)));
        winUINode.addNode(new ActionItem(CommonMethods.pcClick, WindowsUIDialog.getInstance(mainModel)));

        ActionNode frequencyUsedToolsNode = new ActionNode("常用功能");
        frequencyUsedToolsNode.add(new ActionItem(CommonMethods.triggerVariableChanged, VariableTriggerDialog.getInstance(mainModel)));
        frequencyUsedToolsNode.add(new ActionItem(CommonMethods.breakLoop, mainModel));
//        getRoot().addNode(imageRecognizeNode);
//        getRoot().addNode(powerNode);
//        getRoot().addNode(pulseNode);
//        getRoot().addNode(robotNode);
//        getRoot().addNode(androidNode);
//        getRoot().addNode(pythonScriptNode);
        ActionNode timeMeasurementNode = new ActionNode("时间测量");
        timeMeasurementNode.add(new ActionItem(CommonMethods.beginTimeMeasure, mainModel));
        timeMeasurementNode.add(new ActionItem(CommonMethods.endTimeMeasure, mainModel));

//        ActionNode soundMonitorNode = new ActionNode("声音监控");
//        soundMonitorNode.add(new ActionItem(DeviceMethods.beginSoundMonitor, DeviceType.DEVICE_SOUND_CARD, mainModel));
//        soundMonitorNode.add(new ActionItem(DeviceMethods.endSoundMonitor, SoundMonitorDialog.getInstance(mainModel, 1)));

        getRoot().addNode(browserNode);
        getRoot().addNode(winUINode);
        getRoot().addNode(frequencyUsedToolsNode);
        getRoot().addNode(timeMeasurementNode);
//        getRoot().addNode(soundMonitorNode);
        expandAll(true);
        updateTreeUI();
    }

    @Override
    protected void itemClick(AbstractTreeNode selectedNode) {
        if (selectedNode instanceof ActionItem) {
            ActionItem actionItem = (ActionItem) selectedNode;
            actionItem.show();
        }
    }

    @Override
    protected void treeSelectionChanged(AbstractTreeNode selectedNode) {

    }
}

