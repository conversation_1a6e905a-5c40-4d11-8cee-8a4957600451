package ui.layout.left.display.components.tappane.case_mgmt.scriptcase.testcasepackage;

import javax.swing.*;

/**
 * @projectName: swingtest
 * @package: packagefly
 * @className: proger
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/3/4 17:51
 * @version: 1.0
 */
public class ProgressDialog extends JDialog {
    private final JProgressBar progressBar;

    public ProgressDialog(JFrame parent) {
        super(parent, "用例加载中。。。", true);
        progressBar = new JProgressBar(0, 100);
        progressBar.setValue(0);
        progressBar.setStringPainted(true);
        this.add(progressBar);
        this.setSize(300, 75);
        this.setLocationRelativeTo(parent);
    }

    public void updateProgress(int value) {
        progressBar.setValue(value);
    }
}