package ui.layout.left.display.components.tappane.case_mgmt.scriptcase;

import lombok.Getter;
import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * 自动化脚本顶层面板
 */
public class TestScriptCaseTabPaneView extends JPanel implements BaseView {
    @Getter
    private final TestScriptCaseUpperPanel testScriptCaseUpperPanel; //自动化脚本顶部面板
    /**
     * -- GETTER --
     *  获取TestScriptCaseDisplayTable实例
     *
     * @return TestScriptCaseDisplayTable实例
     */
    @Getter
    private final TestScriptCaseDisplayTable testScriptCaseDisplayTable; //自动化脚本展示表格

    private final TestScriptCaseBottomPanel testScriptCaseBottomPanel; //自动化脚本顶部面板

    @Getter
    private final JPanel messagePanel;
    private JPanel centerPanel;


    public TestScriptCaseTabPaneView(MainModel mainModel) {
        testScriptCaseBottomPanel = new TestScriptCaseBottomPanel(mainModel);
        testScriptCaseDisplayTable = new TestScriptCaseDisplayTable(mainModel);
        testScriptCaseUpperPanel = new TestScriptCaseUpperPanel(testScriptCaseDisplayTable, mainModel);
        JLabel messageLabel = new JLabel("请新建一个脚本", SwingConstants.CENTER);
        messageLabel.setFont(new Font(null, Font.BOLD, 35));
        messageLabel.setForeground(Color.GRAY);
        messagePanel = new JPanel(new GridBagLayout());
        messagePanel.add(messageLabel);
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        add(testScriptCaseUpperPanel, BorderLayout.NORTH);
        centerPanel = new JPanel(new CardLayout());
        centerPanel.add(messagePanel, "messagePanel");
        centerPanel.add(testScriptCaseDisplayTable.addScrollRowHeader(), "testScriptCaseDisplayTable");
        add(centerPanel, BorderLayout.CENTER);
        add(testScriptCaseBottomPanel, BorderLayout.SOUTH);
        updatePanelVisibility(testScriptCaseDisplayTable.isScriptEmpty());
    }

    public void updatePanelVisibility(boolean scriptEmpty) {
        CardLayout cl = (CardLayout) (centerPanel.getLayout());
        if (scriptEmpty) {
            cl.show(centerPanel, "messagePanel");
        } else {
            cl.show(centerPanel, "testScriptCaseDisplayTable");
        }
    }

    public void switchToTestScriptCaseDisplayTable() {
        CardLayout cl = (CardLayout) (centerPanel.getLayout());
        cl.show(centerPanel, "testScriptCaseDisplayTable");
        revalidate();
        repaint();
    }

}
