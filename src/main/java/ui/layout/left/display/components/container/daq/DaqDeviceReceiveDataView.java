package ui.layout.left.display.components.container.daq;

import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceReceiveView;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

public class DaqDeviceReceiveDataView extends DeviceReceiveView implements BaseView {
    //    private final JToggleButton enableMonitorButton;
    private final JTextField voltageTextField;
    private final JTextField currentTextField;


    public DaqDeviceReceiveDataView(DeviceContainer deviceContainer, MainModel mainModel) {
        super(deviceContainer, mainModel);
//        enableMonitorButton = getEnableMonitorButton();
        voltageTextField = new JTextField();
        currentTextField = new JTextField();
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        voltageTextField.setEditable(false);
        currentTextField.setEditable(false);
        setLayout(new GridBagLayout());
        SwingUtil.addRowLayoutUnchanged(this, "电压(V):", voltageTextField, 0);
        SwingUtil.addRowLayoutUnchanged(this, "电流(A):", currentTextField, 1);
    }
}
