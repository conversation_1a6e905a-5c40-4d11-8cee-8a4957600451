package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.lin.LinContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * LIN设备控制界面
 */
public class LinDeviceTabPaneView extends DeviceTabPaneView {

    public LinDeviceTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        LinContainer linContainer = new LinContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, linContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
