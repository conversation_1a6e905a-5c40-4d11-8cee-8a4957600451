package ui.layout.left.display.components.treemenu.actiontree;

import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import sdk.base.operation.Operation;

import javax.swing.tree.DefaultMutableTreeNode;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.IOException;

//封装数据：它必须能够封装您希望从拖动的源传到放置目标的数据。在你的场景中，就是来自DefaultMutableTreeNode的数据。
//指定数据格式：它需要定义一种或多种可以传输数据的格式，这些格式是通过 DataFlavor 对象来指定的。DataFlavor 用于指定传输的数据类型，这样放置操作的目标知道如何处理传过来的数据。
//提供数据：Transferable 必须实现一个方法 getTransferData(DataFlavor flavor)，该方法能够在请求时返回表示数据的对象。
@Setter
public class NodeTransferable implements Transferable {

    private DefaultMutableTreeNode node;
    @Getter
    protected static DataFlavor nodeFlavor;
    protected final static DataFlavor[] flavors = new DataFlavor[1];

    static {
        try {
            nodeFlavor = new DataFlavor(DataFlavor.javaJVMLocalObjectMimeType +
                    ";class=\"" +
                    javax.swing.tree.DefaultMutableTreeNode.class.getName() +
                    "\"");
            flavors[0] = nodeFlavor;
        } catch (ClassNotFoundException e) {
            System.out.println("ClassNotFound: " + e.getMessage());
        }
    }

    public NodeTransferable() {
    }

    // 返回支持的所有 DataFlavor
    public DataFlavor[] getTransferDataFlavors() {
        return flavors;
    }

    // 检查是否支持特定的 DataFlavor
    public boolean isDataFlavorSupported(DataFlavor flavor) {
        return nodeFlavor.equals(flavor);
    }

    // 当请求特定 flavor 的数据时，返回封装的数据
    @NotNull
    public Object getTransferData(DataFlavor flavor)
            throws UnsupportedFlavorException, IOException {
        if (!isDataFlavorSupported(flavor)) {
            throw new UnsupportedFlavorException(flavor);
        }
        ActionItem actionItem = (ActionItem) node;
        Operation operation = new Operation();
        operation.setOperationTarget(actionItem.getOperationTarget());
        operation.setOperationMethod(actionItem.getOperationMethod());
        operation.setOperationObject(new Object());
        return operation;
    }
}