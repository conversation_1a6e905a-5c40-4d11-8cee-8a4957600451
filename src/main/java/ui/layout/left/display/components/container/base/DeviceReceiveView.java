package ui.layout.left.display.components.container.base;

import lombok.Getter;
import ui.model.MainModel;

import javax.swing.*;

@Getter
public class DeviceReceiveView extends JPanel {

    private final DeviceContainer deviceContainer;

    private final MainModel mainModel;

    private final JToggleButton enableMonitorButton;

    public DeviceReceiveView(DeviceContainer deviceContainer, MainModel mainModel) {
        this.deviceContainer = deviceContainer;
        this.mainModel = mainModel;
        enableMonitorButton = new JToggleButton();
        enableMonitorButton.setText("启用监控按钮");
        enableMonitorButton.setSelected(false);
        enableMonitorButton.setEnabled(false);
    }
}
