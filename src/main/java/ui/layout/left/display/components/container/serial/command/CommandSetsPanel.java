package ui.layout.left.display.components.container.serial.command;

import com.alibaba.fastjson2.JSON;
import common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.MessageText;
import sdk.domain.PackageMessages;
import sdk.entity.SerialDevice;
import ui.config.json.devices.serial.SerialConfig;
import ui.layout.left.display.components.container.serial.SerialContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellEditor;
import javax.swing.table.TableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;

/**
 * 创建、配置JFrame、JTable、JScrollPane等组件。
 */
@Slf4j
public class CommandSetsPanel extends JPanel implements ActionListener {
    private static final int COLUMN_HEX_INDEX = 0;//“HEX”列坐标
    private static final int COLUMN_CMD_INDEX = 1;//"命令字符串"列坐标
    private static final int COLUMN_MEANING_INDEX = 2;//“命令含义”列坐标
    private static final int COLUMN_RETURN_STRING_INDEX = 3;//“返回字符串”列坐标
    private static final int COLUMN_TIME_OUT_INDEX = 4;//“超时时间”列坐标
    private static final int COLUMN_RETRY_TIMES_INDEX = 5;//“重试次数”列坐标
    private static final int COLUMN_SEND_INDEX = 6;//“仅发送”列坐标
    private static final int COLUMN_ADD_TO_SCRIPT_INDEX = 7;//“添加到脚本”列坐标
    private static final int COLUMN_SEND_ORDER_INDEX = 8;//“发送顺序”列坐标
    private static final int COLUMN_COUNT = 9;//总列数
    private static final String COLUMN_HEX = "HEX";
    private static final String COLUMN_CMD = "命令字符串";
    private static final String COLUMN_MEANING = "命令释义";
    private static final String COLUMN_RETURN_STRING = "返回字符串";
    private static final String COLUMN_TIME_OUT = "超时时间";
    private static final String COLUMN_RETRY_TIMES = "重试次数";
    private static final String COLUMN_SEND_ORDER = "发送顺序";
    private static final String COLUMN_SEND = "发送";
    private static final String COLUMN_ADD_TO_SCRIPT = "添加到脚本";
    private static final int HEADER_FONT_SIZE = 20;//表头字体大小
    private static final int CELL_FONT_SIZE = 14;//表格字体大小
    private static final int CELL_ROW_HEIGHT = 30;//表格字体大小
    private static final int COLUMN_CENTER_WIDTH = 300;//“命令字符串”列的宽度
    private static final int VIEWPORT_WIDTH = 800;//可视表格的宽度
    private static final int VIEWPORT_HEIGHT = 800;//可视表格的高度
    private static final int PANEL_WIDTH = 800;//主面板的宽度、文件组件面板宽度
    private static final int PANEL_HEIGHT = 900;//主面板的高度
    private static final int NORTH_PANEL_HEIGHT = 50;//文件组件面板高度
    private static final int TEXT_FILED_WIDTH = 20;//文本框宽度
    private static final String OPEN_BUTTON_NAME = "打开";
    private static final String SAVE_BUTTON_NAME = "保存为ini";
    private static final String SAVE_AS_JSON_BUTTON_NAME = "保存为json";
    private static final String CLEAR_BUTTON_NAME = "清空表格";
    private static final String LABEL = "文件：";
    private static final String FILE_CHOOSER_DIALOG_TITLE = "请选择文件";//文件选择对话框标题
    private final JTable table;
    private final JTextField filePathTextField;
    private List<CommandString> commandStringList;
    private final MainModel mainModel;
    private final SerialConfig serialConfig;
    private final SerialDevice serialDevice;
    private final JButton packageSendButton;
    private final JButton packageAddToScriptButton;
    private final JSpinner packageSendIntervalSpinner;
    private final static int DEFAULT_RETRY_TIMES = 10;
    private final static float DEFAULT_TIME_OUT = 1;


    public CommandSetsPanel(SerialContainer serialContainer, MainModel mainModel, SerialConfig serialConfig) {
        serialDevice = (SerialDevice) serialContainer.getDevice();
        this.mainModel = mainModel;
        this.serialConfig = serialConfig;
        //主面板
        setSize(new Dimension(PANEL_WIDTH, PANEL_HEIGHT));
        setLayout(new BorderLayout());
        //组件面板
        JPanel panel = new JPanel(new BorderLayout());
        JPanel configPanel = new JPanel(new FlowLayout());
        JPanel packagePanel = new JPanel(new FlowLayout());
        packageSendButton = new JButton("发送");
        packageAddToScriptButton = new JButton(">>[批量发送]添加到脚本");
        packageSendIntervalSpinner = new JSpinner(new SpinnerNumberModel(50, 1, 100000, 10));
        packagePanel.add(new JLabel("组包发送:"));
        packagePanel.add(packageSendButton);
        packagePanel.add(packageAddToScriptButton);
        packagePanel.add(new JLabel("发送间隔(ms):"));
        packagePanel.add(packageSendIntervalSpinner);
        JPanel northPanel = new JPanel();
        northPanel.setLayout(new BoxLayout(northPanel, BoxLayout.Y_AXIS));
        configPanel.setPreferredSize(new Dimension(PANEL_WIDTH, NORTH_PANEL_HEIGHT));
        JLabel label = new JLabel(LABEL);
        //文件路径文本区域
        filePathTextField = new JTextField();
        filePathTextField.setColumns(TEXT_FILED_WIDTH);
        //文件打开与保存按钮设置
        JButton openButton = new JButton(OPEN_BUTTON_NAME);
        JButton saveButton = new JButton(SAVE_BUTTON_NAME);
        JButton saveAsJsonButton = new JButton(SAVE_AS_JSON_BUTTON_NAME);
        JButton clearButton = new JButton(CLEAR_BUTTON_NAME);
        saveButton.setFocusPainted(false);
        openButton.setFocusPainted(false);
        saveAsJsonButton.setFocusPainted(false);
        clearButton.setFocusPainted(false);
        openButton.setActionCommand("open");
        openButton.addActionListener(this);
        saveButton.setActionCommand("save");
        saveButton.addActionListener(this);
        saveAsJsonButton.setActionCommand("saveAsJson");
        saveAsJsonButton.addActionListener(this);
        clearButton.setActionCommand("clear");
        clearButton.addActionListener(this);
        //创建table
        table = new JTable();
        //添加监听
        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getButton() == MouseEvent.BUTTON1) {
                    mouseClick(e);
                } else if (e.getButton() == MouseEvent.BUTTON3) {
                    int row = ((JTable) e.getSource()).rowAtPoint(e.getPoint());
                    table.setRowSelectionInterval(row, row);
                    table.editCellAt(row, COLUMN_MEANING_INDEX);
                }

            }

        });
        //设置table
        updateTable(null, table);
        //把表格放到滚动面板中
        JScrollPane scrollPane = new JScrollPane(table);
        //添加滚动面板到内容面板
        panel.add(scrollPane);
        configPanel.add(label);
        configPanel.add(filePathTextField);
        configPanel.add(openButton);
        configPanel.add(saveButton);
        configPanel.add(saveAsJsonButton);
        configPanel.add(clearButton);

        northPanel.add(configPanel);
        northPanel.add(packagePanel);
        //添加组件面板到主面板
        add(panel, BorderLayout.CENTER);
        add(northPanel, BorderLayout.NORTH);
//        restoreView();
        filePathTextField.setText(serialConfig.getCommandsConfigPath());
        if (!StringUtils.isBlank(filePathTextField.getText())) {
            updateTable(filePathTextField.getText(), table);
        }
        packageSendButton.addActionListener(e -> packageSendButtonActionPerformed());
        packageAddToScriptButton.addActionListener(e -> packageSendAddToScriptButtonActionPerformed());
        addOrder();
    }

    public void addOrder() {
        TableModel model = table.getModel();
        int rowCount = model.getRowCount();
        int sendOrder = 1;

        for (int i = 0; i < rowCount; i++) {
            String cmdValue = (String) model.getValueAt(i, COLUMN_CMD_INDEX);
            if (cmdValue != null && !cmdValue.isEmpty()) {
                model.setValueAt(sendOrder, i, COLUMN_SEND_ORDER_INDEX);
                sendOrder++;
            }
        }
    }

    /**
     * table数据填充
     *
     * @param filePath 文件路径
     * @param table    表格
     */
    public void updateTable(String filePath, JTable table) {
        //获取table model
        DefaultTableModel tableModel = new DefaultTableModel();
        tableModel.addColumn(COLUMN_HEX, new Vector<String>());
        tableModel.addColumn(COLUMN_CMD, new Vector<String>());
        tableModel.addColumn(COLUMN_MEANING, new Vector<String>());
        tableModel.addColumn(COLUMN_RETURN_STRING, new Vector<String>());
        tableModel.addColumn(COLUMN_TIME_OUT, new Vector<String>());
        tableModel.addColumn(COLUMN_RETRY_TIMES, new Vector<String>()); // addColumn(Object columnName, Vector columnData)
        tableModel.addColumn(COLUMN_SEND, new Vector<String>());
        tableModel.addColumn(COLUMN_ADD_TO_SCRIPT, new Vector<String>());
        tableModel.addColumn(COLUMN_SEND_ORDER, new Vector<String>());
        if (filePath == null || StringUtils.isBlank(filePath)) {
            for (int i = 0; i < 200; i++) {
                String[] data = new String[200];
                data[COLUMN_HEX_INDEX] = "H";
                data[COLUMN_CMD_INDEX] = "";
                data[COLUMN_MEANING_INDEX] = "";
                data[COLUMN_SEND_INDEX] = "";
                data[COLUMN_ADD_TO_SCRIPT_INDEX] = "";
                data[COLUMN_RETURN_STRING_INDEX] = "";
                data[COLUMN_TIME_OUT_INDEX] = null;
                data[COLUMN_RETRY_TIMES_INDEX] = null;
                data[COLUMN_SEND_ORDER_INDEX] = null;
                tableModel.addRow(data);
                table.setModel(tableModel);
            }
            setTable(table);
        } else {
            if (filePath.contains(".ini")) {  // 加载ini
                //导入文件数据
                commandStringList = CommandStringList.getCommandStringList(filePath);
                //填充数据
                for (CommandString commandString : commandStringList) {
                    String[] data = new String[COLUMN_COUNT];
                    data[COLUMN_HEX_INDEX] = (String) commandString.getCommandType().values().toArray()[0];
                    data[COLUMN_CMD_INDEX] = commandString.getCommand();
                    data[COLUMN_MEANING_INDEX] = commandString.getCommandInterpretation();
                    data[COLUMN_RETURN_STRING_INDEX] = "";
                    data[COLUMN_TIME_OUT_INDEX] = null;
                    data[COLUMN_RETRY_TIMES_INDEX] = null;
                    //添加数据到表格
                    tableModel.addRow(data);
                    table.setModel(tableModel);
                }
            } else {  // 加载 json
                String jsonString = null;
                try {
                    jsonString = new String(Files.readAllBytes(Paths.get(filePath)));
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
                List<Command> commands = Collections.emptyList();
                try {
                    commands = JSON.parseArray(jsonString, Command.class);
                } catch (Exception e) {
                    log.error("JSON parse error", e);
                }
                if (commands != null && !commands.isEmpty()) {
                    commands.forEach(command -> {
                        Object[] data = new Object[COLUMN_COUNT];
                        data[COLUMN_HEX_INDEX] = command.getCommandType();
                        data[COLUMN_CMD_INDEX] = command.getCommand();
                        data[COLUMN_MEANING_INDEX] = command.getCommandInterpretation();
                        data[COLUMN_RETURN_STRING_INDEX] = command.getReturnString();
                        data[COLUMN_TIME_OUT_INDEX] = command.getTimeout();
                        data[COLUMN_RETRY_TIMES_INDEX] = command.getRetryTimes();
                        tableModel.addRow(data);
                        table.setModel(tableModel);
                    });
                }
            }
            setTable(table);
            serialConfig.setCommandsConfigPath(filePath);
            serialDevice.updateConfig(serialConfig);
        }
    }


    /**
     * 文件打开与保存按钮执行功能设置
     *
     * @param e 按钮监听事件
     */
    @Override
    public void actionPerformed(ActionEvent e) {
        //“打开”按钮功能
        if (e.getActionCommand().equals("open")) {
            JFileChooser fileChooser = new JFileChooser();
            //FileNameExtensionFilter filter = new FileNameExtensionFilter("ini", "ini");
            //fileChooser.setDialogTitle(FILE_CHOOSER_DIALOG_TITLE);
            //fileChooser.setFileFilter(filter);
            FileNameExtensionFilter filter = new FileNameExtensionFilter("ini,json", "ini", "json");
            fileChooser.setDialogTitle(FILE_CHOOSER_DIALOG_TITLE);
            fileChooser.setFileFilter(filter);
            //设置默认显示的文件夹为当前文件夹
            fileChooser.setCurrentDirectory(new File("."));
            int result = fileChooser.showOpenDialog(null);
            if (result != JFileChooser.APPROVE_OPTION) {
                return;
            }
            filePathTextField.setText(fileChooser.getSelectedFile().getAbsolutePath());
            updateTable(filePathTextField.getText(), table);
        } else if (e.getActionCommand().equals("save")) {
            JFileChooser chooser = new JFileChooser();
            //后缀名过滤器
            FileNameExtensionFilter filter = new FileNameExtensionFilter("(*.ini)", "ini");
            chooser.setFileFilter(filter);
            //下面的方法将阻塞，直到(用户按下保存按钮且“文件名”文本框不为空)或(用户按下取消按钮)
            int option = chooser.showSaveDialog(null);
            if (option == JFileChooser.APPROVE_OPTION) {
                File file = chooser.getSelectedFile();

                String fileName = chooser.getName(file);  //从文件名输入框中获取文件名

                //用户填写的文件名不带制定的后缀名，则自动为它添上后缀
                if (!fileName.contains(".ini")) {
                    file = new File(chooser.getCurrentDirectory(), fileName + ".ini");
                    chooser.setSelectedFile(file);
                }
                //commandStringList = CommandStringList.getCommandStringList(filePathTextField.getText());
                commandStringList = CommandStringList.getCommandStringList(chooser.getSelectedFile().getAbsolutePath());
                List<String> commandStrings = new ArrayList<>();
                TableModel model = table.getModel();
                for (int i = 0; i < model.getRowCount(); i++) {
                    //空值判断
                    if (model.getValueAt(i, COLUMN_CMD_INDEX) == null ||
                            ((String)model.getValueAt(i, COLUMN_CMD_INDEX)).isEmpty()) {
                        continue; // 跳过空行
                    }
                    // 直接从表格获取数据，不依赖commandStringList
                    String hex = (String) model.getValueAt(i, COLUMN_HEX_INDEX);
                    String cmd = (String) model.getValueAt(i, COLUMN_CMD_INDEX);
                    String meaning = (String) model.getValueAt(i, COLUMN_MEANING_INDEX);
                    // 构造数据时使用实际值代替commandStringList中的值
                    String string = String.format("N%d", i + 101)
                            + "=0," + meaning + ",1000\n" // 使用固定值sequence为0 和 delayTime为1000
                            + String.format("N%d", i + 1) + "="
                            + hex + hex + "," + cmd + "\n\n";
                    commandStrings.add(string);
                }
                CommandStringList.saveCommandStringList(commandStrings, chooser.getSelectedFile().getAbsolutePath());
            }
        } else if (e.getActionCommand().equals("saveAsJson")) {
            JFileChooser chooser = new JFileChooser();
            FileNameExtensionFilter filter = new FileNameExtensionFilter("(*.json)", "json");
            chooser.setFileFilter(filter);
            int option = chooser.showSaveDialog(null);
            if (option == JFileChooser.APPROVE_OPTION) {
                File file = chooser.getSelectedFile();
                String fileName = chooser.getName(file);

                //用户填写的文件名不带制定的后缀名，则自动为它添上后缀
                if (!fileName.contains(".json")) {
                    file = new File(chooser.getCurrentDirectory(), fileName + ".json");
                    chooser.setSelectedFile(file);
                }
                List<Command> commands = new ArrayList<>();
                TableModel model = table.getModel();
                for (int i = 0; i < model.getRowCount(); i++) {
                    float timeout = 0.0f;
                    if (model.getValueAt(i, COLUMN_TIME_OUT_INDEX) != null) {
                        Object o = model.getValueAt(i, COLUMN_TIME_OUT_INDEX);
                        if (o instanceof Double) {
                            timeout = ((Double) o).floatValue();
                        } else if (o instanceof Float) {
                            timeout = (Float) o; // 直接使用Float值
                        } else if (o instanceof Number) {
                            timeout = ((Number) o).floatValue(); // 通用数值转换
                        } else {
                            log.warn("超时值类型无效：{}", o.getClass().getName());
                            timeout = DEFAULT_TIME_OUT; // 使用默认值
                        }
                    }

                    int retryTimes = 0;
                    if (model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX) != null) {
                        Object o = model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX);
                        retryTimes = (int) o;
                    }
                    Command command = Command.builder()
                            .sequence(0)
                            .commandInterpretation((String) model.getValueAt(i, COLUMN_MEANING_INDEX))
                            .delayTime("1000")
                            .commandType((String) model.getValueAt(i, COLUMN_HEX_INDEX))
                            .command((String) model.getValueAt(i, COLUMN_CMD_INDEX))
                            .returnString((String) model.getValueAt(i, COLUMN_RETURN_STRING_INDEX))
                            .timeout(timeout)
                            .retryTimes(retryTimes)
                            .build();
                    commands.add(command);
                }
                CommandStringList.saveCommandStringListAsJson(commands, chooser.getSelectedFile().getAbsolutePath());
            }

        } else if (e.getActionCommand().equals("clear")) {
            DefaultTableModel tableModel = new DefaultTableModel();
            tableModel.addColumn(COLUMN_HEX, new Vector<String>());
            tableModel.addColumn(COLUMN_CMD, new Vector<String>());
            tableModel.addColumn(COLUMN_MEANING, new Vector<String>());
            tableModel.addColumn(COLUMN_RETURN_STRING, new Vector<String>());
            tableModel.addColumn(COLUMN_TIME_OUT, new Vector<String>());
            tableModel.addColumn(COLUMN_RETRY_TIMES, new Vector<String>()); // addColumn(Object columnName, Vector columnData)
            tableModel.addColumn(COLUMN_SEND, new Vector<String>());
            tableModel.addColumn(COLUMN_ADD_TO_SCRIPT, new Vector<String>());
            tableModel.addColumn(COLUMN_SEND_ORDER, new Vector<String>());
            for (int i = 0; i < 99; i++) {
                String[] data = new String[200];
                data[COLUMN_HEX_INDEX] = "H";
                data[COLUMN_CMD_INDEX] = "";
                data[COLUMN_MEANING_INDEX] = "";
                data[COLUMN_SEND_INDEX] = "";
                data[COLUMN_ADD_TO_SCRIPT_INDEX] = "";
                data[COLUMN_RETURN_STRING_INDEX] = "";
                data[COLUMN_TIME_OUT_INDEX] = null;
                data[COLUMN_RETRY_TIMES_INDEX] = null;
                data[COLUMN_SEND_ORDER_INDEX] = null;
                tableModel.addRow(data);
                table.setModel(tableModel);
            }
            setTable(table);
            filePathTextField.setText("");
        }
    }


    /**
     * 命令输出
     *
     * @param e 鼠标事件
     */
    private void mouseClick(MouseEvent e) {
        int row = ((JTable) e.getSource()).rowAtPoint(e.getPoint());
        int col = ((JTable) e.getSource()).columnAtPoint(e.getPoint());

        if (row >= 0 && (col == COLUMN_SEND_INDEX || col == COLUMN_ADD_TO_SCRIPT_INDEX)) {
            String cmdValue = (String) table.getValueAt(row, COLUMN_CMD_INDEX);

            if (!cmdValue.isEmpty()) {
                MessageText messageText = new MessageText();
                messageText.setHex(table.getValueAt(row, COLUMN_HEX_INDEX).equals("H"));
                messageText.setSendText(cmdValue + "\n");
                messageText.setFriendlyString(table.getModel().getValueAt(row, COLUMN_MEANING_INDEX) + "\n");
                String matchString = (String) table.getModel().getValueAt(row, COLUMN_RETURN_STRING_INDEX);

                if (col == COLUMN_SEND_INDEX) {
                    mainModel.getDeviceSendDataModel().sendMessage(messageText);
                } else if (col == COLUMN_ADD_TO_SCRIPT_INDEX) {
                    Operation operation = Operation.buildOperation(serialDevice);

                    // 发送并匹配
                    if (matchString != null && !matchString.trim().isEmpty()) {
                        operation.setOperationMethod(DeviceMethods.sendAndMatch);
                        messageText.setMatchText(matchString);
                        try {
                            float timeOut = DEFAULT_TIME_OUT;
                            int retryTimes = DEFAULT_RETRY_TIMES;
                            if (table.getModel().getValueAt(row, COLUMN_TIME_OUT_INDEX) != null) {
                                timeOut = Float.parseFloat(Double.toString(((double) table.getModel().getValueAt(row, COLUMN_TIME_OUT_INDEX))));
                            }
                            if (table.getModel().getValueAt(row, COLUMN_RETRY_TIMES_INDEX) != null) {
                                retryTimes = (int) table.getModel().getValueAt(row, COLUMN_RETRY_TIMES_INDEX);
                            }
                            messageText.setReceiveTimeout(timeOut);
                            messageText.setTimeoutRetry(retryTimes);
                        } catch (NumberFormatException exception) {
                            SwingUtil.showWarningDialog(this, "请检查输入的数据是否正常");
                            throw new RuntimeException(exception);
                        } catch (NullPointerException exception) {
                            SwingUtil.showWarningDialog(this, "请输入正确的数据");
                            throw new RuntimeException(exception);
                        }
                        // 仅发送
                    } else {
                        operation.setOperationMethod(DeviceMethods.sendToDevice);
                    }
                    operation.setOperationObject(messageText);
                    mainModel.getOperationModel().updateOperation(operation);
                }
            } else {
                SwingUtil.showWarningDialog(this, "字符串不能为空");
            }
        }
    }

    private void packageSendButtonActionPerformed() {
        if (packageSendButton.getText().equals("停止")) {

        } else {
            if (validateAndPrepareSendOrder()) return;
            List<MessageText> messageTextList = prepareMessageTextList();
            PackageMessages packageMessages = new PackageMessages((Integer) packageSendIntervalSpinner.getValue(), messageTextList);
            Operation operation = Operation.buildOperation(serialDevice);
            operation.setOperationMethod(DeviceMethods.packageSend);
            operation.setOperationObject(packageMessages);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            packageSendButton.setText("停止");
            if (operationResult.isOk()) {
                packageSendButton.setText("发送");
            }
        }
    }

    private void packageSendAddToScriptButtonActionPerformed() {
        if (validateAndPrepareSendOrder()) return;

        List<MessageText> messageTextList = prepareMessageTextList();
        PackageMessages packageMessages = new PackageMessages((Integer) packageSendIntervalSpinner.getValue(), messageTextList);
        Operation operation = Operation.buildOperation(serialDevice);
        operation.setOperationMethod(DeviceMethods.packageSend);
        operation.setOperationObject(packageMessages);
        mainModel.getOperationModel().updateOperation(operation);
    }

    private boolean validateAndPrepareSendOrder() {
        HashSet<Integer> sendOrderSet = new HashSet<>();
        TableModel model = table.getModel();
        int rowCount = model.getRowCount();
        List<Integer> sendOrderList = new ArrayList<>();

        for (int i = 0; i < rowCount; i++) {
            Object sendOrderObj = model.getValueAt(i, COLUMN_SEND_ORDER_INDEX);
            if (sendOrderObj != null) {
                int sendOrder = (int) sendOrderObj;
                if (sendOrderSet.contains(sendOrder)) {
                    SwingUtil.showWarningDialog(this, "发送顺序列数据有重复，请检查并修改。");
                    return true;
                }
                sendOrderSet.add(sendOrder);
                sendOrderList.add(i);
            }
        }

        sendOrderList.sort(Comparator.comparingInt(i -> (int) model.getValueAt(i, COLUMN_SEND_ORDER_INDEX)));

        for (int i : sendOrderList) {
            if (model.getValueAt(i, COLUMN_CMD_INDEX) == null || ((String) model.getValueAt(i, COLUMN_CMD_INDEX)).isEmpty()) {
                SwingUtil.showWarningDialog(this, "命令字符串不能为空，请检查并修改。");
                return true;
            }
            if (model.getValueAt(i, COLUMN_TIME_OUT_INDEX) != null) {
                try {
                    Double.parseDouble(model.getValueAt(i, COLUMN_TIME_OUT_INDEX).toString());
                } catch (NumberFormatException e) {
                    SwingUtil.showWarningDialog(this, "超时时间必须是数字，请检查并修改。");
                    return true;
                }
            }
            if (model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX) != null) {
                try {
                    Integer.parseInt(model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX).toString());
                } catch (NumberFormatException e) {
                    SwingUtil.showWarningDialog(this, "重试次数必须是整数，请检查并修改。");
                    return true;
                }
            }
        }
        return false;
    }

    private List<MessageText> prepareMessageTextList() {
        List<MessageText> messageTextList = new ArrayList<>();
        TableModel model = table.getModel();
        int rowCount = model.getRowCount();
        List<Integer> sendOrderList = new ArrayList<>();

        for (int i = 0; i < rowCount; i++) {
            Object sendOrderObj = model.getValueAt(i, COLUMN_SEND_ORDER_INDEX);
            if (sendOrderObj != null) {
                sendOrderList.add(i);
            }
        }

        sendOrderList.sort(Comparator.comparingInt(i -> (int) model.getValueAt(i, COLUMN_SEND_ORDER_INDEX)));

        for (int i : sendOrderList) {
            MessageText messageText = new MessageText();
            messageText.setHex(model.getValueAt(i, COLUMN_HEX_INDEX).equals("H"));
            messageText.setSendText((String) model.getValueAt(i, COLUMN_CMD_INDEX));
            messageText.setFriendlyString((String) model.getValueAt(i, COLUMN_MEANING_INDEX));
            messageText.setMatchText((String) model.getValueAt(i, COLUMN_RETURN_STRING_INDEX));
            if (model.getValueAt(i, COLUMN_TIME_OUT_INDEX) != null) {
                messageText.setReceiveTimeout((Float) model.getValueAt(i, COLUMN_TIME_OUT_INDEX));
            }
            if (model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX) != null) {
                messageText.setTimeoutRetry((int) model.getValueAt(i, COLUMN_RETRY_TIMES_INDEX));
            }
            messageTextList.add(messageText);
        }
        return messageTextList;
    }

    /**
     * table属性、组件设置
     *
     * @param table 表格
     */
    public void setTable(JTable table) {
        table.setShowGrid(true);
        makeColumnNonEditable(table, COLUMN_SEND_INDEX);
        makeColumnNonEditable(table, COLUMN_ADD_TO_SCRIPT_INDEX);
        //makeColumnNonEditable(table, COLUMN_TIME_OUT_INDEX);
        //makeColumnNonEditable(table, COLUMN_RETRY_TIMES_INDEX);
        //添加复选框
        table.getColumnModel().getColumn(COLUMN_HEX_INDEX).setCellRenderer(new TableCellCheckBox.TableCellCheckBoxRenderer());
        table.getColumnModel().getColumn(COLUMN_HEX_INDEX).setCellEditor(new TableCellCheckBox.TableCellCheckBoxEditor(new TableCellCheckBox.CellCheckBox(), table));
        //添加文本框
        table.getColumnModel().getColumn(COLUMN_CMD_INDEX).setCellEditor(new TableCellTextField.TableCellTextFieldEditor(new TableCellTextField.CellTextField(), table));
        table.getColumnModel().getColumn(COLUMN_CMD_INDEX).setCellRenderer(new TableCellCheckBox.CustomTableCellRenderer());

        table.getColumnModel().getColumn(COLUMN_SEND_INDEX).setCellRenderer(new TableCellCheckBox.ButtonCellRenderer());
        table.getColumnModel().getColumn(COLUMN_SEND_INDEX).setPreferredWidth(80);
        table.getColumnModel().getColumn(COLUMN_ADD_TO_SCRIPT_INDEX).setCellRenderer(new TableCellCheckBox.ButtonCellRenderer());
        table.getColumnModel().getColumn(COLUMN_ADD_TO_SCRIPT_INDEX).setPreferredWidth(80);
        //设置表格内容颜色
        table.setForeground(Color.BLACK);//字体颜色
//        table.setFont(new Font(null, Font.PLAIN, CELL_FONT_SIZE));//字体样式
//        table.setSelectionForeground(Color.DARK_GRAY);//选中后字体颜色
//        table.setSelectionBackground(Color.decode("#E7E7E7"));//选中后字体背景
        table.setGridColor(Color.LIGHT_GRAY);//网格颜色;
        //设置“命令含义”列属性
//        DefaultTableCellRenderer rightRenderer = new DefaultTableCellRenderer();
//        rightRenderer.setBackground(Color.decode("#B6B6B6"));
//        rightRenderer.setHorizontalAlignment(DefaultTableCellRenderer.CENTER);
        table.getColumnModel().getColumn(COLUMN_MEANING_INDEX).setCellRenderer(new TableCellCheckBox.CustomTableCellRenderer());

        //设置“返回字符串”
        table.getColumnModel().getColumn(COLUMN_RETURN_STRING_INDEX).setCellEditor(new TableCellTextField.TableCellTextFieldEditor(new TableCellTextField.CellTextField(), table));
        table.getColumnModel().getColumn(COLUMN_RETURN_STRING_INDEX).setCellRenderer(new TableCellCheckBox.CustomTableCellRenderer());

        //设置"超时时间"
        //table.getColumnModel().getColumn(COLUMN_TIME_OUT_INDEX).setCellRenderer(new TableCellCheckBox.CustomTableCellRenderer());
        table.getColumnModel().getColumn(COLUMN_TIME_OUT_INDEX).setCellEditor((new TableCellTextField.NumberSpinnerCellEditor(new JSpinner(new SpinnerNumberModel(0.0, 0.0, Double.MAX_VALUE, 1.0)))));

        //设置"重试次数"
        //table.getColumnModel().getColumn(COLUMN_RETRY_TIMES_INDEX).setCellRenderer(new TableCellCheckBox.CustomTableCellRenderer());
        table.getColumnModel().getColumn(COLUMN_RETRY_TIMES_INDEX).setCellEditor((new TableCellTextField.NumberSpinnerCellEditor(new JSpinner(new SpinnerNumberModel(0, 0, Integer.MAX_VALUE, 1)))));
        //设置"发送顺序"
        table.getColumnModel().getColumn(COLUMN_SEND_ORDER_INDEX).setCellEditor((new TableCellTextField.NumberSpinnerCellEditor(new JSpinner(new SpinnerNumberModel(0, 0, Integer.MAX_VALUE, 1)))));
        //设置表头
//        table.getTableHeader().setFont(new Font("黑体", Font.BOLD, HEADER_FONT_SIZE));//设置表头字体样式
        table.getTableHeader().setBackground(Color.LIGHT_GRAY);
        table.getTableHeader().setResizingAllowed(false);//设置不允许手动改变列宽
        table.getTableHeader().setReorderingAllowed(false);//设置不允许拖动重新排序各列
        //设置行高
        table.setRowHeight(CELL_ROW_HEIGHT);
        //设置中间列列宽
        table.getColumnModel().getColumn(COLUMN_CMD_INDEX).setPreferredWidth(COLUMN_CENTER_WIDTH);
        //设置滚动面板视口大小
        table.setPreferredScrollableViewportSize(new Dimension(VIEWPORT_WIDTH, VIEWPORT_HEIGHT));
        /*设置表格中的内容居中*/
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer();
        renderer.setHorizontalAlignment(DefaultTableCellRenderer.CENTER);
        table.setDefaultRenderer(Object.class, renderer);

        //table.setModel(model);
    }

    private static void makeColumnNonEditable(JTable table, int columnIndex) {
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer();
        renderer.setEnabled(false);

        TableCellEditor editor = new DefaultCellEditor(new JTextField()) {
            @Override
            public boolean isCellEditable(java.util.EventObject e) {
                return false;
            }
        };

        table.getColumnModel().getColumn(columnIndex).setCellRenderer(renderer);
        table.getColumnModel().getColumn(columnIndex).setCellEditor(editor);
    }

}

