package ui.layout.left.display.components.container.serial.command;

import lombok.Getter;
import lombok.Setter;

import javax.swing.*;
import javax.swing.table.TableCellEditor;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

/**
 * "命令字符串"列的文本框组件配置
 * 文本输入实时格式化
 */
public class TableCellTextField {
    public static class TableCellTextFieldEditor extends DefaultCellEditor {
        private final JTable table;
        private final CellTextField cellTextField;

        public TableCellTextFieldEditor(final CellTextField cellTextField, final JTable table) {
            super(cellTextField);
            this.cellTextField = cellTextField;
            this.table = table;
            //添加键盘监听,为16进制命令添加空格
            cellTextField.addKeyListener(new KeyListener() {
                @Override
                public void keyTyped(KeyEvent e) {
                }

                @Override
                public void keyPressed(KeyEvent e) {

                }

                @Override
                public synchronized void keyReleased(KeyEvent e) {
                    if (e.getKeyCode() != KeyEvent.VK_BACK_SPACE && table.getModel().getValueAt(cellTextField.getRow(), 0).equals("H")) {
                        JTextField source = (JTextField) e.getSource();
                        String text = source.getText();
                        StringBuilder stringBuilder = new StringBuilder(text.replace(" ", ""));
                        if (stringBuilder.length() % 2 == 0 && stringBuilder.length() != 0) {
                            for (int i = stringBuilder.length() - 2; i > 0; i -= 2) {
                                stringBuilder.insert(i, " ");
                            }
                            stringBuilder.append(" ");
                            cellTextField.setText(stringBuilder.toString());
                        }
                    }
                }
            });

        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            cellTextField.setRow(row);
            return super.getTableCellEditorComponent(table, value, isSelected, row, column);
        }

        @Override
        public boolean stopCellEditing() {
            //获取当前编辑单元格的组件
            Component component = getComponent();
            //获取当前单元格编辑器输入的值
            String value = (String) getCellEditorValue();

            //如果当前单元格编辑器输入的值不是16进制，则返回false（表示数据非法，无法保存）
            if (!value.matches("([A-Fa-f0-9]{2}\\s?)*") & (table.getModel().getValueAt(cellTextField.getRow(), 0).equals("H"))) {
                component.setForeground(Color.RED);
                return false;
            }
            //格式化命令字符串
            StringBuilder stringBuilder;//去除字符串的空格
            if (table.getModel().getValueAt(cellTextField.getRow(), 0).equals("H")) {
                stringBuilder = new StringBuilder(value.replace(" ", ""));
                for (int i = stringBuilder.length() - 2; i > 0; i -= 2) {
                    stringBuilder.insert(i, " ");
                }
                cellTextField.setText(stringBuilder.toString());
            }
            component.setForeground(Color.BLACK);
            return super.stopCellEditing();
        }
    }

    @Getter
    @Setter
    public static class CellTextField extends JTextField {
        private int row;
        private int column;
    }

    public static class NumberSpinnerCellEditor extends AbstractCellEditor implements TableCellEditor {

        private final JSpinner spinner;

        private final Object value;


        public NumberSpinnerCellEditor(JSpinner spinner) {
            this.spinner = spinner;
            value = spinner.getModel().getValue();
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            // 将当前单元格的值设置到spinner上
            if (value != null) {
                spinner.setValue(value);
            } else {
                spinner.setValue(this.value);
            }
            // 根据单元格状态调整外观（可选）
            if (isSelected) {
                spinner.setBackground(table.getSelectionBackground());
                spinner.setForeground(table.getSelectionForeground());
            } else {
                spinner.setBackground(table.getBackground());
                spinner.setForeground(table.getForeground());
            }
            return spinner;
        }

        @Override
        public Object getCellEditorValue() {
            // 当编辑完成后，获取spinner中的新值并返回
            return spinner.getValue();
        }
    }
}