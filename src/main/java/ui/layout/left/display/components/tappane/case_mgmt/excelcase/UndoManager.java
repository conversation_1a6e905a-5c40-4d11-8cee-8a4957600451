package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import java.util.Stack;

public class UndoManager {
    private final Stack<ExcelCaseTableEdit> undoStack = new Stack<>();
    private final Stack<ExcelCaseTableEdit> redoStack = new Stack<>();

    public void addEdit(ExcelCaseTableEdit edit) {
        undoStack.push(edit); // 添加编辑操作到 undoStack
        redoStack.clear(); // 清空 redoStack，因为新的编辑会覆盖之前的撤销历史
    }

    public boolean canUndo() {
        return !undoStack.isEmpty(); // 如果 undoStack 不为空，则可以撤销
    }

    public boolean canRedo() {
        return !redoStack.isEmpty(); // 如果 redoStack 不为空，则可以重做
    }

    public void undo(ExcelCaseTable table) {
        if (canUndo()) {
            ExcelCaseTableEdit edit = undoStack.pop(); // 从 undoStack 弹出最近的编辑操作
            redoStack.push(edit); // 将该操作压入 redoStack
            edit.undo(table); // 执行撤销操作
        }
    }

    public void redo(ExcelCaseTable table) {
        if (canRedo()) {
            ExcelCaseTableEdit edit = redoStack.pop(); // 从 redoStack 弹出最近的撤销操作
            undoStack.push(edit); // 将该操作重新压入 undoStack
            edit.redo(table); // 执行重做操作
        }
    }
}