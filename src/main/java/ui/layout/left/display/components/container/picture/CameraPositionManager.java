package ui.layout.left.display.components.container.picture;

import bibliothek.gui.dock.common.DefaultSingleCDockable;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.Device;

import java.awt.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CameraPositionManager {
    @Getter
    @Setter
    private Map<Integer, Rectangle> rectangleMap; // deviceModelType -> index -> device
    private String filePath;
    private final Map<Device, DefaultSingleCDockable> cDockableMap;

    private static volatile CameraPositionManager instance;

    /**
     * 私有构造函数，初始化 cDockableMap。
     */
    private CameraPositionManager() {
        this.cDockableMap = new HashMap<>();
    }

    /**
     * 获取 CameraPositionManager 的单例实例。
     * 使用双重检查锁定确保线程安全。
     *
     * @return CameraPositionManager 的单例实例
     */
    public static CameraPositionManager getInstance() {
        if (instance == null) {
            synchronized (CameraPositionManager.class) {
                if (instance == null) {
                    instance = new CameraPositionManager();
                }
            }
        }
        return instance;
    }

    public void init() {
        // 初始化一次即可
        if (filePath != null) {
            return;
        }
        filePath = "D:\\FlyTest\\data\\client\\app\\config\\positionRectangle.json";
        readPositionFromFile();
    }

    /**
     * 获取 cDockableMap，该映射表存储了设备与其对应的 DefaultSingleCDockable 对象。
     *
     * @return 设备与 DefaultSingleCDockable 对象的映射表
     */
    public Map<Device, DefaultSingleCDockable> getCDockableMap() {
        return cDockableMap;
    }

    /**
     * 将 rectangleMap 中的内容保存到文件中。
     * 使用 Gson 将 rectangleMap 转换为 JSON 字符串并写入文件。
     */
    public void savePositionToFile() {
        Gson gson = new Gson();
        String json = gson.toJson(rectangleMap);
        try {
            Files.write(Paths.get(filePath), json.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 从文件中读取位置信息并解析为 rectangleMap。
     * 如果文件不存在，则创建新文件。读取成功后，将 JSON 字符串解析为 Map<Integer, Rectangle>。
     */
    public void readPositionFromFile() {
        File positionFile = null;
        if (Files.exists(Paths.get(filePath))) {
            positionFile = new File(filePath);
        } else {
            try {
                positionFile = Files.createFile(Paths.get(filePath)).toFile();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (positionFile != null) {
            try {
                String s = new String(Files.readAllBytes(Paths.get(positionFile.getAbsolutePath())), StandardCharsets.UTF_8);
                rectangleMap = JSON.parseObject(s, new TypeReference<Map<Integer, Rectangle>>() {
                });
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (rectangleMap == null) {
            rectangleMap = new HashMap<>();
        }
    }
}
