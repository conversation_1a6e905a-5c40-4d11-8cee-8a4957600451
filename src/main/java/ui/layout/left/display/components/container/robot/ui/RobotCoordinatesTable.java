package ui.layout.left.display.components.container.robot.ui;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import sdk.constants.methods.ImageMethods;
import sdk.domain.Device;
import sdk.domain.complex.CoordinatesRoi;
import sdk.domain.robot.*;
import sdk.entity.RobotDevice;
import sdk.entity.RoiKit;
import ui.base.BaseView;
import ui.base.table.CommonTable;
import ui.base.table.TableCellListener;
import ui.callback.TableMenuCallback;
import ui.config.json.devices.robot.RobotConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.picture.RoiRect;
import ui.layout.left.display.components.container.robot.RobotExceptions;
import ui.layout.left.display.components.container.robot.RobotImagePoint;
import ui.layout.left.display.components.container.robot.ui.panels.RobotMovePanel;
import ui.layout.left.display.components.container.robot.ui.panels.RobotRandomPanel;
import ui.layout.left.display.components.container.robot.ui.panels.RobotSwipePanel;
import ui.layout.left.display.dialogs.CalibrationRoiSettingDialog;
import ui.layout.left.display.dialogs.LongTouchSettingDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.table.JTableHeader;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机械臂坐标系表格
 */
@Slf4j
public class RobotCoordinatesTable extends CommonTable<RobotCoordinates>
        implements BaseView, TableMenuCallback<RobotCoordinates> {
    private final static String notSelectCameraWarning = "校准相机未选择";
    private final DeviceContainer deviceContainer;
    private final RobotDevice robotDevice;
    private final MainModel mainModel;
    private final ClientView clientView;
    private final RobotConfig robotConfig;

    @Getter
    private boolean isRobotEnabled;

    public RobotCoordinatesTable(DeviceContainer deviceContainer, MainModel mainModel, boolean isContainSlideRail, RobotConfig robotConfig) {
        this.deviceContainer = deviceContainer;
        this.clientView = deviceContainer.getClientView();
        this.mainModel = mainModel;
        this.robotDevice = (RobotDevice) deviceContainer.getDevice();
        this.robotConfig = robotConfig;
        setTableModel(new CoordinatesEditModel(getTableList()));
        createView();
        createActions();
        createMenu();
        addMenuCallback(this);
        if (!isContainSlideRail) {
            hiddenColumn(CoordinatesEditModel.slideRail.getColumnIndex());
        }
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        isRobotEnabled = isDeviceConnected;
    }

    @Override
    public void deleteRowsActivated(int[] rows) {
        if (!isRobotEnabled) {
            showNotConnectWarning();
            return;
        }
        for (int row : rows) {
//        System.out.println("deleteRowActivated:" + robotCoordinates);
            RobotCoordinates robotCoordinates = getRow(row);
            if (robotCoordinates.getName() == null || robotCoordinates.getName().trim().isEmpty()) {
                deleteRow(row);
                continue;
            }
            JsonResponse<RobotCoordinates> resp = robotDevice.deleteCoordinates(robotCoordinates);
            if (resp.isOk()) {
                deleteRow(row);
            } else {
                SwingUtil.showWebMessageDialog(this, resp.getMessage());
            }
        }
    }

    @Override
    public boolean clearTableActivated() {
        if (!isRobotEnabled) {
            showNotConnectWarning();
            return false;
        }
        RobotCoordinatesQuery robotCoordinatesQuery = new RobotCoordinatesQuery();
        robotCoordinatesQuery.setProjectName(mainModel.getAppInfo().getProject());
        robotCoordinatesQuery.setDeviceUniqueCode(robotDevice.getDeviceUniqueCode());
        JsonResponse<String> resp = robotDevice.clearCoordinates(robotCoordinatesQuery);
        if (resp.isOk()) {
            clearTable();
            ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotSwipePanel().clearSwipe();
            return true;
        } else {
            SwingUtil.showWebMessageDialog(this, resp.getMessage());
            return false;
        }
    }

    @Override
    public void updateTableActivated(ActionEvent e) {
        TableCellListener tableCellListener = (TableCellListener) e.getSource();
        int row = tableCellListener.getRow();
        int column = tableCellListener.getColumn();
        RobotCoordinates robotCoordinates = getRow(row);
        System.out.println("update robotCoordinates:" + robotCoordinates);

        if (column == CoordinatesEditModel.name.getColumnIndex()) {
            String newName = String.valueOf(tableCellListener.getNewValue()).trim();
            String oldName = String.valueOf(tableCellListener.getOldValue()).trim();
            if (newName.isEmpty()) {
                SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "坐标名不能为空!");
                setValueAt(oldName, row, column);
                return;
            } else {
                //TODO：改为查询数据库字段的唯一性
                List<String> nameList = getTableList().stream().map(RobotCoordinates::getName).collect(Collectors.toList());
                nameList.remove(row);
//                System.out.println("nameList:" + nameList);
//                System.out.println("raw nameList:" + getTableList().stream().map(RobotCoordinates::getName).collect(Collectors.toList()));
                if (nameList.contains(newName)) {
                    SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "存在相同坐标名!");
                    setValueAt(oldName, row, column);
                    return;
                } else if (oldName.isEmpty()) {
                    robotCoordinates.setName(newName);
                    JsonResponse<RobotCoordinates> resp = robotDevice.addCoordinates(robotCoordinates);
                    if (resp.isOk()) {
                        RobotCoordinates coordinates = resp.getData();
                        getTableList().set(row, coordinates);
                    } else {
                        SwingUtil.showWebMessageDialog(this, resp.getMessage());
                        setValueAt(oldName, row, column);
                    }
                    return;
                } else {
                    robotCoordinates.setName(newName);
                }
            }
        } else {
            try {
                if (column == CoordinatesEditModel.coordinateX.getColumnIndex()) {
                    robotCoordinates.setX(Double.parseDouble(String.valueOf(tableCellListener.getNewValue())));
                } else if (column == CoordinatesEditModel.coordinateY.getColumnIndex()) {
                    robotCoordinates.setY(Double.parseDouble(String.valueOf(tableCellListener.getNewValue())));
                } else if (column == CoordinatesEditModel.coordinateZ.getColumnIndex()) {
                    robotCoordinates.setZ(Double.parseDouble(String.valueOf(tableCellListener.getNewValue())));
                } else if (column == CoordinatesEditModel.coordinateR.getColumnIndex()) {
                    robotCoordinates.setR(Double.parseDouble(String.valueOf(tableCellListener.getNewValue())));
                } else if (column == CoordinatesEditModel.slideRail.getColumnIndex()) {
                    robotCoordinates.setSlideRail(Double.parseDouble(String.valueOf(tableCellListener.getNewValue())));
                }
            } catch (NumberFormatException ex) {
                SwingUtil.showWarningDialog(this, ex.getMessage());
                setValueAt(tableCellListener.getOldValue(), row, column);
                return;
            }
        }

        JsonResponse<RobotCoordinates> resp = robotDevice.updateCoordinates(robotCoordinates);
        if (!resp.isOk()) {
            SwingUtil.showWebMessageDialog(this, resp.getMessage());
            setValueAt(tableCellListener.getOldValue(), row, column);
        }
    }

    protected void setPreferredColumn() {
        getColumnModel().getColumn(CoordinatesEditModel.name.getColumnIndex()).setPreferredWidth(60);
        getColumnModel().getColumn(CoordinatesEditModel.coordinateX.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(CoordinatesEditModel.coordinateY.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(CoordinatesEditModel.coordinateZ.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(CoordinatesEditModel.coordinateR.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(CoordinatesEditModel.slideRail.getColumnIndex()).setPreferredWidth(30);
        getColumnModel().getColumn(CoordinatesEditModel.executeThisRow.getColumnIndex()).setPreferredWidth(60);
        getColumnModel().getColumn(CoordinatesEditModel.addToScriptForNormalClick.getColumnIndex()).setPreferredWidth(60);
    }

    @Override
    public void createView() {
        super.createView();
        makeCellCenter();
        JTableHeader tableHeader = getTableHeader();
        getTableHeader().setPreferredSize(new Dimension(tableHeader.getWidth(), 60));
    }

    public void initData() {
        RobotCoordinatesQuery robotCoordinatesQuery = new RobotCoordinatesQuery();
        robotCoordinatesQuery.setProjectName(mainModel.getAppInfo().getProject());
        robotCoordinatesQuery.setDeviceUniqueCode(robotDevice.getDeviceUniqueCode());
        JsonResponse<List<RobotCoordinates>> resp = robotDevice.getAllRobotCoordinates(robotCoordinatesQuery);
        if (resp.isOk()) {
            clearTable();
            int row = 0;
            for (RobotCoordinates robotCoordinates : resp.getData()) {
                RobotCoordinates coordinates = addRowAtSelectedPos(robotCoordinates);
                JsonResponse<CoordinatesRoi> response = robotDevice.fetchRobotRoiByCoordinatesUUID(coordinates.getUuid());
                CoordinatesRoi coordinatesRoi = response.getData();
                if (response.isOk() && coordinatesRoi != null) {
                    coordinates.setCoordinatesRoi(coordinatesRoi);
                    setValueAt(coordinatesRoi, row, CoordinatesEditModel.calibrationImage.getColumnIndex());
                }
                row++;
            }
        } else {
            SwingUtil.showWarningDialog(this, resp.getMessage(), "提醒", "获取机械臂坐标出错:");
        }
    }


    private class SwipeStartActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String swipeName = getCoordinateName("滑动时要求当前行有明确的坐标名");
            if (swipeName != null) {
                ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotSwipePanel().startSwipe(swipeName);
            }
        }
    }

    private class SwipeEndActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            RobotSwipePanel view = ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotSwipePanel();

            if (!view.isSwipeStarted()) {
                SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "还未确定滑动起点，操作无效！");
                return;
            }
            String swipeName = getCoordinateName("滑动时要求当前行有明确的坐标名");
            if (swipeName != null) {
                if (swipeName.equals(view.getLastSwipeName())) {
                    SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "滑动点和上一点重合，操作无效！");
                } else {
                    view.addSwipe(swipeName);
                }
            }
        }
    }

    private class MoveStartActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String swipeName = getCoordinateName("移动时要求当前行有明确的坐标名");
            if (swipeName != null) {
                ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotMovePanel().startMove(swipeName);
            }
        }
    }

    private class MoveEndActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            RobotMovePanel view = ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotMovePanel();

            if (!view.moveStarted()) {
                SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "还未确定移动起点，操作无效！");
                return;
            }
            String moveName = getCoordinateName("移动时要求当前行有明确的坐标名");
            if (moveName != null) {
                if (moveName.equals(view.getLastMoveName())) {
                    SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "移动和上一点重合，操作无效！");
                } else {
                    view.addMove(moveName);
                }
            }
        }
    }

    private class SwipeQuicklyListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int row = getSelectedRow();
            RobotCoordinates robotCoordinates = getRow(row);
            DirectionDialog dialog = DirectionDialog.getInstance(mainModel, robotDevice, robotCoordinates.getName());
            dialog.setModal(true);
            dialog.setVisible(true);

        }
    }

    private String getCoordinateName(String message) {
        int row = getSelectedRow();
        RobotCoordinates robotCoordinates = getRow(row);
        String name = robotCoordinates.getName();
        if (name == null || name.trim().isEmpty()) {
            SwingUtil.showWarningDialog(RobotCoordinatesTable.this, message);
            return null;
        }
        return name;
    }

    private class randomTopLeftActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String randomName = getCoordinateName("选取范围点时要求当前行有明确的坐标名");
            if (randomName != null) {
                ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotRandomPanel().leftRandom(randomName);
            }
        }
    }

    private class randomLowerRightActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            RobotRandomPanel view = ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotRandomPanel();

            if (!view.isRandomStarted()) {
                SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "还未确定左上角，操作无效！");
                return;
            }
            String randomName = getCoordinateName("选取范围点时要求当前行有明确的坐标名");
            if (randomName != null) {
                if (randomName.equals(view.getLastRandomName())) {
                    SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "范围点和上一个点相同，操作无效！");
                } else {
                    view.addRandom(randomName);
                }
            }
        }
    }

    private class LongTouchActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int row = getSelectedRow();
            RobotCoordinates robotCoordinates = getRow(row);
            LongTouchSettingDialog dialog = LongTouchSettingDialog.getInstance(mainModel, robotDevice, robotCoordinates.getName());
            dialog.setModal(true);
            dialog.setVisible(true);
        }
    }


    private class ReplaceCurrentCoordinatesActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int row = getSelectedRow();
            RobotCoordinates robotCoordinates = getRow(row);
            FeedbackData data = robotDevice.fetchFeedbackData();
            robotCoordinates.setX(data.getX());
            robotCoordinates.setY(data.getY());
            robotCoordinates.setZ(data.getZ());
            robotCoordinates.setR(data.getR());
            setCoordinates(row, robotCoordinates);
            robotDevice.updateCoordinates(robotCoordinates);
        }
    }

    private class KeepPressingActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String randomName = getCoordinateName("选取范围点时要求当前行有明确的坐标名");
            if (randomName != null) {
                Operation operation = Operation.buildOperation(robotDevice);
                operation.setOperationMethod(DeviceMethods.pressTouch);
                operation.setOperationObject(randomName);
                mainModel.getOperationModel().updateOperation(operation);
            }
        }
    }

    private class ReleaseButtonActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String randomName = getCoordinateName("选取范围点时要求当前行有明确的坐标名");
            if (randomName != null) {
                Operation operation = Operation.buildOperation(robotDevice);
                operation.setOperationMethod(DeviceMethods.releaseTouch);
                operation.setOperationObject(randomName);
                mainModel.getOperationModel().updateOperation(operation);
            }
        }
    }

    private class SettingCircleCenterActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String coordinateName = getCoordinateName("选取的坐标必须有坐标名");
            if (coordinateName != null) {
                ((RobotContainer) deviceContainer).getRobotOperationPanel().getRobotActionPanel().getRobotCirclePanel().setCircleCenterCoordinate(coordinateName);
            }
        }
    }

    private void setCoordinates(int row, RobotCoordinates robotCoordinates) {
        setValueAt(robotCoordinates.getX(), row, CoordinatesEditModel.coordinateX.getColumnIndex());
        setValueAt(robotCoordinates.getY(), row, CoordinatesEditModel.coordinateY.getColumnIndex());
        setValueAt(robotCoordinates.getZ(), row, CoordinatesEditModel.coordinateZ.getColumnIndex());
        setValueAt(robotCoordinates.getR(), row, CoordinatesEditModel.coordinateR.getColumnIndex());
    }


    private void addSwipeMenu() {
        makePopupMenu("设置为滑动起点", new SwipeStartActionListener());
        makePopupMenu("设置为滑动终点", new SwipeEndActionListener());
        makePopupMenu("快速滑动", new SwipeQuicklyListener());
    }

    private void addMoveMenu() {
        makePopupMenu("设置为移动起点", new MoveStartActionListener());
        makePopupMenu("设置为移动终点", new MoveEndActionListener());
    }

    private void addRandomMenu() {
        makePopupMenu("设置为随机范围左上角", new randomTopLeftActionListener());
        makePopupMenu("设置为随即范围右下角", new randomLowerRightActionListener());
    }

    private void addLongTouchMenu() {
        makePopupMenu("当前坐标长按", new LongTouchActionListener());
        makePopupMenu("当前坐标持续按下", new KeepPressingActionListener());
        makePopupMenu("当前坐标抬起", new ReleaseButtonActionListener());
    }

    private void addCircleMenu() {
        makePopupMenu("设为圆心", new SettingCircleCenterActionListener());
    }

    @Override
    protected void createMenu() {
        super.createMenu();
        addSwipeMenu();
        addMenuSeparator();
        addMoveMenu();
        addMenuSeparator();
        addRandomMenu();
        addMenuSeparator();
        addCircleMenu();
        addMenuSeparator();
        makePopupMenu("替换当前坐标", new ReplaceCurrentCoordinatesActionListener());
        addMenuSeparator();
        addLongTouchMenu();
        addMenuSeparator();
        addDelMenu();
        addClearMenu();
    }

    /**
     * 获取MovLEntity坐标实体类
     *
     * @param row 行
     * @return MovLEntity
     */
    public MoveEntity getMovLEntity(int row) {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(Double.parseDouble(String.valueOf(getValueAt(row, CoordinatesEditModel.coordinateX.getColumnIndex()))));
        moveEntity.setY(Double.parseDouble(String.valueOf(getValueAt(row, CoordinatesEditModel.coordinateY.getColumnIndex()))));
        moveEntity.setZ(Double.parseDouble(String.valueOf(getValueAt(row, CoordinatesEditModel.coordinateZ.getColumnIndex()))));
        moveEntity.setR(Double.parseDouble(String.valueOf(getValueAt(row, CoordinatesEditModel.coordinateR.getColumnIndex()))));
        moveEntity.setSlideRail(Double.parseDouble(String.valueOf(getValueAt(row, CoordinatesEditModel.slideRail.getColumnIndex()))));
        RobotContainer robotContainer = (RobotContainer) deviceContainer;
        moveEntity.setAcc(robotContainer.getRobotAdvancedSettingsPanel().getRobotParamSettingsPanel().getAccelerationSpeedFactor()); //加速度获取
        return moveEntity;
    }

    private int findRowByName(String name) {
        return getTableList().stream().map(RobotCoordinates::getName).collect(Collectors.toList()).indexOf(name);
    }

    public MoveEntity getMovLEntity(String name) throws RobotExceptions.RobotCoordinateNamesNotFoundException {
        int row = findRowByName(name);
        if (row == -1) {
            throw new RobotExceptions.RobotCoordinateNamesNotFoundException(String.format("%s坐标未找到", name));
        }
        return getMovLEntity(row);
    }

    /**
     * 获取坐标名称
     *
     * @param row 行
     * @return 坐标名称
     */
    private String getCoordinateName(int row) {
        Object name = getValueAt(row, CoordinatesEditModel.name.getColumnIndex());
        if (name == null) {
            return "";
        }
        return ((String) name).trim();
    }

    /**
     * 提醒机械臂未连接对话框
     */
    private void showNotConnectWarning() {
        SwingUtil.showWarningDialog(RobotCoordinatesTable.this, "当前机械臂未连接!");
    }

    /**
     * 添加坐标到脚本
     *
     * @param row 表格行
     */
    private void addToScriptForCoordinatesPureClick(int row) {
        MoveEntity moveEntity = getMovLEntity(row);
        String coordinateName = getCoordinateName(row);
        SpeedCoordinates speedCoordinates = new SpeedCoordinates();
        speedCoordinates.setCoordinateName(coordinateName);
        Operation operation = Operation.buildOperation(robotDevice);
        if (!coordinateName.isEmpty()) {
            operation.setOperationMethod(DeviceMethods.touch);
            operation.setOperationObject(speedCoordinates);
            operation.setRetry(3);
        } else {
            operation.setOperationMethod(DeviceMethods.touch);
            operation.setOperationObject(moveEntity);
        }
        deviceContainer.getMainModel().getOperationModel().updateOperation(operation);
    }

    private void addToScriptForCoordClickAndTouchCheck(int row) {
        String coordinateName = getCoordinateName(row);
        Operation operation = Operation.buildOperation(robotDevice);
        if (!coordinateName.isEmpty()) {
            operation.setOperationMethod(DeviceMethods.touchAndCheckTouchPoint);
            operation.setOperationObject(coordinateName);
            deviceContainer.getMainModel().getOperationModel().updateOperation(operation);
        } else {
            SwingUtil.showWarningDialog(this, "必须给坐标命名");
        }
    }

    private void addToScriptForCoordClickAndChangeCheck(int row) {
        String coordinateName = getCoordinateName(row);
        if (robotConfig.getMonitorArea() != null) {
            if (!coordinateName.isEmpty()) {
                RecognitionAlgorithmSelectDialog dialog = new RecognitionAlgorithmSelectDialog();
                List<Object> selectedOption = dialog.showDialog();
                if (selectedOption != null) {
                    Device cameraDevice = ((RobotContainer) deviceContainer)
                            .getRobotAdvancedSettingsPanel()
                            .getRobotCalibrationPanel().getSelectedDevice();
                    if (cameraDevice == null) {
                        log.info(notSelectCameraWarning);
                        return;
                    }
                    RobotImagePoint robotImagePoint = RobotImagePoint.builder()
                            .coordinateName(coordinateName)
                            .selectedOption((String) selectedOption.get(0))
                            .similarity((int) selectedOption.get(1))
                            .robotConfig(robotConfig)
                            .projectName(mainModel.getAppInfo().getProject())
                            .build();
                    Operation operation = Operation.buildOperation(cameraDevice);
                    operation.setOperationMethod(ImageMethods.touchAndCheckDisplayChange);
                    operation.setOperationObject(robotImagePoint);
                    deviceContainer.getMainModel().getOperationModel().updateOperation(operation);
                }
            } else {
                SwingUtil.showWarningDialog(this, "必须给坐标命名");
            }
        } else {
            SwingUtil.showWarningDialog(this, "请设置全局监控或变化区域");
        }
    }

    /**
     * 设置校准图片
     *
     * @param row 表格行
     */
    private void setCalibrationImage(int row) {
        Device device = ((RobotContainer) deviceContainer)
                .getRobotAdvancedSettingsPanel()
                .getRobotCalibrationPanel()
                .getSelectedDevice();
        if (device == null) {
            log.info(notSelectCameraWarning);
        }
        RobotCoordinates coordinates = getRow(row);
        CoordinatesRoi coordinatesRoi = coordinates.getCoordinatesRoi();
        //弹出一个图片设置对话框
        CalibrationRoiSettingDialog dialog = CalibrationRoiSettingDialog.getInstance(this,
                "请设置机械臂点击后一定会变化的区域",
                clientView,
                mainModel,
                device,
                coordinatesRoi == null ? null : coordinatesRoi.getScaledRoiRect());
        dialog.setVisible(true);
        if (dialog.isConfirmed()) {
            //确认或者删除ROI
            RoiRect calibrationRoi = dialog.getRoi();
            if (calibrationRoi == null) {
                if (coordinatesRoi != null) {
                    //删除坐标ROI
                    JsonResponse<String> response = robotDevice.deleteRobotRoi(coordinatesRoi.getCoordinatesUUID());
                    if (response.isOk()) {
                        coordinates.setCoordinatesRoi(null);
                        setValueAt("", row, CoordinatesEditModel.calibrationImage.getColumnIndex());
                    } else {
                        SwingUtil.showWebMessageDialog(this, response.getMessage());
                    }
                }
            } else {
                //设定坐标ROI
                coordinatesRoi = new CoordinatesRoi();
                coordinatesRoi.setStartX(calibrationRoi.getPointStart().getX());
                coordinatesRoi.setStartY(calibrationRoi.getPointStart().getY());
                coordinatesRoi.setEndX(calibrationRoi.getPointEnd().getX());
                coordinatesRoi.setEndY(calibrationRoi.getPointEnd().getY());
                coordinatesRoi.setTypeId(RoiKit.getRectTypeId());
                coordinatesRoi.setCoordinatesUUID(coordinates.getUuid());
                coordinatesRoi.setDeviceType(robotDevice.getDeviceType());
                JsonResponse<CoordinatesRoi> response = robotDevice.addRobotRoi(coordinatesRoi);
                coordinatesRoi = response.getData();
                if (response.isOk() && coordinatesRoi != null) {
                    System.out.println("coordinatesRoi:" + coordinatesRoi);
                    coordinates.setCoordinatesRoi(coordinatesRoi);
                    setValueAt(coordinatesRoi, row, CoordinatesEditModel.calibrationImage.getColumnIndex());
                } else {
                    SwingUtil.showWebMessageDialog(this, response.getMessage());
                }
            }
        }
    }

    /**
     * 单击
     */
    private void singleClick() {
        int row = getSelectedRow();
        int column = getSelectedColumn();
        MoveEntity moveEntity = getMovLEntity(row);
        if (column == CoordinatesEditModel.calibrationImage.getColumnIndex()) {
            setCalibrationImage(row);
        } else if (column == CoordinatesEditModel.executeThisRow.getColumnIndex()) {
            if (isRobotEnabled) {
//                点击@设置速度和加速度
//                RobotContainer robotContainer= (RobotContainer) deviceContainer;
//                robot.setSpeed(robotContainer.getSpeedAccSettingsPanel().getSpeed());
//                robot.setAcc(robotContainer.getSpeedAccSettingsPanel().getAccelerate());
                robotDevice.touch(moveEntity);
            } else {
                showNotConnectWarning();
            }
        } else if (column == CoordinatesEditModel.addToScriptForNormalClick.getColumnIndex()) {
            //机械臂点击坐标
            addToScriptForCoordinatesPureClick(row);
        } else if (column == CoordinatesEditModel.addToScriptForTouchPointClick.getColumnIndex()) {
            //机械臂点击并检测报点
            addToScriptForCoordClickAndTouchCheck(row);
        } else if (column == CoordinatesEditModel.addToScriptForTouchPointClickChange.getColumnIndex()) {
            //机械臂点击自动判断
            addToScriptForCoordClickAndChangeCheck(row);
        }
    }

    /**
     * 双击
     */
    private void doubleClick() {
        //TBD
    }

    @Override
    public void createActions() {
        super.createActions();
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 1 && e.getButton() == MouseEvent.BUTTON1) {
                    singleClick();
                } else if (e.getClickCount() == 2 && e.getButton() == MouseEvent.BUTTON1) {
                    doubleClick();
                }
            }
        });
    }

}
