package ui.layout.left.display.components.container.can;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import ui.base.BaseView;
import ui.base.OperationAssemblerPanel;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.can.model.SignalMeasurement;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于CAN信号检测
 */
@Slf4j
public class CanSignalInspectorFunctionView extends OperationAssemblerPanel<CanSignalInspectorRequest> implements BaseView {

    private DeviceContainer canContainer;
    private MainModel mainModel;
    private int channel;

    // 信号检测部分组件
    private JTextField messageNameTextField; // 报文名称输入框
    private JTable signalTable; // 信号表格
    private DefaultTableModel signalTableModel; // 信号表格模型
    private JScrollPane signalScrollPane; // 信号表格滚动面板
    private JButton addSignalToScriptButton; // 添加到脚本按钮

    public CanSignalInspectorFunctionView() {
        initComponents();
        createView();
    }

    public CanSignalInspectorFunctionView(DeviceContainer canContainer, MainModel mainModel, int channel) {
        this.canContainer = canContainer;
        this.mainModel = mainModel;
        this.channel = channel;
        initComponents();
        createView();
    }

    /**
     * 初始化组件
     */
    private void initComponents() {
        // 初始化报文名称输入框
        messageNameTextField = new JTextField();

        // 初始化信号表格模型
        String[] columnNames = {"信号名称", "信号值"};
        signalTableModel = new DefaultTableModel(columnNames, 0);

        // 初始化信号表格
        signalTable = new JTable(signalTableModel);
        signalTable.setRowHeight(25);
        signalTable.getColumnModel().getColumn(0).setPreferredWidth(150);
        signalTable.getColumnModel().getColumn(1).setPreferredWidth(100);
        signalTable.setGridColor(Color.LIGHT_GRAY); // 设置网格线颜色
        signalTable.setShowGrid(true); // 显示网格线
        signalTable.setShowHorizontalLines(true); // 显示水平网格线
        signalTable.setShowVerticalLines(true); // 显示垂直网格线

        // 添加默认的10行到表格
        for (int i = 0; i < 10; i++) {
            signalTableModel.addRow(new Object[]{"", ""});
        }

        // 初始化信号表格滚动面板
        signalScrollPane = new JScrollPane(signalTable);
        signalScrollPane.setPreferredSize(new Dimension(300, 200));

        // 初始化添加到脚本按钮
        addSignalToScriptButton = SwingUtil.addNewScriptButton("添加到脚本");
        addSignalToScriptButton.addActionListener(e -> addSignalToScript());
    }

    /**
     * 创建视图
     */
    @Override
    public void createView() {
        setLayout(new BorderLayout());
        setBorder(new EmptyBorder(10, 10, 10, 10));
        add(createSignalInspectionPanel(), BorderLayout.CENTER);
    }

    /**
     * 创建信号检测面板
     */
    private JPanel createSignalInspectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "信号检测", TitledBorder.LEFT, TitledBorder.TOP));

        // 创建主面板，使用垂直BoxLayout
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(new EmptyBorder(5, 5, 5, 5));

        // --- 报文名称行 ---
        JPanel messageNamePanel = new JPanel(new BorderLayout(10, 0));
        messageNamePanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 30)); // 限制最大高度

        JPanel messageLabelPanel = new JPanel(new BorderLayout());
        messageLabelPanel.setPreferredSize(new Dimension(100, 25));
        messageLabelPanel.add(new JLabel("报文名称:"), BorderLayout.WEST);
        messageNamePanel.add(messageLabelPanel, BorderLayout.WEST);

        // 将输入框放入一个固定高度的面板中
        JPanel textFieldWrapper = new JPanel(new BorderLayout());
        textFieldWrapper.setMaximumSize(new Dimension(Integer.MAX_VALUE, 30));
        textFieldWrapper.add(messageNameTextField, BorderLayout.CENTER);
        messageNamePanel.add(textFieldWrapper, BorderLayout.CENTER);

        mainPanel.add(messageNamePanel);
        mainPanel.add(Box.createVerticalStrut(10));

        // --- 信号表格 ---
        JPanel signalTablePanel = new JPanel(new BorderLayout());

        // 表格标题
        JPanel tableHeaderPanel = new JPanel(new BorderLayout());
        tableHeaderPanel.add(new JLabel("信号列表:"), BorderLayout.WEST);
        signalTablePanel.add(tableHeaderPanel, BorderLayout.NORTH);
        signalTablePanel.add(signalScrollPane, BorderLayout.CENTER);
        mainPanel.add(signalTablePanel);
        mainPanel.add(Box.createVerticalStrut(10));

        // --- 添加到脚本按钮 ---
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0)); // 按钮右对齐
        buttonPanel.add(addSignalToScriptButton);
        mainPanel.add(buttonPanel);

        panel.add(mainPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 添加信号到脚本
     */
    private void addSignalToScript() {
        String messageName = messageNameTextField.getText().trim();
        int selectedRow = signalTable.getSelectedRow();

        if (messageName.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入报文名称", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请选择一个信号", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        try {
            // 创建CAN信号检查对象
            CanSignalInspectorRequest canSignalInspectorRequest = new CanSignalInspectorRequest();
            canSignalInspectorRequest.setMessageName(messageName);

            // 获取所有有效的信号测量
            List<SignalMeasurement> measurements = getValidSignalMeasurements();
            if (measurements.isEmpty()) {
                throw new Exception("没有有效的信号，请至少添加一个有效的信号名称和值");
            }
            canSignalInspectorRequest.setSignalMeasurements(measurements);

            // 创建操作对象
            Operation operation = Operation.buildOperation(canContainer.getDevice());
            operation.getOperationTarget().setChannel(channel);
            operation.setOperationMethod(DeviceMethods.inspectCanSignal);
            operation.setOperationObject(canSignalInspectorRequest);

            // 添加到脚本
            mainModel.getOperationModel().updateOperation(operation);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "添加信号到脚本失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            log.error("添加信号到脚本失败", e);
        }
    }

    /**
     * 从表格中获取有效的信号测量列表
     * 有效条目是指信号名称和信号值都不为空的行
     *
     * @return 有效的信号测量列表
     */
    public List<SignalMeasurement> getValidSignalMeasurements() {
        List<SignalMeasurement> validMeasurements = new ArrayList<>();

        // 检查表格模型中每一行
        for (int i = 0; i < signalTableModel.getRowCount(); i++) {
            String signalName = (String) signalTableModel.getValueAt(i, 0);
            String signalValue = (String) signalTableModel.getValueAt(i, 1);

            // 只添加信号名称和信号值都不为空的行
            if (signalName != null && !signalName.trim().isEmpty() &&
                    signalValue != null && !signalValue.trim().isEmpty()) {
                validMeasurements.add(new SignalMeasurement(signalName.trim(), signalValue.trim()));
            }
        }

        return validMeasurements;
    }

    /**
     * 实现OperationAssembler接口的方法，从Operation构建UI
     */
    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        CanSignalInspectorRequest canSignalInspectorRequest = JSON.to(CanSignalInspectorRequest.class, injectedOperation.getOperationObject());
        // 设置报文名称
        if (canSignalInspectorRequest.getMessageName() != null && !canSignalInspectorRequest.getMessageName().isEmpty()) {
            messageNameTextField.setText(canSignalInspectorRequest.getMessageName());
        }

        // 设置信号列表
        if (canSignalInspectorRequest.getSignalMeasurements() != null && !canSignalInspectorRequest.getSignalMeasurements().isEmpty()) {
            // 确保表格有足够的行
            while (signalTableModel.getRowCount() < canSignalInspectorRequest.getSignalMeasurements().size()) {
                signalTableModel.addRow(new Object[]{"", ""});
            }

            // 使用setValue设置信号值
            for (int i = 0; i < canSignalInspectorRequest.getSignalMeasurements().size(); i++) {
                SignalMeasurement measurement = canSignalInspectorRequest.getSignalMeasurements().get(i);
                signalTableModel.setValueAt(measurement.getSignalName(), i, 0);
                signalTableModel.setValueAt(measurement.getSignalValue(), i, 1);
            }

            // 清空未使用的行
            for (int i = canSignalInspectorRequest.getSignalMeasurements().size(); i < signalTableModel.getRowCount(); i++) {
                signalTableModel.setValueAt("", i, 0);
                signalTableModel.setValueAt("", i, 1);
            }

            // 如果行数少于10，补充空行到10行
            while (signalTableModel.getRowCount() < 10) {
                signalTableModel.addRow(new Object[]{"", ""});
            }
        }
    }

    /**
     * 实现OperationAssembler接口的方法，组装Operation
     */
    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        //TODO：实现自动化组装
        injectedOperation.setOperationObject(assembleOperationObject());
        return injectedOperation;

    }

    /**
     * 实现OperationAssembler接口的方法，组装OperationObject
     */
    @Override
    public CanSignalInspectorRequest assembleOperationObject() {
        CanSignalInspectorRequest canSignalInspectorRequest = new CanSignalInspectorRequest();
        canSignalInspectorRequest.setMessageName(messageNameTextField.getText().trim());
        // 获取有效的信号测量
        List<SignalMeasurement> validMeasurements = getValidSignalMeasurements();
        canSignalInspectorRequest.setSignalMeasurements(validMeasurements);

        return canSignalInspectorRequest;
    }

    /**
     * 实现OperationAssembler接口的方法，检查用户输入
     */
    @Override
    public boolean checkUserInput() {
        // 检查报文名称
        String messageName = messageNameTextField.getText().trim();
        if (messageName.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入报文名称", "提示", JOptionPane.WARNING_MESSAGE);
            return false;
        }

        // 检查是否有有效的信号
        List<SignalMeasurement> validMeasurements = getValidSignalMeasurements();
        if (validMeasurements.isEmpty()) {
            JOptionPane.showMessageDialog(this, "没有有效的信号，请至少添加一个有效的信号名称和值", "提示", JOptionPane.WARNING_MESSAGE);
            return false;
        }

        return true;
    }

    /**
     * 获取完美尺寸
     */
    @Override
    public Dimension getPerfectDimension() {
        return new Dimension(400, 350);
    }
}
