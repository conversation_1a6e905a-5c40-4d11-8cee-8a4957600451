package ui.layout.left.display.components.container.screen.touchPoint;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class Point {
    private BigDecimal x;
    private BigDecimal y;
    private static BigDecimal delta_Y;
    private static BigDecimal delta_X;
    private static BigDecimal k;
    private static double a;
    //  d在x轴的投影
    private static BigDecimal xShadow;
    //  d在y轴的投影
    private static BigDecimal yShadow;
    //  step在x轴的投影
    private static BigDecimal xShadow1;
    //  step在y轴的投影
    private static BigDecimal yShadow1;
    private static BigDecimal edgeDistance;
    private static BigDecimal step;
    //    边缘点:机械臂坐标
    private static List<Point> topRowPoints = new ArrayList<>();
    private static List<Point> bottomRowPoints = new ArrayList<>();
    private static List<Point> leftColumnPoints = new ArrayList<>();
    private static List<Point> rightColumnPoints = new ArrayList<>();
    //    边缘点:屏幕坐标
    private static List<Point> topScreenPoints = new ArrayList<>();
    private static List<Point> bottomScreenPoints = new ArrayList<>();
    private static List<Point> leftScreenPoints = new ArrayList<>();
    private static List<Point> rightScreenPoints = new ArrayList<>();
    private static List<EntryPoints> entryPointsList = new ArrayList<>();

    private static Point pointOnLastVerticalEdgeLine;
    private static int rowNum;

    public Point() {
        x = null;
        y = null;
    }


    public static class EdgePoints {
        public List<Point> topRowPoints;
        public List<Point> bottomRowPoints;
        public List<Point> leftColumnPoints;
        public List<Point> rightColumnPoints;

        public EdgePoints(List<Point> topRowPoints, List<Point> bottomRowPoints, List<Point> leftColumnPoints, List<Point> rightColumnPoints) {
            this.topRowPoints = topRowPoints;
            this.bottomRowPoints = bottomRowPoints;
            this.leftColumnPoints = leftColumnPoints;
            this.rightColumnPoints = rightColumnPoints;
        }
    }

    static class EntryPoints {
        Point startPoint;
        Point endPoint;

        public EntryPoints(Point startPoint, Point endPoint) {
            this.startPoint = startPoint;
            this.endPoint = endPoint;
        }
    }

    static class CirclePoints {
        Point start1;
        Point end1;
        Point start2;
        Point end2;

        public CirclePoints(Point start1, Point end1, Point start2, Point end2) {
            this.start1 = start1;
            this.end1 = end1;
            this.start2 = start2;
            this.end2 = end2;
        }
    }

    public Point(BigDecimal x, BigDecimal y) {
        this.x = x;
        this.y = y;
    }

    public boolean isNull() {
        return getX() == null || getY() == null;
    }

    public static BigDecimal getK(Point p1, Point p2) {
//      p2-p1 ,p2是原点
        delta_Y = p1.getY().subtract(p2.getY());
        delta_X = p1.getX().subtract(p2.getX());
        k = (delta_X.multiply(BigDecimal.valueOf(-1)).divide(delta_Y, 20, RoundingMode.UP));
        return k;
    }

    /**
     * getAlpha  Alpha： p1,p2 与横轴形成的角度
     *
     * @param p1 左下角顶点
     * @param p2 左上角顶点
     */
    public static Double getAlpha(Point p1, Point p2) {
//      p2-p1 ,p2是原点
        delta_Y = p1.getY().subtract(p2.getY());
        delta_X = p1.getX().subtract(p2.getX());
        if (delta_Y.compareTo(BigDecimal.valueOf(0)) == 0) {
            a = 90;
            return a;
        }
        //        按照坐标系 k=-y/x
        k = (delta_X.multiply(BigDecimal.valueOf(-1)).divide(delta_Y, RoundingMode.FLOOR));
        double tanA = Math.atan(Double.parseDouble(String.valueOf(k)));
        a = tanA * 180 / Math.PI;
//      Math.atan 返回 一个逆时针角度
        if (k.compareTo(BigDecimal.valueOf(0)) < 0) {
            a = 180 + a;
        }
//        弧度=角度*Math.PI/180;  atan 返回的是一个逆时针角度的值   原点到一个点(坐标)的线段逆时针旋转的弧度值就是Math.atan2()的返回值

        return a;
    }

    /**
     * @method getBeta Beta和Alpha 互为余角
     */
    public static Double getBeta(double a) {
        Double beta = null;
        if (a > 90) beta = a - 90;
        if (a < 90) beta = 90 - a;
        return beta;
    }

    /**
     * @param d 边距
     * @param b beta的简写  是角度
     * @method getXShadow :获取边距分解在x轴上的距离
     */
    public static BigDecimal getXShadow(BigDecimal d, double b) {
//        Math.toRadians(b)角度转弧度
        BigDecimal x = d.multiply(BigDecimal.valueOf(Math.cos(Math.toRadians(b))));
//        10位小数以后向下取整
//        x = x.setScale(20, RoundingMode.UP);
        x = x.setScale(10, RoundingMode.DOWN);
        return x;
    }

    public static BigDecimal getYShadow(BigDecimal d, double b) {
        BigDecimal y = d.multiply(BigDecimal.valueOf(Math.sin(Math.toRadians(b))));
//        y = y.setScale(20, RoundingMode.UP);
        y = y.setScale(10, RoundingMode.DOWN);
        return y;
    }

    //    求中心点
    public static Point getPivot(Point pointLeftBottom, Point pointLeftTop, Point pointRightTop, Point pointRightBottom) {
        BigDecimal k1 = getK(pointLeftBottom, pointRightTop);
        BigDecimal k2 = getK(pointLeftTop, pointRightBottom);
//        b=-ky-x
        BigDecimal b1 = k1.multiply(pointLeftBottom.getY()).multiply(BigDecimal.valueOf(-1)).subtract(pointLeftBottom.getX());
        BigDecimal b2 = k2.multiply(pointLeftTop.getY()).multiply(BigDecimal.valueOf(-1)).subtract(pointLeftTop.getX());
//        center_y=(b2-b1)/(k1-k2)
        BigDecimal center_y = (b2.subtract(b1)).divide(k1.subtract(k2), RoundingMode.FLOOR);
        BigDecimal center_x = BigDecimal.valueOf(-1).multiply(k1.multiply(center_y).add(b1));
//        center_x = center_x.setScale(3, RoundingMode.UP);
        return new Point(center_x, center_y);
    }

    /**
     * @param pointLeftBottom  顶点
     * @param pointLeftTop     顶点
     * @param pointRightTop    顶点
     * @param pointRightBottom 顶点
     * @param edgeDistance     边距
     * @param stp              步长
     * @param b                beta
     */

    //    求边缘点
    public static EdgePoints getEdgePoint(Point pointLeftBottom, Point pointLeftTop, Point pointRightTop, Point pointRightBottom, BigDecimal edgeDistance, BigDecimal stp, double b) {
        topRowPoints.clear();
        bottomRowPoints.clear();
        leftColumnPoints.clear();
        rightColumnPoints.clear();
        Point.edgeDistance = edgeDistance;
        step = stp;
        xShadow = getXShadow(Point.edgeDistance, b);
        yShadow = getYShadow(Point.edgeDistance, b);
//        步长在x、y轴上的分解
        xShadow1 = getXShadow(step, b);
        yShadow1 = getYShadow(step, b);

        rowNum = rowNum(pointLeftBottom, pointLeftTop);
        int columnNum = columnNum(pointLeftBottom, pointRightBottom);
        if (a == 90) {
            //            对应摆正的情况  原点是p2 k=0
            Point pTop = new Point(pointLeftTop.getX().add(Point.edgeDistance), pointLeftTop.getY());
            Point firstTopEdgePoint = new Point(pTop.getX(), pTop.getY().add(Point.edgeDistance));
            Point firstBottomEdgePoint = new Point(pointLeftBottom.getX().subtract(Point.edgeDistance), pointLeftBottom.getY().add(Point.edgeDistance));
            topRowPoints.add(firstTopEdgePoint);
            bottomRowPoints.add(firstBottomEdgePoint);
            getRowPoints_AEqualTo90(columnNum, firstTopEdgePoint, topRowPoints);
            getRowPoints_AEqualTo90(columnNum, firstBottomEdgePoint, bottomRowPoints);
            Point lastTopPoint = new Point(pointRightTop.getX().add(Point.edgeDistance), pointRightTop.getY().subtract(Point.edgeDistance));
            Point lastBottomPoint = new Point(pointRightBottom.getX().subtract(Point.edgeDistance), pointRightBottom.getY().subtract(Point.edgeDistance));
            topRowPoints.add(lastTopPoint);
            bottomRowPoints.add(lastBottomPoint);
            getColumnPoints_AEqualTo90(rowNum, topRowPoints.get(0), leftColumnPoints);
            getColumnPoints_AEqualTo90(rowNum, topRowPoints.get(topRowPoints.size() - 1), rightColumnPoints);
        }
        if (a > 90) {
            Point pTop = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().add(yShadow));
            Point firstTopEdgePoint = new Point(pTop.getX().subtract(yShadow), pTop.getY().add(xShadow));
            Point pBottom = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().subtract(yShadow));
            Point firstBottomEdgePoint = new Point(pBottom.getX().subtract(yShadow), pBottom.getY().add(xShadow));
            topRowPoints.add(firstTopEdgePoint);
            bottomRowPoints.add(firstBottomEdgePoint);

            getRowPoints_AMoreThan90(columnNum, firstTopEdgePoint, topRowPoints);
            getRowPoints_AMoreThan90(columnNum, firstBottomEdgePoint, bottomRowPoints);

            getColumnPoints_AMoreThan90(rowNum, topRowPoints.get(0), leftColumnPoints);
            getColumnPoints_AMoreThan90(rowNum, topRowPoints.get(topRowPoints.size() - 1), rightColumnPoints);

            pointsFilter_AMoreThan90(pointRightTop, pointRightBottom, b);
        }
        if (a < 90) {
            // 求与p2p3斜率相同的直线上的点
            // 求出第一个点
            Point pTop = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().subtract(yShadow));
            Point firstTopEdgePoint = new Point(pTop.getX().add(yShadow), pTop.getY().add(xShadow));
            Point pBottom = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().add(yShadow));
            Point firstBottomEdgePoint = new Point(pBottom.getX().add(yShadow), pBottom.getY().add(xShadow));
            topRowPoints.add(firstTopEdgePoint);
            bottomRowPoints.add(firstBottomEdgePoint);
            getRowPoints_ALessThan90(columnNum, firstTopEdgePoint, topRowPoints);
            getRowPoints_ALessThan90(columnNum, firstBottomEdgePoint, bottomRowPoints);
//            求与p1p2斜率相同的直线上的点,除了左/右上角边第一个点,最后一个是左/右下角的点
            getColumnPoints_ALessThan90(rowNum, firstTopEdgePoint, leftColumnPoints);
            getColumnPoints_ALessThan90(rowNum, topRowPoints.get(topRowPoints.size() - 1), rightColumnPoints);
//          过滤点
            pointsFilter_ALessThan90(pointRightTop, pointRightBottom, b);
        }
        return new EdgePoints(topRowPoints, bottomRowPoints, leftColumnPoints, rightColumnPoints);
    }

    /**
     * @method rowNum  在横向的头尾2个边缘点之间的点的个数
     */
    public static int rowNum(Point p1, Point p2) {
        //       如果 p1p2 平行于 y轴
        BigDecimal distance = null;
        BigDecimal x1 = p1.getX();
        BigDecimal x2 = p2.getX();

        BigDecimal y1 = p1.getY();
        BigDecimal y2 = p2.getY();

        if (x1.compareTo(x2) == 0) {
            distance = y1.subtract(y2).abs();
        }
        if (x1.compareTo(x2) != 0) {
            distance = pythagoreanTheorem(x1, x2, y1, y2);
        }
        assert distance != null;
        BigDecimal num = (distance.subtract(edgeDistance.multiply(BigDecimal.valueOf(2))).divide(step, 10, RoundingMode.UP));
        //        做除法的结果是整数 :
        if (num.compareTo(distance.divide(step, 2, RoundingMode.UP)) == 0) {
            num = num.subtract(BigDecimal.valueOf(1));
        }
        return num.intValue();
    }

    /**
     * @method columnNum  在竖向的头尾2个边缘点之间的点的个数
     */
    public static int columnNum(Point p1, Point p4) {
        BigDecimal distance = null;
        BigDecimal x1 = p1.getX();
        BigDecimal x2 = p4.getX();

        BigDecimal y1 = p1.getY();
        BigDecimal y2 = p4.getY();

        if (y1.compareTo(y2) == 0) {
            distance = x2.subtract(x1).abs();
        }
        if (y1.compareTo(y2) != 0) {
            distance = pythagoreanTheorem(x1, x2, y1, y2);

        }
        assert distance != null;
        BigDecimal num = (distance.subtract(edgeDistance.multiply(BigDecimal.valueOf(2))).divide(step, 0, RoundingMode.UP));
        //        做除法的结果是整数 :
        if (num.compareTo(distance.divide(step, 2, RoundingMode.UP)) == 0) {
            num = num.subtract(BigDecimal.valueOf(1));
        }
        return num.intValue();
    }

    /**
     * pythagoreanTheorem 勾股定理 求出斜边的长度
     *
     * @param x1 p1的x坐标
     * @param x2 p2的x坐标
     * @param y1 p1的y坐标
     * @param y2 p2的y坐标
     */
    public static BigDecimal pythagoreanTheorem(BigDecimal x1, BigDecimal x2, BigDecimal y1, BigDecimal y2) {
        double X = Double.parseDouble(String.valueOf(x1.subtract(x2).abs()));
        double Y = Double.parseDouble(String.valueOf(y1.subtract(y2).abs()));
        return BigDecimal.valueOf(Math.sqrt(Math.pow(X, 2) + Math.pow(Y, 2)));
    }

    /**
     * @param columnNum
     * @param firstPoint 第一个边缘点
     * @param rowPoints  存横向边缘点的数组
     * @method getRowPoints_ALessThan90 求Alpha小于90度时的横向边缘点
     */
    public static void getRowPoints_ALessThan90(int columnNum, Point firstPoint, List<Point> rowPoints) {
        for (int i = 1; i < columnNum; i++) {
            Point topOrBottomEdge = new Point(firstPoint.getX().add(yShadow1.multiply(BigDecimal.valueOf(i))), firstPoint.getY().add(xShadow1.multiply(BigDecimal.valueOf(i))));
            rowPoints.add(topOrBottomEdge);
        }
    }

    /**
     * @param rowNum
     * @param firstPoint   第一个边缘点
     * @param columnPoints 存竖向边缘点的数组
     * @method getColumnPoints_ALessThan90 求Alpha小于90度时的竖向边缘点
     */
    public static void getColumnPoints_ALessThan90(int rowNum, Point firstPoint, List<Point> columnPoints) {
        for (int j = 1; j <= rowNum; j++) {
            BigDecimal formulaX = firstPoint.getX().add(xShadow1.multiply(BigDecimal.valueOf(j)));
            BigDecimal formulaY = firstPoint.getY().subtract(yShadow1.multiply(BigDecimal.valueOf(j)));
            Point leftOrRightEdge = new Point(formulaX, formulaY);
            columnPoints.add(leftOrRightEdge);
        }
    }

    public static void getRowPoints_AMoreThan90(int columnNum, Point firstPoint, List<Point> rowPoints) {
        for (int i = 1; i < columnNum; i++) {
            Point topOrBottomEdge = new Point(firstPoint.getX().subtract(yShadow1.multiply(BigDecimal.valueOf(i))), firstPoint.getY().add(xShadow1.multiply(BigDecimal.valueOf(i))));
            rowPoints.add(topOrBottomEdge);
        }
    }

    public static void getColumnPoints_AMoreThan90(int rowNum, Point firstPoint, List<Point> columnPoints) {
        for (int j = 1; j <= rowNum; j++) {
            BigDecimal formulaX = firstPoint.getX().add(xShadow1.multiply(BigDecimal.valueOf(j)));
            BigDecimal formulaY = firstPoint.getY().add(yShadow1.multiply(BigDecimal.valueOf(j)));
            Point leftOrRightEdge = new Point(formulaX, formulaY);
            columnPoints.add(leftOrRightEdge);
        }
    }

    public static void getRowPoints_AEqualTo90(int columnNum, Point firstPoint, List<Point> rowPoints) {
        for (int i = 1; i < columnNum; i++) {
            Point topOrBottomEdge = new Point(firstPoint.getX(), firstPoint.getY().add(step.multiply(BigDecimal.valueOf(i))));
            rowPoints.add(topOrBottomEdge);
        }
    }

    public static void getColumnPoints_AEqualTo90(int rowNum, Point firstPoint, List<Point> columnPoints) {
        for (int j = 1; j <= rowNum; j++) {
            Point leftOrRightEdge = new Point(firstPoint.getX().add(step.multiply(BigDecimal.valueOf(j))), firstPoint.getY());
            columnPoints.add(leftOrRightEdge);
        }
    }

    /**
     * @param pointRightTop
     * @param pointRightBottom
     * @param b
     * @method pointsFilter_ALessThan90 筛选Alpha小于90度时的边缘点
     */
    public static void pointsFilter_ALessThan90(Point pointRightTop, Point pointRightBottom, double b) {
//pointOnLastVerticalEdgeLine:右边竖直边缘线上的点
        pointOnLastVerticalEdgeLine = new Point(pointRightTop.getX().subtract(yShadow), pointRightTop.getY().subtract(xShadow));
//      竖直边缘线的直线方程上的常量
        BigDecimal px = pointOnLastVerticalEdgeLine.getX();
        BigDecimal py = pointOnLastVerticalEdgeLine.getY();


//         5mm 在x轴和y轴上的投影
        BigDecimal little_x = getXShadow(BigDecimal.valueOf(5), b);
        BigDecimal little_y = getYShadow(BigDecimal.valueOf(5), b);
//        System.out.println("little_x:" + little_x + "little_y:" + little_y);
        //      竖直边缘线左边和右边5mm的直线方程上的点

        Point pointOnLeftOfLine = new Point(px.subtract(little_y), py.subtract(little_x));

        BigDecimal b3_Left = pointOnLeftOfLine.getX().multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(pointOnLeftOfLine.getY()));

//        斜率相同,在竖直边缘线右边的线的直线方程中的b<b3    lastPoint:在屏幕范围内能求得的最后的点
        Point lastPoint = new Point(topRowPoints.get(topRowPoints.size() - 1).getX(), topRowPoints.get(topRowPoints.size() - 1).getY());
        BigDecimal b4 = lastPoint.getX().multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(lastPoint.getY()));
//        System.out.println("b4:" + b4);
        judgeVerticalPointPosition_ALessThan90(b3_Left, b4, pointRightBottom);
    }

    /**
     * @param b3_Left b3_Left为边缘线左侧5mm的线的截距
     * @param b4      b4为所求得的边缘点构成的线的截距
     * @param p4      右下角的顶点
     * @method judgeVerticalPointPosition_ALessThan90 判断Alpha小于90度时求得的边缘点是否在边缘线左侧5mm之内
     */
    public static void judgeVerticalPointPosition_ALessThan90(BigDecimal b3_Left, BigDecimal b4, Point p4) {
        //  点在线1的左边
        if (b4.compareTo(b3_Left) > 0) {
            rightColumnPoints.clear();
            // 补点
            addPoints_ALessThan90(p4);
        }

    }

    /**
     * @param pointRightBottom
     * @method addPoints_ALessThan90  Alpha小于90度时 求得的边缘线在边缘线左侧5mm以左,补充边缘线
     */
    public static void addPoints_ALessThan90(Point pointRightBottom) {
        Point lastTopEdgePoint = new Point(pointOnLastVerticalEdgeLine.getX().add(xShadow), pointOnLastVerticalEdgeLine.getY().subtract(yShadow));
        topRowPoints.add(lastTopEdgePoint);
        getColumnPoints_ALessThan90(rowNum, new Point(lastTopEdgePoint.getX(), lastTopEdgePoint.getY()), rightColumnPoints);
        Point lastBottomEdgePoint = new Point(pointRightBottom.getX().subtract(xShadow).subtract(yShadow), pointRightBottom.getY().add(yShadow).subtract(xShadow));
        bottomRowPoints.add(lastBottomEdgePoint);
    }

    public static void pointsFilter_AMoreThan90(Point pointRightTop, Point pointRightBottom, double b) {
        pointOnLastVerticalEdgeLine = new Point(pointRightTop.getX().add(yShadow), pointRightTop.getY().subtract(xShadow));
        BigDecimal px = pointOnLastVerticalEdgeLine.getX();
        BigDecimal py = pointOnLastVerticalEdgeLine.getY();


        BigDecimal little_x = getXShadow(BigDecimal.valueOf(5), b);
        BigDecimal little_y = getYShadow(BigDecimal.valueOf(5), b);
        //      竖直边缘线左边和右边5mm的直线方程上的点

        Point pointOnLeftOfLine = new Point(px.add(little_y), py.subtract(little_x));

        BigDecimal b3_Left = pointOnLeftOfLine.getX().multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(pointOnLeftOfLine.getY()));

        //        斜率相同,在竖直边缘线右边的线的直线方程中的b<b3    lastPoint:在屏幕范围内能求得的最后的点
        Point lastPoint = new Point(topRowPoints.get(topRowPoints.size() - 1).getX(), topRowPoints.get(topRowPoints.size() - 1).getY());
        BigDecimal b4 = lastPoint.getX().multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(lastPoint.getY()));
//        System.out.println("b4:" + b4);
        judgeVerticalPointPosition_AMoreThan90(b3_Left, b4, pointRightBottom);

    }

    public static void judgeVerticalPointPosition_AMoreThan90(BigDecimal b3_Left, BigDecimal b4, Point pointRightBottom) {
        //点在线1的左边
        if (b4.compareTo(b3_Left) < 0) {
            rightColumnPoints.clear();
//            System.out.println("在左边");
            //补点
            addPoints_AMoreThan90(pointRightBottom);
        }
    }

    public static void addPoints_AMoreThan90(Point pointRightBottom) {
        Point lastTopEdgePoint = new Point(pointOnLastVerticalEdgeLine.getX().add(xShadow), pointOnLastVerticalEdgeLine.getY().add(yShadow));
        System.out.println("lastTopEdgePoint:" + lastTopEdgePoint);
        topRowPoints.add(lastTopEdgePoint);
        getColumnPoints_ALessThan90(rowNum, new Point(lastTopEdgePoint.getX(), lastTopEdgePoint.getY()), rightColumnPoints);
        Point lastBottomEdgePoint = new Point(pointRightBottom.getX().subtract(xShadow).add(yShadow), pointRightBottom.getY().subtract(yShadow).subtract(xShadow));
        bottomRowPoints.add(lastBottomEdgePoint);
    }
//获取点的屏幕坐标

    /**
     * @param screenWidth  屏幕宽度
     * @param screenHeight 屏幕高度
     * @param edgeDistance 边距
     * @param step         步长
     * @method getScreenPoints 获取边缘点在屏幕上的坐标
     */
    public static EdgePoints getScreenPoints(BigDecimal screenWidth, BigDecimal screenHeight, BigDecimal edgeDistance, BigDecimal step) {
        topScreenPoints.clear();
        bottomScreenPoints.clear();
        leftScreenPoints.clear();
        rightScreenPoints.clear();
        Point firstTopPoint = new Point(edgeDistance, edgeDistance);
        Point lastTopPoint = new Point(screenWidth.subtract(edgeDistance), edgeDistance);
        Point firstBottomPoint = new Point(edgeDistance, screenHeight.subtract(edgeDistance));
        Point lastBottomPoint = new Point(screenWidth.subtract(edgeDistance), screenHeight.subtract(edgeDistance));

        int cNum = screenWidth.subtract(edgeDistance.multiply(BigDecimal.valueOf(2))).divide(step, 10, RoundingMode.FLOOR).intValue();
        int rNum = screenHeight.subtract(edgeDistance.multiply(BigDecimal.valueOf(2))).divide(step, 10, RoundingMode.FLOOR).intValue();


        for (int i = 0; i <= cNum; i++) {
            BigDecimal add = edgeDistance.add(step.multiply(BigDecimal.valueOf(i)));
            topScreenPoints.add(new Point(add, firstTopPoint.getY()));
            bottomScreenPoints.add(new Point(add, firstBottomPoint.getY()));
        }
        for (int i = 0; i <= rNum; i++) {
            BigDecimal add = edgeDistance.add(step.multiply(BigDecimal.valueOf(i)));
            leftScreenPoints.add(new Point(firstTopPoint.getX(), add));
            rightScreenPoints.add(new Point(lastTopPoint.getX(), add));
        }
        topScreenPoints.add(lastTopPoint);
        bottomScreenPoints.add(lastBottomPoint);
        return new EdgePoints(topScreenPoints, bottomScreenPoints, leftScreenPoints, rightScreenPoints);
    }

    /**
     * 获取圆上用于Arc指令的4个点
     *
     * @param pivot  中心点
     * @param radius 半径
     */
//    public static  CirclePoints getCirclePoint(Point pivot, BigDecimal radius) {

    /// /        圆的参数方程
//        BigDecimal x = BigDecimal.valueOf(Math.sin(Math.toRadians(45))).multiply(radius);
//        BigDecimal y = BigDecimal.valueOf(Math.cos(Math.toRadians(45))).multiply(radius);
//        Point start1 = new Point(pivot.getX().add(x), pivot.getY().add(y));
//        Point end1 = new Point(pivot.getX().add(x), pivot.getY().subtract(y));
//        Point start2 = new Point(pivot.getX().subtract(x), pivot.getY().subtract(y));
//        Point end2 = new Point(pivot.getX().subtract(x), pivot.getY().add(y));
//        return new CirclePoints(start1,end1,start2,end2);
//    }
//
    public static Point[] getCirclePoint(Point pivot, double degree, BigDecimal radius) {
//        圆的参数方程

        int times = (int) (360 / degree);
        Point[] circlePoints = new Point[times];
        for (int i = 0; i < times; i++) {
            BigDecimal x = BigDecimal.valueOf(Math.sin(Math.toRadians(degree))).multiply(radius);
            BigDecimal y = BigDecimal.valueOf(Math.cos(Math.toRadians(degree))).multiply(radius);
            circlePoints[i] = new Point(pivot.getX().add(x), pivot.getY().add(y));
            System.out.println(circlePoints[i]);
        }
        return circlePoints;
    }


    /**
     * 获取屏幕上圆点的坐标
     *
     * @param screenWidth
     * @param screenHeight
     * @param radius       半径
     * @param degree       角度
     */
    public static List<Point> getScreenCirclePoints(BigDecimal screenWidth, BigDecimal screenHeight, BigDecimal radius, Double degree) {
        Point screenPivot = new Point(screenWidth.divide(BigDecimal.valueOf(2), 3, RoundingMode.FLOOR), screenHeight.divide(BigDecimal.valueOf(2), 3, RoundingMode.FLOOR));
/*  圆点坐标：(x0,y0) 半径：r 角度：a
    则圆上任一点为：（x1,y1）
    x1 = x0 + r * cos(a)
    y1 = y0 + r * sin(a)
*/
        List<Point> screenCirclePoints = new ArrayList<>();
        int times = Integer.parseInt(String.valueOf(BigDecimal.valueOf(360).divide(BigDecimal.valueOf(degree), 0, RoundingMode.FLOOR)));
        for (int i = 1; i <= times; i++) {
            BigDecimal x1 = screenPivot.getX().add(radius.multiply(BigDecimal.valueOf(Math.cos(Math.toRadians(degree * i)))));
            BigDecimal y1 = screenPivot.getY().add(radius.multiply(BigDecimal.valueOf(Math.sin(Math.toRadians(degree * i)))));
            screenCirclePoints.add(new Point(x1, y1));
        }
        return screenCirclePoints;
    }

    public static Point getRandomPoint(Point pointLeftBottom, Point pointLeftTop, Point pointRightTop, Point pointRightBottom) {
        BigDecimal xStart = null;
        BigDecimal k1 = null;
        BigDecimal k2 = null;
        BigDecimal randomX;
        BigDecimal randomY = null;
        BigDecimal xLimit = null;
        BigDecimal yLimit;
        BigDecimal b1 = null;
        BigDecimal b2 = null;
        BigDecimal b3 = null;
        BigDecimal b4 = null;
        System.out.println(pointLeftBottom);
        System.out.println(pointLeftTop);
        double a = getAlpha(pointLeftBottom, pointLeftTop);
        if (a > 90) {
            xLimit = pointLeftBottom.getX().subtract(pointRightTop.getX()).abs();
            xStart = pointRightTop.getX();
        }
        if (a < 90) {
            xLimit = pointRightBottom.getX().subtract(pointLeftTop.getX()).abs();
            xStart = pointLeftTop.getX();
        }
        if (a == 90) {
            xLimit = pointLeftTop.getX().subtract(pointLeftBottom.getX()).abs();
            xStart = pointLeftTop.getX();
        }
        if (a != 90) {
            k1 = getK(pointLeftBottom, pointLeftTop);
            k2 = getK(pointLeftTop, pointRightTop);
            //        -x=ky+b b=-x-ky
//        p1p2的截距
            b1 = BigDecimal.valueOf(-1).multiply(pointLeftBottom.getX().subtract(k1.multiply(BigDecimal.valueOf(-1).multiply(pointLeftBottom.getY()))));
//        p2p3的截距
            b2 = BigDecimal.valueOf(-1).multiply(pointLeftTop.getX().subtract(k2.multiply(BigDecimal.valueOf(-1).multiply(pointLeftTop.getY()))));
//        p1p4的截距
            b3 = BigDecimal.valueOf(-1).multiply(pointLeftBottom.getX().subtract(k2.multiply(BigDecimal.valueOf(-1).multiply(pointLeftBottom.getY()))));
//        p3p4的截距
            b4 = BigDecimal.valueOf(-1).multiply(pointRightTop.getX().subtract(k1.multiply(BigDecimal.valueOf(-1).multiply(pointRightTop.getY()))));

            System.out.println("b1:" + b1);
            System.out.println("b2:" + b2);
            System.out.println("b3:" + b3);
            System.out.println("b4:" + b4);
        }
        randomX = xStart.add(xLimit.multiply(BigDecimal.valueOf(Math.random())));
        randomX = randomX.setScale(3, RoundingMode.FLOOR);
        //            -x=ky+b  y=(-x-b)/k
        if (a < 90) {
            if (pointLeftBottom.getX().compareTo(pointRightTop.getX()) == 0) {
                if (randomX.compareTo(pointLeftBottom.getX()) == 0) {
                    yLimit = pointRightTop.getY().subtract(pointLeftBottom.getY());
                    randomY = pointLeftBottom.getY().add(yLimit.multiply(BigDecimal.valueOf(Math.random())));
                }
                if (randomX.compareTo(pointLeftBottom.getX()) < 0) {
                    randomY = getRandomY(randomX, b1, b2, k1, k2);
                }
                if (randomX.compareTo(pointLeftBottom.getX()) > 0) {
                    randomY = getRandomY(randomX, b3, b4, k2, k1);
                }
            }
            if (pointLeftBottom.getX().compareTo(pointRightTop.getX()) > 0) {
                if (randomX.compareTo(pointRightTop.getX()) < 0) {
                    randomY = getRandomY(randomX, b1, b2, k1, k2);
                }
                if ((randomX.compareTo(pointRightTop.getX()) > 0) && (randomX.compareTo(pointLeftBottom.getX()) < 0)) {
                    randomY = getRandomY(randomX, b1, b4, k1, k1);
                }
                if (randomX.compareTo(pointLeftBottom.getX()) > 0) {
                    randomY = getRandomY(randomX, b3, b4, k2, k1);
                }
            }
            if (pointLeftBottom.getX().compareTo(pointRightTop.getX()) < 0) {
                System.out.println("randomX:" + randomX);
                if (randomX.compareTo(pointLeftBottom.getX()) < 0) {
                    randomY = getRandomY(randomX, b1, b2, k1, k2);
                }
                if ((randomX.compareTo(pointLeftBottom.getX()) >= 0) && (randomX.compareTo(pointRightTop.getX()) <= 0)) {
                    randomY = getRandomY(randomX, b3, b2, k2, k2);
                }
                if (randomX.compareTo(pointRightTop.getX()) > 0) {
                    randomY = getRandomY(randomX, b3, b4, k2, k1);
                }
            }
        }
        if (a > 90) {
            if (pointLeftTop.getX().compareTo(pointRightBottom.getX()) == 0) {
                if (randomX.compareTo(pointLeftTop.getX()) == 0) {
                    yLimit = pointRightBottom.getY().subtract(pointLeftTop.getY());
                    randomY = pointLeftTop.getY().add(yLimit.multiply(BigDecimal.valueOf(Math.random())));
                }
                if (randomX.compareTo(pointLeftTop.getX()) < 0) {
                    randomY = getRandomY(randomX, b2, b4, k2, k1);
                }
                if (randomX.compareTo(pointLeftTop.getX()) > 0) {
                    randomY = getRandomY(randomX, b1, b3, k1, k2);
                }
            }
            if (pointLeftTop.getX().compareTo(pointRightBottom.getX()) > 0) {
                System.out.println("hello???");
                if (randomX.compareTo(pointRightBottom.getX()) < 0) {

                    randomY = getRandomY(randomX, b2, b4, k2, k1);
                }
                if ((randomX.compareTo(pointRightBottom.getX()) >= 0) && (randomX.compareTo(pointLeftTop.getX()) <= 0)) {
                    randomY = getRandomY(randomX, b2, b3, k2, k2);
                }
                if (randomX.compareTo(pointLeftTop.getX()) > 0) {

                    randomY = getRandomY(randomX, b1, b3, k1, k2);
                }
            }
            if (pointLeftTop.getX().compareTo(pointRightBottom.getX()) < 0) {
                if (randomX.compareTo(pointLeftTop.getX()) < 0) {
                    randomY = getRandomY(randomX, b2, b4, k2, k1);
                }
                if ((randomX.compareTo(pointLeftTop.getX()) >= 0) && (randomX.compareTo(pointRightBottom.getX()) <= 0)) {
                    randomY = getRandomY(randomX, b1, b4, k1, k1);
                }
                if (randomX.compareTo(pointRightBottom.getX()) > 0) {
                    randomY = getRandomY(randomX, b1, b3, k1, k2);
                }
            }
        }
        if (a == 90) {
            yLimit = pointRightTop.getY().subtract(pointLeftTop.getY());
            randomY = pointLeftTop.getY().add(yLimit.multiply(BigDecimal.valueOf(Math.random())));
            randomY = randomY.setScale(3, RoundingMode.FLOOR);
        }
        return new Point(randomX, randomY);
    }

    public static BigDecimal getRandomY(BigDecimal randomX, BigDecimal bLeft, BigDecimal bRight, BigDecimal kLeft, BigDecimal kRight) {
        BigDecimal yLeft = (BigDecimal.valueOf(-1).multiply(randomX).subtract(bLeft)).divide(kLeft, 3, RoundingMode.FLOOR);
        BigDecimal yRight = (BigDecimal.valueOf(-1).multiply(randomX).subtract(bRight)).divide(kRight, 3, RoundingMode.FLOOR);
        BigDecimal yLimit = yRight.subtract(yLeft);
        BigDecimal randomY = yLeft.add(yLimit.multiply(BigDecimal.valueOf(Math.random())));
        randomY = randomY.setScale(3, RoundingMode.FLOOR);
        return randomY;
    }

    /**
     * @param pointLeftBottom
     * @param pointLeftTop
     * @param pointRightTop
     * @param pointRightBottom
     * @param edgeDistance              边距
     * @param stp                       步长
     * @param b                         角度
     * @param upAndDownEntryDistance    上边和下边的进入距离
     * @param leftAndRightEntryDistance 左边和右边的进入距离
     * @return List<List < ?>>  :   第一个元素List<EntryPoints>记录边缘进入的开始点和结束点，用于机械臂的直线运动    第二个元素 List<List<Point[]>>记录开始点到停止点之间的点 ,用于求屏幕坐标
     * @method getPointOfEdgeEntry 获取边缘进入的开始点、停止点和开始点到停止点中间的点
     */
//    public static List<List<?>> getPointOfEdgeEntry(Point pointLeftBottom, Point pointLeftTop, Point pointRightTop, Point pointRightBottom, BigDecimal edgeDistance, BigDecimal stp, double b, BigDecimal upAndDownEntryDistance, BigDecimal leftAndRightEntryDistance, String request, BigDecimal splitDistance) {
//        List<List<?>> pointsOfEdgeEntry = new ArrayList<>();
//        List<Point> topStopPoints = new ArrayList<>();
//        List<Point> bottomStopPoints = new ArrayList<>();
//        List<Point> leftStopPoints = new ArrayList<>();
//        List<Point> rightStopPoints = new ArrayList<>();
//        List<Point> topEntryPoints = new ArrayList<>();
//        List<Point> bottomEntryPoints = new ArrayList<>();
//        List<Point> leftEntryPoints = new ArrayList<>();
//        List<Point> rightEntryPoints = new ArrayList<>();
////        topEntryPoints.clear();
////        bottomStopPoints.clear();
////        leftStopPoints.clear();
////        rightStopPoints.clear();
//        entryPointsList.clear();
//        Point.edgeDistance = edgeDistance;
//        step = stp;
//        xShadow = getXShadow(edgeDistance, b);
//        xShadow = getXShadow(edgeDistance, b);
//        yShadow = getYShadow(edgeDistance, b);
//        xShadow1 = getXShadow(step, b);
//        yShadow1 = getYShadow(step, b);
//        rowNum = rowNum(pointLeftBottom, pointLeftTop);
//        int columnNum = columnNum(pointLeftBottom, pointRightBottom);
//        if (!request.equals("ForPointsInLine")) {
//            pointsOfEdgeEntry.clear();
//        }
//
//        if (a == 90) {
//            Point firstTopEdgePoint = new Point(pointLeftTop.getX(), pointLeftTop.getY().add(Point.edgeDistance));
//            Point firstBottomEdgePoint = new Point(pointLeftBottom.getX(), pointLeftBottom.getY().add(Point.edgeDistance));
//            topEntryPoints.add(firstTopEdgePoint);
//            bottomEntryPoints.add(firstBottomEdgePoint);
//            getRowPoints_AEqualTo90(columnNum, firstTopEdgePoint, topEntryPoints);
//            getRowPoints_AEqualTo90(columnNum, firstBottomEdgePoint, bottomEntryPoints);
//
//            Point lastTopPoint = new Point(pointRightTop.getX(), pointRightTop.getY().subtract(Point.edgeDistance));
//            Point lastBottomPoint = new Point(pointRightBottom.getX(), pointRightBottom.getY().subtract(Point.edgeDistance));
//            topEntryPoints.add(lastTopPoint);
//            bottomEntryPoints.add(lastBottomPoint);
//
//            Point firstLeftPoint = new Point(pointLeftTop.getX().add(Point.edgeDistance), pointLeftTop.getY());
//            Point firstRightPoint = new Point(pointRightTop.getX().add(Point.edgeDistance), pointRightTop.getY());
//            leftEntryPoints.add(firstLeftPoint);
//            rightEntryPoints.add(firstRightPoint);
//
//            getColumnPoints_AEqualTo90(rowNum, firstLeftPoint, leftEntryPoints);
//            getColumnPoints_AEqualTo90(rowNum, firstRightPoint, rightEntryPoints);
//
//            Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(Point.edgeDistance), pointLeftBottom.getY());
//            Point lastRightPoint = new Point(pointRightBottom.getX().subtract(Point.edgeDistance), pointRightBottom.getY());
//            leftEntryPoints.add(lastLeftPoint);
//            rightEntryPoints.add(lastRightPoint);
//
//            for (Point entryPoint : topEntryPoints) {
//                topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistance), entryPoint.getY()));
//            }
//            for (Point entryPoint : bottomEntryPoints) {
//                bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistance), entryPoint.getY()));
//            }
//
//            for (Point entryPoint : leftEntryPoints) {
//                leftStopPoints.add(new Point(entryPoint.getX(), entryPoint.getY().add(leftAndRightEntryDistance)));
//            }
//            for (Point entryPoint : rightEntryPoints) {
//                rightStopPoints.add(new Point(entryPoint.getX(), entryPoint.getY().subtract(leftAndRightEntryDistance)));
//            }
//        }
//        if (a != 90) {
//            BigDecimal upAndDownEntryDistanceX = getXShadow(upAndDownEntryDistance, b);
//            BigDecimal upAndDownEntryDistanceY = getYShadow(upAndDownEntryDistance, b);
//            BigDecimal leftAndRightEntryDistanceX = getXShadow(leftAndRightEntryDistance, b);
//            BigDecimal leftAndRightEntryDistanceY = getYShadow(leftAndRightEntryDistance, b);
//            if (a > 90) {
//                Point firstTopEdgePoint = new Point(pointLeftTop.getX().subtract(yShadow), pointLeftTop.getY().add(xShadow));
//                Point firstBottomEdgePoint = new Point(pointLeftBottom.getX().subtract(yShadow), pointLeftBottom.getY().add(xShadow));
//                topEntryPoints.add(firstTopEdgePoint);
//                bottomEntryPoints.add(firstBottomEdgePoint);
//                getRowPoints_AMoreThan90(columnNum, firstTopEdgePoint, topEntryPoints);
//                getRowPoints_AMoreThan90(columnNum, firstBottomEdgePoint, bottomEntryPoints);
//
//                Point lastTopPoint = new Point(pointRightTop.getX().add(yShadow), pointRightTop.getY().subtract(xShadow));
//                Point lastBottomPoint = new Point(pointRightBottom.getX().add(yShadow), pointRightBottom.getY().subtract(xShadow));
//
//                topEntryPoints.add(lastTopPoint);
//                bottomEntryPoints.add(lastBottomPoint);
//
//                Point firstLeftPoint = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().add(yShadow));
//                Point firstRightPoint = new Point(pointRightTop.getX().add(xShadow), pointRightTop.getY().add(yShadow));
//                leftEntryPoints.add(firstLeftPoint);
//                rightEntryPoints.add(firstRightPoint);
//
//                getColumnPoints_AMoreThan90(rowNum, firstLeftPoint, leftEntryPoints);
//                getColumnPoints_AMoreThan90(rowNum, firstRightPoint, rightEntryPoints);
//                Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().subtract(yShadow));
//                Point lastRightPoint = new Point(pointRightBottom.getX().subtract(xShadow), pointRightBottom.getY().subtract(yShadow));
//                leftEntryPoints.add(lastLeftPoint);
//                rightEntryPoints.add(lastRightPoint);
//
//                for (Point entryPoint : topEntryPoints) {
//                    topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistanceX), entryPoint.getY().add(upAndDownEntryDistanceY)));
//                }
//                for (Point entryPoint : bottomEntryPoints) {
//                    bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistanceX), entryPoint.getY().subtract(upAndDownEntryDistanceY)));
//                }
//
//                for (Point entryPoint : leftEntryPoints) {
//                    leftStopPoints.add(new Point(entryPoint.getX().subtract(leftAndRightEntryDistanceY), entryPoint.getY().add(leftAndRightEntryDistanceX)));
//                }
//                for (Point entryPoint : rightEntryPoints) {
//                    rightStopPoints.add(new Point(entryPoint.getX().add(leftAndRightEntryDistanceY), entryPoint.getY().subtract(leftAndRightEntryDistanceX)));
//                }
//            }
//            if (a < 90) {
//                Point firstTopEdgePoint = new Point(pointLeftTop.getX().add(yShadow), pointLeftTop.getY().add(xShadow));
//                Point firstBottomEdgePoint = new Point(pointLeftBottom.getX().add(yShadow), pointLeftBottom.getY().add(xShadow));
//                topEntryPoints.add(firstTopEdgePoint);
//                bottomEntryPoints.add(firstBottomEdgePoint);
//
//                getRowPoints_ALessThan90(columnNum, firstTopEdgePoint, topEntryPoints);
//                getRowPoints_ALessThan90(columnNum, firstBottomEdgePoint, bottomEntryPoints);
//
//                Point lastTopPoint = new Point(pointRightTop.getX().subtract(yShadow), pointRightTop.getY().subtract(xShadow));
//                Point lastBottomPoint = new Point(pointRightBottom.getX().subtract(yShadow), pointRightBottom.getY().subtract(xShadow));
//                topEntryPoints.add(lastTopPoint);
//                bottomEntryPoints.add(lastBottomPoint);
//
//                Point firstLeftPoint = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().subtract(yShadow));
//                Point firstRightPoint = new Point(pointRightTop.getX().add(xShadow), pointRightTop.getY().subtract(yShadow));
//                leftEntryPoints.add(firstLeftPoint);
//                rightEntryPoints.add(firstRightPoint);
//
//                getColumnPoints_ALessThan90(rowNum, firstLeftPoint, leftEntryPoints);
//                getColumnPoints_ALessThan90(rowNum, firstRightPoint, rightEntryPoints);
//                Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().add(yShadow));
//                Point lastRightPoint = new Point(pointRightBottom.getX().subtract(xShadow), pointRightBottom.getY().add(yShadow));
//                leftEntryPoints.add(lastLeftPoint);
//                rightEntryPoints.add(lastRightPoint);
//
//                for (Point entryPoint : topEntryPoints) {
//                    topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistanceX), entryPoint.getY().subtract(upAndDownEntryDistanceY)));
//                }
//                for (Point entryPoint : bottomEntryPoints) {
//                    bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistanceX), entryPoint.getY().add(upAndDownEntryDistanceY)));
//                }
//                for (Point entryPoint : leftEntryPoints) {
//                    leftStopPoints.add(new Point(entryPoint.getX().add(leftAndRightEntryDistanceY), entryPoint.getY().add(leftAndRightEntryDistanceX)));
//                }
//                for (Point entryPoint : rightEntryPoints) {
//                    rightStopPoints.add(new Point(entryPoint.getX().subtract(leftAndRightEntryDistanceY), entryPoint.getY().subtract(leftAndRightEntryDistanceX)));
//                }
//
//            }
//        }
//        pointsOfEdgeEntry.add(entryPointsList);
//
////        System.out.println("topStopPoints:"+topStopPoints);
////        System.out.println("bottomStopPoints:"+bottomStopPoints);
////        System.out.println("leftStopPoints:"+leftStopPoints);
////        System.out.println("rightStopPoints:"+rightStopPoints);
//        for (int i = 0; i < topEntryPoints.size(); i++) {
//            entryPointsList.add(new EntryPoints(topEntryPoints.get(i), topStopPoints.get(i)));
//        }
//        for (int i = 0; i < bottomEntryPoints.size(); i++) {
//            entryPointsList.add(new EntryPoints(bottomEntryPoints.get(i), bottomStopPoints.get(i)));
//        }
//        for (int i = 0; i < leftEntryPoints.size(); i++) {
//            entryPointsList.add(new EntryPoints(leftEntryPoints.get(i), leftStopPoints.get(i)));
//        }
//        for (int i = 0; i < rightEntryPoints.size(); i++) {
//            entryPointsList.add(new EntryPoints(rightEntryPoints.get(i), rightStopPoints.get(i)));
//        }
//
//
//        if (request.equals("ForPointsInLine")) {
//            if (pointsOfEdgeEntry.size() == 2) {
//                pointsOfEdgeEntry.get(1).clear();
//            }
//            List<Point[]> pointsTopInScreen =new ArrayList<>();
//            for (int i = 0; i < topEntryPoints.size(); i++) {
//                Point[] splitPoints=splitLine(topEntryPoints.get(i),topStopPoints.get(i),splitDistance);
//                pointsTopInScreen.add(splitPoints);
//            }
//            List<Point[]> pointsBottomInScreen=new ArrayList<>();
//            for (int i = 0; i < bottomEntryPoints.size(); i++) {
//                Point[] splitPoints=splitLine(bottomEntryPoints.get(i),bottomStopPoints.get(i),splitDistance);
//                pointsBottomInScreen.add(splitPoints);
//            }
//            List<Point[]> pointsLeftInScreen=new ArrayList<>();
//            for (int i = 0; i < leftEntryPoints.size(); i++) {
//                Point[] splitPoints=splitLine(leftEntryPoints.get(i),leftStopPoints.get(i),splitDistance);
//                pointsLeftInScreen.add(splitPoints);
//            }
//            List<Point[]> pointsRightInScreen=new ArrayList<>();
//            for (int i = 0; i < rightEntryPoints.size(); i++) {
//                Point[] splitPoints=splitLine(rightEntryPoints.get(i),rightStopPoints.get(i),splitDistance);
//                pointsRightInScreen.add(splitPoints);
//            }
//            List<List<Point[]>> allPointsInEntryLineList = new ArrayList<>();
//            allPointsInEntryLineList.add(pointsTopInScreen);
//            allPointsInEntryLineList.add(pointsBottomInScreen);
//            allPointsInEntryLineList.add(pointsLeftInScreen);
//            allPointsInEntryLineList.add(pointsRightInScreen);
//            pointsOfEdgeEntry.add(allPointsInEntryLineList);
//        }
//        return pointsOfEdgeEntry;

    /// /        return new EntryPoints(topEntryPoints,bottomEntryPoints,leftEntryPoints,rightEntryPoints,topStopPoints,bottomStopPoints,leftStopPoints,rightStopPoints);
//    }
    public static List<List<Point[]>> getPointOfEdgeEntry(Point pointLeftBottom, Point pointLeftTop, Point pointRightTop, Point pointRightBottom, BigDecimal edgeDistance, BigDecimal stp, double b, BigDecimal upAndDownEntryDistance, BigDecimal leftAndRightEntryDistance, BigDecimal splitDistance) {
//        List<List<?>> pointsOfEdgeEntry = new ArrayList<>();
        List<Point> topStopPoints = new ArrayList<>();
        List<Point> bottomStopPoints = new ArrayList<>();
        List<Point> leftStopPoints = new ArrayList<>();
        List<Point> rightStopPoints = new ArrayList<>();
        List<Point> topEntryPoints = new ArrayList<>();
        List<Point> bottomEntryPoints = new ArrayList<>();
        List<Point> leftEntryPoints = new ArrayList<>();
        List<Point> rightEntryPoints = new ArrayList<>();
//        topEntryPoints.clear();
//        bottomStopPoints.clear();
//        leftStopPoints.clear();
//        rightStopPoints.clear();
        entryPointsList.clear();
        Point.edgeDistance = edgeDistance;
        step = stp;
        xShadow = getXShadow(edgeDistance, b);
        xShadow = getXShadow(edgeDistance, b);
        yShadow = getYShadow(edgeDistance, b);
        xShadow1 = getXShadow(step, b);
        yShadow1 = getYShadow(step, b);
        rowNum = rowNum(pointLeftBottom, pointLeftTop);
        int columnNum = columnNum(pointLeftBottom, pointRightBottom);
//        if (!request.equals("ForPointsInLine")) {
//            pointsOfEdgeEntry.clear();
//        }

        if (a == 90) {
            Point firstTopEdgePoint = new Point(pointLeftTop.getX(), pointLeftTop.getY().add(Point.edgeDistance));
            Point firstBottomEdgePoint = new Point(pointLeftBottom.getX(), pointLeftBottom.getY().add(Point.edgeDistance));
            topEntryPoints.add(firstTopEdgePoint);
            bottomEntryPoints.add(firstBottomEdgePoint);
            getRowPoints_AEqualTo90(columnNum, firstTopEdgePoint, topEntryPoints);
            getRowPoints_AEqualTo90(columnNum, firstBottomEdgePoint, bottomEntryPoints);

            Point lastTopPoint = new Point(pointRightTop.getX(), pointRightTop.getY().subtract(Point.edgeDistance));
            Point lastBottomPoint = new Point(pointRightBottom.getX(), pointRightBottom.getY().subtract(Point.edgeDistance));
            topEntryPoints.add(lastTopPoint);
            bottomEntryPoints.add(lastBottomPoint);

            Point firstLeftPoint = new Point(pointLeftTop.getX().add(Point.edgeDistance), pointLeftTop.getY());
            Point firstRightPoint = new Point(pointRightTop.getX().add(Point.edgeDistance), pointRightTop.getY());
            leftEntryPoints.add(firstLeftPoint);
            rightEntryPoints.add(firstRightPoint);

            getColumnPoints_AEqualTo90(rowNum, firstLeftPoint, leftEntryPoints);
            getColumnPoints_AEqualTo90(rowNum, firstRightPoint, rightEntryPoints);

            Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(Point.edgeDistance), pointLeftBottom.getY());
            Point lastRightPoint = new Point(pointRightBottom.getX().subtract(Point.edgeDistance), pointRightBottom.getY());
            leftEntryPoints.add(lastLeftPoint);
            rightEntryPoints.add(lastRightPoint);

            for (Point entryPoint : topEntryPoints) {
                topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistance), entryPoint.getY()));
            }
            for (Point entryPoint : bottomEntryPoints) {
                bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistance), entryPoint.getY()));
            }

            for (Point entryPoint : leftEntryPoints) {
                leftStopPoints.add(new Point(entryPoint.getX(), entryPoint.getY().add(leftAndRightEntryDistance)));
            }
            for (Point entryPoint : rightEntryPoints) {
                rightStopPoints.add(new Point(entryPoint.getX(), entryPoint.getY().subtract(leftAndRightEntryDistance)));
            }
        }
        if (a != 90) {
            BigDecimal upAndDownEntryDistanceX = getXShadow(upAndDownEntryDistance, b);
            BigDecimal upAndDownEntryDistanceY = getYShadow(upAndDownEntryDistance, b);
            BigDecimal leftAndRightEntryDistanceX = getXShadow(leftAndRightEntryDistance, b);
            BigDecimal leftAndRightEntryDistanceY = getYShadow(leftAndRightEntryDistance, b);
            if (a > 90) {
                Point firstTopEdgePoint = new Point(pointLeftTop.getX().subtract(yShadow), pointLeftTop.getY().add(xShadow));
                Point firstBottomEdgePoint = new Point(pointLeftBottom.getX().subtract(yShadow), pointLeftBottom.getY().add(xShadow));
                topEntryPoints.add(firstTopEdgePoint);
                bottomEntryPoints.add(firstBottomEdgePoint);
                getRowPoints_AMoreThan90(columnNum, firstTopEdgePoint, topEntryPoints);
                getRowPoints_AMoreThan90(columnNum, firstBottomEdgePoint, bottomEntryPoints);

                Point lastTopPoint = new Point(pointRightTop.getX().add(yShadow), pointRightTop.getY().subtract(xShadow));
                Point lastBottomPoint = new Point(pointRightBottom.getX().add(yShadow), pointRightBottom.getY().subtract(xShadow));

                topEntryPoints.add(lastTopPoint);
                bottomEntryPoints.add(lastBottomPoint);

                Point firstLeftPoint = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().add(yShadow));
                Point firstRightPoint = new Point(pointRightTop.getX().add(xShadow), pointRightTop.getY().add(yShadow));
                leftEntryPoints.add(firstLeftPoint);
                rightEntryPoints.add(firstRightPoint);

                getColumnPoints_AMoreThan90(rowNum, firstLeftPoint, leftEntryPoints);
                getColumnPoints_AMoreThan90(rowNum, firstRightPoint, rightEntryPoints);
                Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().subtract(yShadow));
                Point lastRightPoint = new Point(pointRightBottom.getX().subtract(xShadow), pointRightBottom.getY().subtract(yShadow));
                leftEntryPoints.add(lastLeftPoint);
                rightEntryPoints.add(lastRightPoint);

                for (Point entryPoint : topEntryPoints) {
                    topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistanceX), entryPoint.getY().add(upAndDownEntryDistanceY)));
                }
                for (Point entryPoint : bottomEntryPoints) {
                    bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistanceX), entryPoint.getY().subtract(upAndDownEntryDistanceY)));
                }

                for (Point entryPoint : leftEntryPoints) {
                    leftStopPoints.add(new Point(entryPoint.getX().subtract(leftAndRightEntryDistanceY), entryPoint.getY().add(leftAndRightEntryDistanceX)));
                }
                for (Point entryPoint : rightEntryPoints) {
                    rightStopPoints.add(new Point(entryPoint.getX().add(leftAndRightEntryDistanceY), entryPoint.getY().subtract(leftAndRightEntryDistanceX)));
                }
            }
            if (a < 90) {
                Point firstTopEdgePoint = new Point(pointLeftTop.getX().add(yShadow), pointLeftTop.getY().add(xShadow));
                Point firstBottomEdgePoint = new Point(pointLeftBottom.getX().add(yShadow), pointLeftBottom.getY().add(xShadow));
                topEntryPoints.add(firstTopEdgePoint);
                bottomEntryPoints.add(firstBottomEdgePoint);

                getRowPoints_ALessThan90(columnNum, firstTopEdgePoint, topEntryPoints);
                getRowPoints_ALessThan90(columnNum, firstBottomEdgePoint, bottomEntryPoints);

                Point lastTopPoint = new Point(pointRightTop.getX().subtract(yShadow), pointRightTop.getY().subtract(xShadow));
                Point lastBottomPoint = new Point(pointRightBottom.getX().subtract(yShadow), pointRightBottom.getY().subtract(xShadow));
                topEntryPoints.add(lastTopPoint);
                bottomEntryPoints.add(lastBottomPoint);

                Point firstLeftPoint = new Point(pointLeftTop.getX().add(xShadow), pointLeftTop.getY().subtract(yShadow));
                Point firstRightPoint = new Point(pointRightTop.getX().add(xShadow), pointRightTop.getY().subtract(yShadow));
                leftEntryPoints.add(firstLeftPoint);
                rightEntryPoints.add(firstRightPoint);

                getColumnPoints_ALessThan90(rowNum, firstLeftPoint, leftEntryPoints);
                getColumnPoints_ALessThan90(rowNum, firstRightPoint, rightEntryPoints);
                Point lastLeftPoint = new Point(pointLeftBottom.getX().subtract(xShadow), pointLeftBottom.getY().add(yShadow));
                Point lastRightPoint = new Point(pointRightBottom.getX().subtract(xShadow), pointRightBottom.getY().add(yShadow));
                leftEntryPoints.add(lastLeftPoint);
                rightEntryPoints.add(lastRightPoint);

                for (Point entryPoint : topEntryPoints) {
                    topStopPoints.add(new Point(entryPoint.getX().add(upAndDownEntryDistanceX), entryPoint.getY().subtract(upAndDownEntryDistanceY)));
                }
                for (Point entryPoint : bottomEntryPoints) {
                    bottomStopPoints.add(new Point(entryPoint.getX().subtract(upAndDownEntryDistanceX), entryPoint.getY().add(upAndDownEntryDistanceY)));
                }
                for (Point entryPoint : leftEntryPoints) {
                    leftStopPoints.add(new Point(entryPoint.getX().add(leftAndRightEntryDistanceY), entryPoint.getY().add(leftAndRightEntryDistanceX)));
                }
                for (Point entryPoint : rightEntryPoints) {
                    rightStopPoints.add(new Point(entryPoint.getX().subtract(leftAndRightEntryDistanceY), entryPoint.getY().subtract(leftAndRightEntryDistanceX)));
                }

            }
        }
//        pointsOfEdgeEntry.add(entryPointsList);

//        System.out.println("topStopPoints:"+topStopPoints);
//        System.out.println("bottomStopPoints:"+bottomStopPoints);
//        System.out.println("leftStopPoints:"+leftStopPoints);
//        System.out.println("rightStopPoints:"+rightStopPoints);
        for (int i = 0; i < topEntryPoints.size(); i++) {
            entryPointsList.add(new EntryPoints(topEntryPoints.get(i), topStopPoints.get(i)));
        }
        for (int i = 0; i < bottomEntryPoints.size(); i++) {
            entryPointsList.add(new EntryPoints(bottomEntryPoints.get(i), bottomStopPoints.get(i)));
        }
        for (int i = 0; i < leftEntryPoints.size(); i++) {
            entryPointsList.add(new EntryPoints(leftEntryPoints.get(i), leftStopPoints.get(i)));
        }
        for (int i = 0; i < rightEntryPoints.size(); i++) {
            entryPointsList.add(new EntryPoints(rightEntryPoints.get(i), rightStopPoints.get(i)));
        }

        List<Point[]> pointsTopInScreen = new ArrayList<>();
        for (int i = 0; i < topEntryPoints.size(); i++) {
            Point[] splitPoints = splitLine(topEntryPoints.get(i), topStopPoints.get(i), splitDistance);
            pointsTopInScreen.add(splitPoints);
        }
        List<Point[]> pointsBottomInScreen = new ArrayList<>();
        for (int i = 0; i < bottomEntryPoints.size(); i++) {
            Point[] splitPoints = splitLine(bottomEntryPoints.get(i), bottomStopPoints.get(i), splitDistance);
            pointsBottomInScreen.add(splitPoints);
        }
        List<Point[]> pointsLeftInScreen = new ArrayList<>();
        for (int i = 0; i < leftEntryPoints.size(); i++) {
            Point[] splitPoints = splitLine(leftEntryPoints.get(i), leftStopPoints.get(i), splitDistance);
            pointsLeftInScreen.add(splitPoints);
        }
        List<Point[]> pointsRightInScreen = new ArrayList<>();
        for (int i = 0; i < rightEntryPoints.size(); i++) {
            Point[] splitPoints = splitLine(rightEntryPoints.get(i), rightStopPoints.get(i), splitDistance);
            pointsRightInScreen.add(splitPoints);
        }
        List<List<Point[]>> allPointsInEntryLineList = new ArrayList<>();
        allPointsInEntryLineList.add(pointsTopInScreen);
        allPointsInEntryLineList.add(pointsBottomInScreen);
        allPointsInEntryLineList.add(pointsLeftInScreen);
        allPointsInEntryLineList.add(pointsRightInScreen);

        return allPointsInEntryLineList;
    }


    /**
     * @param k     斜率
     * @param point 直线上的任意一点
     * @return
     * @method getConstantOfLinearEquation :求出直线方程中的常量b
     */
    public static BigDecimal getConstantOfLinearEquation(BigDecimal k, Point point) {
        BigDecimal px = point.getX();
        BigDecimal py = point.getY();
        return px.multiply(BigDecimal.valueOf(-1)).subtract(k.multiply(py));
    }

    public static Point calculateScreenPointByAnyRobotCoordinatePoint(Point point, Point pointLeftBottom, Point pointLeftTop, Point pointRightTop) {
        double a = getAlpha(pointLeftBottom, pointLeftTop);
        BigDecimal x = null;
        BigDecimal y = null;
        if (a == 90) {
            x = pointLeftTop.getX().subtract(point.getX()).abs();
            y = pointLeftTop.getY().subtract(point.getY()).abs();
        }
        if (a != 90) {
            BigDecimal kLeft = getK(pointLeftBottom, pointLeftTop);
            BigDecimal kRight = getK(pointLeftTop, pointRightTop);
            BigDecimal bLeft = getConstantOfLinearEquation(kLeft, point);
            BigDecimal bLeftEdge = getConstantOfLinearEquation(kLeft, pointLeftBottom);
            BigDecimal bRight = getConstantOfLinearEquation(kRight, point);
            BigDecimal bRightEdge = getConstantOfLinearEquation(kRight, pointRightTop);
            Point pointLeft = new Point();
            Point pointRight = new Point();
//            -x=ky+b
            pointLeft.setY(BigDecimal.valueOf(-1).multiply((bLeftEdge.subtract(bRight)).divide(kLeft.subtract(kRight), RoundingMode.FLOOR)));
            pointLeft.setX(BigDecimal.valueOf(-1).multiply(kLeft.multiply(pointLeft.getY()).add(bLeftEdge)));
//            System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
//            System.out.println("point" + point);
//            System.out.println(">>>" + pointLeft);

            pointRight.setY(BigDecimal.valueOf(-1).multiply((bLeft.subtract(bRightEdge)).divide(kLeft.subtract(kRight), RoundingMode.FLOOR)));
            pointRight.setX(BigDecimal.valueOf(-1).multiply(kRight.multiply(pointRight.getY()).add(bRightEdge)));
//            System.out.println(">>>" + pointRight);
//          p1p2边上的点和屏幕内任意一点的距离的分解
            y = pythagoreanTheorem(point.getX(), pointLeft.getX(), point.getY(), pointLeft.getY());
            x = pythagoreanTheorem(point.getX(), pointRight.getX(), point.getY(), pointRight.getY());
        }
        return new Point(x, y);
//        return new Point(y, x);
    }

    //    /**
//     * @method getPointsInEntryLine 获取机械臂从边缘进入的路线上的点的机械臂坐标
//     * @param position 边缘进入线在屏幕上的位置
//     * @param entryPoints 机械臂进入点
//     * @param stopPoints  机械臂结束点
//     * @param num  线上的点的个数
//     * @return
//     */
//    public static List<Point[]> getPointsInEntryLine(String position, List<Point> entryPoints, List<Point> stopPoints, BigDecimal num) {
//        List<Point[]> pointInEntryLine = new ArrayList<>();
//        for (int i = 0; i < entryPoints.size(); i++) {
//            BigDecimal yLimit = stopPoints.get(i).getY().subtract(entryPoints.get(i).getY()).abs();
//            BigDecimal xLimit = stopPoints.get(i).getX().subtract(entryPoints.get(i).getX()).abs();
////           在y轴上的取值限制在2个点之间 x=-（ky+b）
//            BigDecimal yStart = null;
//            BigDecimal xStart = null;
//            Point[] points = new Point[Integer.parseInt(num.add(BigDecimal.valueOf(1)).toString())];
////        往上拖
//            if (Objects.equals(position, "bottom")) {
////                验证！
//                if (a > 90) {
//                    yStart = stopPoints.get(i).getY();
//                    points[0] = stopPoints.get(i);
//                }
//                if (a < 90) {
//                    yStart = entryPoints.get(i).getY();
//                    points[0] = entryPoints.get(i);
//                }
//                if (a == 90) {
//                    xStart = stopPoints.get(i).getX();
//                    points[0] = stopPoints.get(i);
//                }
//            }
//
////        往下拖
//            if (Objects.equals(position, "top")) {
//                if (a > 90) {
//                    yStart = entryPoints.get(i).getY();
//                    points[0] = entryPoints.get(i);
//                }
//                if (a < 90) {
//                    yStart = stopPoints.get(i).getY();
//                    points[0] = stopPoints.get(i);
//                }
//                if (a == 90) {
//                    xStart = entryPoints.get(i).getX();
//                    points[0] = entryPoints.get(i);
//                }
//            }
////        往右拖
//            if (Objects.equals(position, "left")) {
//                yStart = entryPoints.get(i).getY();
//                points[0] = entryPoints.get(i);
//            }
////        往左拖
//            if (Objects.equals(position, "right")) {
//                yStart = stopPoints.get(i).getY();
//                points[0] = stopPoints.get(i);
//            }
////
//            BigDecimal interval;
//            BigDecimal xCoordinate;
//            BigDecimal yCoordinate;
//            for (int j = 0; j < Integer.parseInt(num.toString()); j++) {
//                if (a!=90) {
////                    interval
//                    interval = yLimit.divide(num, 20, RoundingMode.FLOOR);
//                    yCoordinate = yStart.add(interval.multiply(BigDecimal.valueOf(j + 1)));
////            yCoordinate=yCoordinate.setScale(3,RoundingMode.FLOOR);
////            -x=ky+b x=-(ky+b)
//                    BigDecimal k = getK(entryPoints.get(i), stopPoints.get(i));
//                    BigDecimal b = getConstantOfLinearEquation(k, entryPoints.get(i));
//                    xCoordinate = BigDecimal.valueOf(-1).multiply(k.multiply(yCoordinate).add(b));
//                    xCoordinate = xCoordinate.setScale(3, RoundingMode.FLOOR);
//                } else {
//                    if (Objects.equals(position, "top") || Objects.equals(position, "bottom")) {
//                        interval = xLimit.divide(num, 20, RoundingMode.FLOOR);
//                        yCoordinate = entryPoints.get(i).getY();
//                        xCoordinate = xStart.add(interval.multiply(BigDecimal.valueOf(j + 1)));
//                    } else {
//                        interval = yLimit.divide(num, 20, RoundingMode.FLOOR);
//                        yCoordinate = yStart.add(interval.multiply(BigDecimal.valueOf(j + 1)));
//                        xCoordinate = entryPoints.get(i).getX();
//                    }
//                }
//                points[j + 1] = new Point();
//                points[j + 1].setX(xCoordinate);
//                points[j + 1].setY(yCoordinate);
//            }
//            pointInEntryLine.add(points);
//        }
//        return pointInEntryLine;
//    }

    /**
     * @param startPoint
     * @param endPoint
     * @return
     * @method getLineTrend 获取线段的走向
     */
    public static String getLineTrend(Point startPoint, Point endPoint) {
        String trend = null;
        BigDecimal startX = startPoint.getX();
        BigDecimal startY = startPoint.getY();
        BigDecimal endX = endPoint.getX();
        BigDecimal endY = endPoint.getY();
        if (a < 90) {
            if (startX.compareTo(endX) > 0) {
                if (startY.compareTo(endY) < 0) trend = "toRightTop";
            }
            if (startX.compareTo(endX) < 0) {
                if (startY.compareTo(endY) > 0) trend = "toLeftBottom";
            }
        }
        if (a > 90) {
            if (startX.compareTo(endX) > 0) {
                if (startY.compareTo(endY) > 0) trend = "toLeftTop";
            }
            if (startX.compareTo(endX) < 0) {
                if (startY.compareTo(endY) < 0) trend = "toRightBottom";
            }
        }
        if (a == 90) {
            if (startX.compareTo(endX) < 0) {
                trend = "toBottom";
            } else {
                trend = "toTop";
            }
        }
        if (a == 0) {
            if (startY.compareTo(endY) < 0) {
                trend = "toRight";
            } else {
                trend = "toLeft";
            }
        }
        return trend;
    }

    /**
     * @param startPoint
     * @param endPoint
     * @param distance
     * @return
     * @method splitLine 分割线段
     */
    public static Point[] splitLine(Point startPoint, Point endPoint, BigDecimal distance) {
        double a = getAlpha(startPoint, endPoint);
        BigDecimal xLimit = endPoint.getX().subtract(startPoint.getX()).abs();
        Point[] points;
        if (a != 90 && a != 0) {
            double b = getBeta(a);
            BigDecimal xShadow = getXShadow(distance, b);
            BigDecimal yShadow = getYShadow(distance, b);
            String lineTrend = getLineTrend(startPoint, endPoint);
            int num = Integer.parseInt((xLimit.divide(xShadow, 0, RoundingMode.FLOOR).toString()));
            if (xLimit.divide(xShadow, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            points = new Point[num + 2];
            points[0] = startPoint;
            if (lineTrend.equals("toRightBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().add(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeftTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().subtract(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toRightTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().add(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeftBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(xShadow.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY().subtract(yShadow.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        } else if (a == 0) {
            BigDecimal yLimit = endPoint.getY().subtract(startPoint.getY()).abs();
            int num = Integer.parseInt(yLimit.divide(distance, 0, RoundingMode.FLOOR).toString());
            if (yLimit.divide(distance, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            points = new Point[num + 2];
            points[0] = startPoint;
            String lineTrend = getLineTrend(startPoint, endPoint);
            if (lineTrend.equals("toRight")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX();
                    BigDecimal yCoordinate = startPoint.getY().add(distance.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toLeft")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX();
                    BigDecimal yCoordinate = startPoint.getY().subtract(distance.multiply(BigDecimal.valueOf(i + 1)));
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        } else {
            //a=90
            int num = Integer.parseInt(xLimit.divide(distance, 0, RoundingMode.FLOOR).toString());
            if (xLimit.divide(distance, 3, RoundingMode.FLOOR).compareTo(BigDecimal.valueOf(num)) == 0) {
                num = num - 1;
            }
            String lineTrend = getLineTrend(startPoint, endPoint);
            points = new Point[num + 2];

            points[0] = startPoint;
            if (lineTrend.equals("toTop")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().subtract(distance.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY();
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            if (lineTrend.equals("toBottom")) {
                for (int i = 0; i < num; i++) {
                    BigDecimal xCoordinate = startPoint.getX().add(distance.multiply(BigDecimal.valueOf(i + 1)));
                    BigDecimal yCoordinate = startPoint.getY();
                    points[i + 1] = new Point();
                    points[i + 1].setX(xCoordinate);
                    points[i + 1].setY(yCoordinate);
                }
            }
            points[points.length - 1] = endPoint;
        }
//        System.out.println(Arrays.toString(points));

        return points;
    }
}

