package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.electric_relay.ElectricRelayContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;
import ui.service.PanelManager;
import ui.service.RelayControlService;

public class ElectricRelayTabPaneView extends DeviceTabPaneView {

    public ElectricRelayTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        ElectricRelayContainer electricRelayContainer = new ElectricRelayContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, electricRelayContainer);

        // 获取继电器控制服务并注册
        PanelManager.getInstance().registerRelayContainer(device.getAliasName(), electricRelayContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));

        // 从PanelManager单例中注销指定别名的继电器容器
        PanelManager.getInstance().unregisterRelayContainer(device.getAliasName());
    }
}