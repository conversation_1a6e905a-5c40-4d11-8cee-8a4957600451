package ui.layout.left.display.components.container.power.kikusui.pulse_edit;

import com.alibaba.fastjson2.JSON;
import common.constant.ResourceConstant;
import sdk.base.operation.Operation;
import sdk.constants.methods.DeviceMethods;
import ui.layout.left.display.components.container.power.kikusui.WavePulsePanel;
import ui.layout.left.display.components.container.power.kikusui.entity.KikusuiWavePulse;
import ui.layout.left.display.components.container.power.kikusui.entity.VoltageLevelResetPulse;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;

public class VoltageLevelResetPulsePanel extends WavePulsePanel {

    private final JLabel highVoltageLabel = new JLabel("起始电压(V):               ");
    private final JLabel lowVoltageLabel = new JLabel("最低电压(V):               ");
    private final JLabel lowPulseDurationLabel = new JLabel("低脉冲持续时间(ms):");
    private final JLabel highPulseDurationLabel = new JLabel("高脉冲持续时间(ms):");
    private JSpinner highVoltageSpinner;
    private JSpinner lowVoltageSpinner;
    private JSpinner lowPulseDurationSpinner;
    private JSpinner highPulseDurationSpinner;
    private JSplitPane splitPane;

    public VoltageLevelResetPulsePanel() {
        setLayout(new BorderLayout());
        createSpinner();
        add(assemblePanel(), BorderLayout.CENTER);
    }

    public JPanel assemblePanel() {
        JPanel assemblePanel = new JPanel(new BorderLayout());

//        JPanel leftPanel = new JPanel(new BorderLayout());
        JPanel leftPanel = new JPanel(new GridBagLayout());
        JPanel rightPanel = new JPanel(new GridBagLayout());

        Insets insets = new Insets(0, 5, 0, 0);
        int anchor = GridBagConstraints.CENTER;
        int fill = GridBagConstraints.CENTER;
        double weightY = 0;
        double weightX = 0;

        leftPanel.add(highVoltageLabel, new GridBagConstraints(0, 0, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(highVoltageSpinner, new GridBagConstraints(1, 0, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(lowVoltageLabel, new GridBagConstraints(0, 1, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(lowVoltageSpinner, new GridBagConstraints(1, 1, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(lowPulseDurationLabel, new GridBagConstraints(0, 2, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(lowPulseDurationSpinner, new GridBagConstraints(1, 2, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(highPulseDurationLabel, new GridBagConstraints(0, 3, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        leftPanel.add(highPulseDurationSpinner, new GridBagConstraints(1, 3, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));

        Icon icon = SwingUtil.getResourceAsImageIcon(ResourceConstant.DevicePictures.adsVoltageLevelResetPulsePicture);
        JLabel imgLabel = new JLabel();
        imgLabel.setIcon(icon);
        rightPanel.add(imgLabel, new GridBagConstraints(0, 0, 1, 1, weightX, weightY, anchor, fill, insets, 1, 1));
        splitPane = SwingUtil.getSplitPane(leftPanel, rightPanel);

        assemblePanel.add(getMemoryPanel(), BorderLayout.NORTH);
        assemblePanel.add(splitPane, BorderLayout.CENTER);
        setFont();
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                splitPane.setDividerLocation(0.5);
            }
        });
        return assemblePanel;
    }

    public void setFont() {
        Font font = new Font(null, Font.BOLD, 16);
        highVoltageLabel.setFont(font);
        lowVoltageLabel.setFont(font);
        lowPulseDurationLabel.setFont(font);
        highPulseDurationLabel.setFont(font);
    }

    public void createSpinner() {
        highVoltageSpinner = new JSpinner(new SpinnerNumberModel(12, -33, 33, 0.1));
        SwingUtil.setPreferredWidth(highVoltageSpinner, 150);
        lowVoltageSpinner = new JSpinner(new SpinnerNumberModel(0, -33, 33, 0.1));
        SwingUtil.setPreferredWidth(lowVoltageSpinner, 150);
        double MAX_TIME_SETTING = 1000000;
        lowPulseDurationSpinner = new JSpinner(new SpinnerNumberModel(300, 0, MAX_TIME_SETTING, 1));
        SwingUtil.setPreferredWidth(lowPulseDurationSpinner, 150);
        highPulseDurationSpinner = new JSpinner(new SpinnerNumberModel(5000, 0, MAX_TIME_SETTING, 1));
        SwingUtil.setPreferredWidth(highPulseDurationSpinner, 150);
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

    private void buildUI(KikusuiWavePulse kikusuiWavePulse) {
        VoltageLevelResetPulse wavePulse = (VoltageLevelResetPulse) kikusuiWavePulse;
        setMemoryPosition(wavePulse.getMemoryPosition());
        highVoltageSpinner.setValue(wavePulse.getHighVoltage());
        lowVoltageSpinner.setValue(wavePulse.getLowVoltage());

        highPulseDurationSpinner.setValue(wavePulse.getHighPulseDuration() * 1000);
        lowPulseDurationSpinner.setValue(wavePulse.getLowPulseDuration() * 1000);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) {
        VoltageLevelResetPulse wavePulse = JSON.to(VoltageLevelResetPulse.class, injectedOperation.getOperationObject());
        buildUI(wavePulse);
    }

    @Override
    public VoltageLevelResetPulse assembleOperationObject() {
        VoltageLevelResetPulse wavePulse = new VoltageLevelResetPulse();
        wavePulse.setMemoryPosition(getMemoryPosition());
        wavePulse.setHighVoltage((Double) highVoltageSpinner.getValue());
        wavePulse.setLowVoltage((Double) lowVoltageSpinner.getValue());

        wavePulse.setHighPulseDuration((Double) highPulseDurationSpinner.getValue() / 1000);
        wavePulse.setLowPulseDuration((Double) lowPulseDurationSpinner.getValue() / 1000);
        return wavePulse;
    }


    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationMethod(DeviceMethods.writeVoltageLevelResetPulse);
        injectedOperation.setOperationObject(assembleOperationObject());
        return injectedOperation;
    }
}
