package ui.layout.left.display.components.container.robot.ui;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import sdk.base.SseSession;
import sdk.base.operation.Operation;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.robot.FeedbackData;
import sdk.domain.robot.MoveEntity;
import sdk.domain.robot.RobotTrajectory;
import sdk.entity.RobotDevice;
import ui.base.BaseView;
import ui.callback.DeviceCallback;
import ui.config.json.devices.robot.RobotConfig;
import ui.model.MainModel;
import ui.model.app.AppObserver;

import javax.swing.*;
import javax.swing.event.AncestorEvent;
import javax.swing.event.AncestorListener;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 15:13
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
public class RobotJogOrMovePanel extends JPanel implements BaseView, DeviceCallback, AppObserver {

    private final JLabel errorMessageLabel;
    private final JButton addXButton;
    private final JButton addYButton;
    private final JButton addZButton;
    private final JButton addRButton;
    private final JButton minusXButton;
    private final JButton minusYButton;
    private final JButton minusZButton;
    private final JButton minusRButton;
    private final JLabel feedbackXLabel;
    private final JLabel feedbackYLabel;
    private final JLabel feedbackZLabel;
    private final JLabel feedbackRLabel;
    private final JLabel feedbackSlideRailLabel;
    private final JLabel robotModeLabel;
    private final JLabel userCoordinateLabel;
    private final JButton collectButton;
    private final RobotControlPanel robotControlPanel;
    private final JComboBox<String> jogModeComboBox;
    private final boolean isContainSlideRail;
    private final MainModel mainModel;
    private final RobotConfig robotConfig;
    //TODO：这个robot重连后可能会动态更新
    private final RobotDevice robotDevice;
    private boolean poseTransportAlive;
    private final SseSession sseSession;
    private boolean collecting;
    private final List<RobotTrajectory> trajectoryList = new ArrayList<>();

    public RobotJogOrMovePanel(RobotControlPanel robotControlPanel, MainModel mainModel, boolean isContainSlideRail, RobotConfig robotConfig) {
        sseSession = new SseSession();
        this.robotConfig = robotConfig;
        this.robotControlPanel = robotControlPanel;
        this.mainModel = mainModel;
        this.isContainSlideRail = isContainSlideRail;
        collecting = false;
        collectButton = new JButton("动作还原开始示教");
        robotDevice = (RobotDevice) robotControlPanel.getDeviceContainer().getDevice();
//        isAppAlive = true;
        poseTransportAlive = false;
        errorMessageLabel = new JLabel();
        addXButton = new JButton("X+");
        addYButton = new JButton("Y+");
        addZButton = new JButton("Z+");
        addRButton = new JButton("R+");

        minusXButton = new JButton("X-");
        minusYButton = new JButton("Y-");
        minusZButton = new JButton("Z-");
        minusRButton = new JButton("R-");

        feedbackXLabel = new JLabel("X:");
        feedbackYLabel = new JLabel("Y:");
        feedbackZLabel = new JLabel("Z:");
        feedbackRLabel = new JLabel("R:");

        robotModeLabel = new JLabel();
        userCoordinateLabel = new JLabel();
        feedbackSlideRailLabel = new JLabel();

        jogModeComboBox = new JComboBox<>();
        jogModeComboBox.addItem("粗调");
        jogModeComboBox.addItem("微调");

        createView();
        restoreView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void restoreView() {
        jogModeComboBox.setSelectedIndex(robotConfig.getParameters().getJogMode());
    }

    @Override
    public void registerModelObservers() {
        robotControlPanel.getDeviceContainer().addDeviceCallback(this);
        mainModel.getAppModel().registerObserver(this);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        addXButton.setEnabled(isDeviceConnected);
        addYButton.setEnabled(isDeviceConnected);
        addZButton.setEnabled(isDeviceConnected);
        addRButton.setEnabled(isDeviceConnected);

        minusXButton.setEnabled(isDeviceConnected);
        minusYButton.setEnabled(isDeviceConnected);
        minusZButton.setEnabled(isDeviceConnected);
        minusRButton.setEnabled(isDeviceConnected);

        if (isDeviceConnected) {
            startGetPoseThread();
        }

    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {

    }

    @Override
    public void deviceDisconnected(Device device) {

    }

    @Override
    public void createView() {
        setLayout(new BorderLayout(3, 3));
        JPanel jogModePanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        jogModePanel.add(new JLabel("点动模式："));
        jogModePanel.add(jogModeComboBox);
        jogModePanel.add(collectButton);
        add(jogModePanel, BorderLayout.NORTH);

        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        statusPanel.add(userCoordinateLabel);
        statusPanel.add(robotModeLabel);
        if (isContainSlideRail) {
            statusPanel.add(feedbackSlideRailLabel);
        }
        add(statusPanel, BorderLayout.CENTER);

        JPanel controlPanel = new JPanel();
        controlPanel.setLayout(new GridLayout(4, 3));
        controlPanel.add(minusXButton);
        controlPanel.add(feedbackXLabel);
        controlPanel.add(addXButton);

        controlPanel.add(minusYButton);
        controlPanel.add(feedbackYLabel);
        controlPanel.add(addYButton);

        controlPanel.add(minusZButton);
        controlPanel.add(feedbackZLabel);
        controlPanel.add(addZButton);

        controlPanel.add(minusRButton);
        controlPanel.add(feedbackRLabel);
        controlPanel.add(addRButton);

        Box controlBox = Box.createHorizontalBox();
        controlBox.add(errorMessageLabel);
        controlBox.add(Box.createHorizontalGlue());
        controlBox.add(controlPanel);
        controlBox.add(Box.createHorizontalGlue());
        add(controlBox, BorderLayout.SOUTH);

        feedbackXLabel.setHorizontalAlignment(SwingConstants.CENTER);
        feedbackYLabel.setHorizontalAlignment(SwingConstants.CENTER);
        feedbackZLabel.setHorizontalAlignment(SwingConstants.CENTER);
        feedbackRLabel.setHorizontalAlignment(SwingConstants.CENTER);

        setBorder(BorderFactory.createLineBorder(Color.black));

    }

    @Override
    public void createActions() {
        jogModeComboBox.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                robotConfig.getParameters().setJogMode(jogModeComboBox.getSelectedIndex());
                robotDevice.updateConfig(robotConfig);
            }
        });
        minusXButton.addMouseListener(new FeedbackButtonListeners());
        addXButton.addMouseListener(new FeedbackButtonListeners());

        minusYButton.addMouseListener(new FeedbackButtonListeners());
        addYButton.addMouseListener(new FeedbackButtonListeners());

        minusZButton.addMouseListener(new FeedbackButtonListeners());
        addZButton.addMouseListener(new FeedbackButtonListeners());

        minusRButton.addMouseListener(new FeedbackButtonListeners());
        addRButton.addMouseListener(new FeedbackButtonListeners());
        collectButton.addActionListener(e -> {
            collectButtonListener();
        });

        addAncestorListener(new AncestorListener() {
            @Override
            public void ancestorAdded(AncestorEvent event) {
                robotDevice.resumePoseMonitor();
            }

            @Override
            public void ancestorRemoved(AncestorEvent event) {
                robotDevice.pausePoseMonitor();
            }

            @Override
            public void ancestorMoved(AncestorEvent event) {

            }
        });
    }


    private void setPoseDisplay(FeedbackData data) {
        feedbackXLabel.setText("X:" + data.getX());
        feedbackYLabel.setText("Y:" + data.getY());
        feedbackZLabel.setText("Z:" + data.getZ());
        feedbackRLabel.setText("R:" + data.getR());
        String mode = data.getMode();
        if (mode != null) {
            robotModeLabel.setText("状态:" + data.getMode());
        }
        userCoordinateLabel.setText("用户坐标系:" + data.getUser());
        if (isContainSlideRail) {
            feedbackSlideRailLabel.setText("滑轨位置:" + data.getSlideRail() + "毫米");
        }
    }

    private void collectButtonListener() {
        if (collectButton.getText().equals("动作还原开始示教")) {
            startActions();
            collectButton.setText("动作还原结束示教");
        } else {
            stopActions();
            collectButton.setText("动作还原开始示教");
        }

    }

    private void startActions() {
        collecting = true;
    }

    private void stopActions() {
        collecting = false;
        List<RobotTrajectory> simplified = simplifyTrajectory(trajectoryList, 10);

        List<MoveEntity> moveEntityList = new ArrayList<>();
        for (RobotTrajectory point : simplified) {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(point.getX());
            moveEntity.setY(point.getY());
            moveEntity.setZ(point.getZ());
            moveEntity.setR(point.getR());
            moveEntityList.add(moveEntity);
        }
        Operation operation = Operation.buildOperation(robotDevice);
        operation.setOperationMethod(DeviceMethods.robotMove);
        operation.setOperationObject(moveEntityList);
        mainModel.getOperationModel().updateOperation(operation);

        robotDevice.move(moveEntityList);
        trajectoryList.clear();
    }

    private void startGetPoseThread() {
        robotDevice.enablePoseMonitor();
        robotDevice.pausePoseMonitor();
        if (!poseTransportAlive) {
            Executors.newSingleThreadExecutor().submit(() -> {
                poseTransportAlive = true;
                while (poseTransportAlive) {
                    try {
                        sseSession.readStream(UrlConstants.getSseUrl(robotDevice.getDeviceName()),
                                line -> {
                                    try {
                                        String jsonContent = line.substring(line.indexOf("{"));
                                        FeedbackData feedbackData = JSONObject.parseObject(jsonContent, FeedbackData.class);
                                        setPoseDisplay(feedbackData);
                                        if (collecting) {
                                            //获取当前时间戳
                                            long timestamp = System.currentTimeMillis();
                                            RobotTrajectory robotTrajectory = RobotTrajectory.builder()
                                                    .time(String.valueOf(timestamp))
                                                    .x(feedbackData.getX())
                                                    .y(feedbackData.getY())
                                                    .z(feedbackData.getZ())
                                                    .r(feedbackData.getR())
                                                    .build();
                                            addPoseToTrajectoryList(robotTrajectory);
                                        }
                                    } catch (JSONException e) {
                                        log.error(e.getMessage(), e);
                                    }
                                });
                    } catch (SocketTimeoutException ignored) {
                    } catch (Exception e) {
                        log.warn("机械臂位姿获取已停止");
                        log.warn(e.getMessage(), e);
                        poseTransportAlive = false;
                    }
                }
            });

        }

    }

    private void addPoseToTrajectoryList(RobotTrajectory robotTrajectory) {
        synchronized (trajectoryList) {
            trajectoryList.add(robotTrajectory);
        }
    }

    public static List<RobotTrajectory> simplifyTrajectory(List<RobotTrajectory> original, double epsilon) {
        if (original.size() <= 2) {
            return new ArrayList<>(original);
        }

        TreeSet<Integer> keepIndices = new TreeSet<>();
        keepIndices.add(0);
        keepIndices.add(original.size() - 1);

        douglasPeucker(original, 0, original.size() - 1, epsilon, keepIndices);

        List<RobotTrajectory> simplified = new ArrayList<>();
        for (int index : keepIndices) {
            simplified.add(original.get(index));
        }
        return simplified;
    }

    private static void douglasPeucker(List<RobotTrajectory> points, int start, int end,
                                       double epsilon, TreeSet<Integer> keep) {
        if (end - start < 1) return;

        RobotTrajectory a = points.get(start);
        RobotTrajectory b = points.get(end);

        int maxIndex = -1;
        double maxDistance = 0;

        for (int i = start + 1; i < end; i++) {
            double distance = distanceToLine(a, b, points.get(i));
            if (distance > maxDistance) {
                maxDistance = distance;
                maxIndex = i;
            }
        }

        if (maxDistance > epsilon) {
            keep.add(maxIndex);
            douglasPeucker(points, start, maxIndex, epsilon, keep);
            douglasPeucker(points, maxIndex, end, epsilon, keep);
        }
    }

    private static double distanceToLine(RobotTrajectory a, RobotTrajectory b, RobotTrajectory p) {

        double abx = b.getX() - a.getX();
        double aby = b.getY() - a.getY();
        double abz = b.getZ() - a.getZ();

        double apx = p.getX() - a.getX();
        double apy = p.getY() - a.getY();
        double apz = p.getZ() - a.getZ();

        double crossX = aby * apz - abz * apy;
        double crossY = abz * apx - abx * apz;
        double crossZ = abx * apy - aby * apx;

        double crossNorm = crossX * crossX + crossY * crossY + crossZ * crossZ;
        double abNorm = abx * abx + aby * aby + abz * abz;

        if (abNorm == 0) {

            return Math.sqrt(apx * apx + apy * apy + apz * apz);
        }

        return Math.sqrt(crossNorm / abNorm);
    }


    private class FeedbackButtonListeners extends MouseAdapter {

        @Override
        public void mousePressed(MouseEvent e) {
            AbstractButton button = (AbstractButton) e.getSource();
            robotDevice.moveJog(button.getText());
        }

        @Override
        public void mouseReleased(MouseEvent e) {
            robotDevice.stopMoveJog();
        }
    }

    public static void main(String[] args) {
        String s = "{\"j1\":0.0,\"j2\":0.0,\"j3\":0.0,\"j4\":0.0,\"mode\":\"模拟\",\"r\":0.7321805694832211,\"slideRail\":0.0,\"user\":2,\"x\":103.20628753400074,\"y\":15.037256448912505}";
        FeedbackData feedbackData = JSON.parseObject(s, FeedbackData.class);
        System.out.println(feedbackData);
    }

}
