package ui.layout.left.display.components.container.testbox.test;

import lombok.Getter;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

import static ui.utils.SwingUtil.setPanelBorder;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/12 19:36
 * @description :
 * @modified By :
 * @since : 2023/6/12
 **/
public class PWMOutputPane implements LayoutGenerator {
    private final ConfigBoardCardPane configBoardCardPane;
    private final PanelGenerator generator;
    @Getter
    private final List<CompositePanel> compositePanelList = new ArrayList<>();

    public PWMOutputPane(ConfigBoardCardPane configBoardCardPane, PanelGenerator generator) {
        this.configBoardCardPane = configBoardCardPane;
        this.generator = generator;
    }

    public JPanel getPWMOutputPanel() {
        JPanel rootPane = new JPanel(new BorderLayout());
        JPanel panel =  generator.generatePanel(10, "CH", null, 0, this);
        JPanel unitLabelPane = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        unitLabelPane.add(new JLabel(String.format("%s","频率：0~10000 Hz, 占空比：0~100%")));
        rootPane.add(unitLabelPane, BorderLayout.NORTH);
        rootPane.add(panel, BorderLayout.CENTER);
        setPanelBorder(rootPane, "PWM输出设定");
        return rootPane;
    }

    @Override
    public CustomizeGridLayout formLayout() {
        return new CustomizeGridLayout(10, 1);
    }

    @Override
    public String labelGenerator(int channel) {
        channel = channel + 1;
        String channelText = String.valueOf(channel);
        if (channel < 10) {
            channelText = "0" + channelText;
        }
        return channelText;
    }

    @Override
    public JComponent componentGenerator(int channel) {
        CompositePanel compositePanel = new CompositePanel(configBoardCardPane, channel + 1);
        compositePanelList.add(compositePanel);
        return compositePanel;
    }

}
