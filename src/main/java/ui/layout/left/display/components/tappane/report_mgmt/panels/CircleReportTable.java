package ui.layout.left.display.components.tappane.report_mgmt.panels;

import sdk.domain.screen.report.TouchReport;
import ui.base.table.DefaultTable;
import sdk.domain.screen.report.ScreenCircleSummary;
import ui.layout.left.display.components.tappane.report_mgmt.TouchReportListener;

public class CircleReportTable extends DefaultTable<ScreenCircleSummary> implements TouchReportListener {
    @Override
    protected String[] getColumns() {
        return new String[0];
    }

    @Override
    protected void setDefaultTableHeader() {

    }

    @Override
    public void setColumnWidth(int columnWidth) {

    }

    @Override
    protected Object[] convertData(ScreenCircleSummary data) {
        return new Object[0];
    }


    @Override
    public void report(TouchReport touchReport) {

    }
}
