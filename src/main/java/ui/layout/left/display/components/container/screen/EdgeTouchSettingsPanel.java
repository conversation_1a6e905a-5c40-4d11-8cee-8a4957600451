package ui.layout.left.display.components.container.screen;

import sdk.base.operation.OperationResult;
import sdk.domain.robot.MoveEntity;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.RobotDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.screen.touchPoint.Point;
import ui.layout.left.display.components.container.screen.touchPoint.PointUtils;
import ui.layout.left.display.components.container.screen.touchPoint.ScreenDimension;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


public class EdgeTouchSettingsPanel extends ScreenManager implements BaseView {
    private final JLabel edgeDistanceLabel;
    private final JLabel stepLabel;
    private final JSpinner edgeDistanceSpinner;
    private final JSpinner stepSpinner;
    private final JButton getEdgePointsBtn;
    private final JLabel upAndDownEntryDistanceLabel;
    private final JLabel leftAndRightEntryDistanceLabel;
    private final JSpinner upAndDownEntryDistanceSpinner;
    private final JSpinner leftAndRightEntryDistanceSpinner;

    private final JButton edgeEntryBtn;
    private final JLabel splitDistanceLabel;
    private final JSpinner splitDistanceSpinner;
    private final JButton getPointsInEdgeEntryLineBtn;
    private final JButton getEdgePointsWithScreenCoordinateBtn;

    private Boolean isAllowDrag;

    private BigDecimal segmentSplitDistance;

    private BigDecimal edgeDistance;
    private BigDecimal step;
    private final RobotDevice robotDevice;

    private final ScreenConfig screenConfig;

    public EdgeTouchSettingsPanel(DeviceContainer deviceContainer, ScreenConfig screenConfig) {
        robotDevice = (RobotDevice) deviceContainer.getDevice();
        this.screenConfig = screenConfig;
        getEdgePointsWithScreenCoordinateBtn = new JButton("获取边缘点的屏幕坐标");
        edgeDistanceLabel = new JLabel("边距:", SwingConstants.RIGHT);
        stepLabel = new JLabel("步长:", SwingConstants.RIGHT);
        edgeDistanceLabel.setMinimumSize(new Dimension(60, 20));
        upAndDownEntryDistanceLabel = new JLabel("上下进入距离:", SwingConstants.RIGHT);
        upAndDownEntryDistanceLabel.setMinimumSize(new Dimension(60, 20));
        leftAndRightEntryDistanceLabel = new JLabel("左右进入距离:", SwingConstants.RIGHT);
        leftAndRightEntryDistanceLabel.setMinimumSize(new Dimension(60, 20));
        splitDistanceLabel = new JLabel("分割距离:");
        getEdgePointsBtn = new JButton("获取边缘点的机械臂坐标");
        edgeEntryBtn = new JButton("边缘进入");
        getPointsInEdgeEntryLineBtn = new JButton("获取边缘进入屏幕坐标点");
        edgeDistanceSpinner = new JSpinner();
        stepSpinner = new JSpinner();
        upAndDownEntryDistanceSpinner = new JSpinner();
        leftAndRightEntryDistanceSpinner = new JSpinner();
        splitDistanceSpinner = new JSpinner();
        edgeDistanceSpinner.setModel(new SpinnerNumberModel(0, 0, 500, 1));
        stepSpinner.setModel(new SpinnerNumberModel(0, 0, 500, 1));
        upAndDownEntryDistanceSpinner.setModel(new SpinnerNumberModel(0, 0, 500, 1));
        leftAndRightEntryDistanceSpinner.setModel(new SpinnerNumberModel(0, 0, 500, 1));
        splitDistanceSpinner.setModel(new SpinnerNumberModel(0, 0, 100, 1));

        edgeDistanceSpinner.setPreferredSize(new Dimension(120, 20));
        stepSpinner.setPreferredSize(new Dimension(120, 20));
        upAndDownEntryDistanceSpinner.setPreferredSize(new Dimension(120, 20));
        leftAndRightEntryDistanceSpinner.setPreferredSize(new Dimension(120, 20));
        splitDistanceSpinner.setPreferredSize(new Dimension(120, 20));
        createView();
        createActions();
    }

    @Override
    public void createView() {
        add(createBottomPanel());
    }

    private JPanel createBottomPanel() {
        JPanel edgePointsSettingPanel = new JPanel(new GridBagLayout());

        edgePointsSettingPanel.add(edgeDistanceLabel, new GridBagConstraints(0, 0, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(edgeDistanceSpinner, new GridBagConstraints(1, 0, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(stepLabel, new GridBagConstraints(2, 0, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(stepSpinner, new GridBagConstraints(3, 0, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(getEdgePointsBtn, new GridBagConstraints(4, 0, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        edgePointsSettingPanel.add(upAndDownEntryDistanceLabel, new GridBagConstraints(0, 2, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(upAndDownEntryDistanceSpinner, new GridBagConstraints(1, 2, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(leftAndRightEntryDistanceLabel, new GridBagConstraints(2, 2, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(leftAndRightEntryDistanceSpinner, new GridBagConstraints(3, 2, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(edgeEntryBtn, new GridBagConstraints(4, 2, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        edgePointsSettingPanel.add(splitDistanceLabel, new GridBagConstraints(0, 3, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(splitDistanceSpinner, new GridBagConstraints(1, 3, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        edgePointsSettingPanel.add(getPointsInEdgeEntryLineBtn, new GridBagConstraints(2, 3, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        return edgePointsSettingPanel;
    }

    public void calculateEdgePoints() {
        double a = Point.getAlpha(leftBottomPoint, leftTopPoint);
        double b = 0;
        if (a != 90) {
            b = Point.getBeta(a);
        }
        edgeDistance = (BigDecimal) edgeDistanceSpinner.getValue();
        step = (BigDecimal) stepSpinner.getValue();
        if (!(edgeDistance.intValue() == 0 || step.intValue() == 0)) {
            //          获取边缘点
            Point.EdgePoints edgePoints = Point.getEdgePoint(leftBottomPoint, leftTopPoint, rightTopPoint, rightBottomPoint, edgeDistance, step, b);
            System.out.println(edgePoints.topRowPoints);
            System.out.println(edgePoints.bottomRowPoints);
            System.out.println(edgePoints.leftColumnPoints);
            System.out.println(edgePoints.rightColumnPoints);
        } else {
            SwingUtil.showWarningDialog(this, "请设置边距/步长");
        }
    }

    public void calculateScreenEdgePoints() {
        ScreenDimension screenDimension = getScreenDimension();
        edgeDistance = (BigDecimal) edgeDistanceSpinner.getValue();
        step = (BigDecimal) stepSpinner.getValue();
        if (!(edgeDistance.intValue() == 0 || step.intValue() == 0)) {
            Point.EdgePoints screenPoints = Point.getScreenPoints(screenDimension.getWidth(), screenDimension.getHeight(), edgeDistance, step);
            System.out.println(screenPoints.topRowPoints);
            System.out.println(screenPoints.bottomRowPoints);
            System.out.println(screenPoints.leftColumnPoints);
            System.out.println(screenPoints.rightColumnPoints);
        } else {
            SwingUtil.showWarningDialog(this, "请设置边距/步长");
        }
    }

    //    /**
//     * @param request 请求什么数据
//     * @return List<?> 返回边缘进入的开始点和结束点  、返回开始点和结束点及这两者之间的点
//     * @method edgeEntry 关于边缘进入的函数
//     */
//    public List<?> edgeEntry(String request) {
//        initPoints();
//        double a = Point.getAlpha(p1, p2);
//        double b = 0;
//        if (a != 90) {
//            b = Point.getBeta(a);
//        }
//        edgeDistance = String.valueOf(edgeDistanceSpinner.getValue());
//        step = String.valueOf(stepSpinner.getValue());
//        String upAndDownEntryDistance = String.valueOf(upAndDownEntryDistanceSpinner.getValue());
//        String leftAndRightEntryDistance = String.valueOf(leftAndRightEntryDistanceSpinner.getValue());
//        String num = String.valueOf(numOfEdgeEntryLineSpinner.getValue());
//        Boolean condition = null;
//        if (Objects.equals(request, "ForEntryPoints"))
//            condition = edgeDistance.equals("0") || step.equals("0") || upAndDownEntryDistance.equals("0") || leftAndRightEntryDistance.equals("0");
//        if (Objects.equals(request, "ForPointsInLine"))
//            condition = edgeDistance.equals("0") || step.equals("0") || upAndDownEntryDistance.equals("0") || leftAndRightEntryDistance.equals("0") || num.equals("2");
//        if (Boolean.TRUE.equals(condition)) {
//            isAllowDrag=false;
//            if (Objects.equals(request, "ForEntryPoints"))
//                SwingUtil.showWarningDialog(this, "请设置边距/步长/进入距离");
//            if (Objects.equals(request, "ForPointsInLine"))
//                SwingUtil.showWarningDialog(this, "请设置边距/步长/进入距离/边缘进入点的个数");
//            return null;
//        } else {
//            isAllowDrag=true;
//            List<?> result = null;
//            if (Objects.equals(request, "ForEntryPoints")) {
//                result = Point.getPointOfEdgeEntry(p1, p2, p3, p4, BigDecimal.valueOf(Double.parseDouble(edgeDistance)),
//                        BigDecimal.valueOf(Double.parseDouble(step)), b,
//                        BigDecimal.valueOf(Double.parseDouble(upAndDownEntryDistance)),
//                        BigDecimal.valueOf(Double.parseDouble(leftAndRightEntryDistance)), request
//                        , BigDecimal.valueOf(5)).get(0);
//            }
//            if (Objects.equals(request, "ForPointsInLine")) {
////                pointsInEdgeEntryLine
//                List<List<Point[]>> allPointInEntryLineList = (List<List<Point[]>>) Point.getPointOfEdgeEntry(p1, p2, p3, p4, BigDecimal.valueOf(Double.parseDouble(edgeDistance)),
//                        BigDecimal.valueOf(Double.parseDouble(step)), b,
//                        BigDecimal.valueOf(Double.parseDouble(upAndDownEntryDistance)),
//                        BigDecimal.valueOf(Double.parseDouble(leftAndRightEntryDistance)), request, BigDecimal.valueOf(5)
//                ).get(1);
//                List<Point[]> screenCoordinateOfPointsInEdgeEntryLine = new ArrayList<>();
//                for (List<Point[]> pointInEntryLineList : allPointInEntryLineList) {
//                    for (Point[] points : pointInEntryLineList) {
//
//                        Point[] screenCoordinateOfPoints = new Point[points.length];
//                        int i = 0;
//                        for (Point point : points) {
//                            screenCoordinateOfPoints[i++] = calculateScreenPointByAnyRobotCoordinatePoint(point, p1, p2, p3);
//                        }
////                        System.out.println("screenCoordinateOfPoints??????:"+Arrays.toString(screenCoordinateOfPoints));
//                        System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
//                        screenCoordinateOfPointsInEdgeEntryLine.add(screenCoordinateOfPoints);
//                    }
//                }
//                return screenCoordinateOfPointsInEdgeEntryLine;
//            }
//            return result;
//        }
//
//    }

    public List<?> edgeEntry(String request) {
        double a = Point.getAlpha(leftBottomPoint, leftTopPoint);
        double b = 0;
        if (a != 90) {
            b = Point.getBeta(a);
        }
        edgeDistance = (BigDecimal) edgeDistanceSpinner.getValue();
        step = (BigDecimal) stepSpinner.getValue();
        String upAndDownEntryDistance = String.valueOf(upAndDownEntryDistanceSpinner.getValue());
        String leftAndRightEntryDistance = String.valueOf(leftAndRightEntryDistanceSpinner.getValue());
        String splitDistance = String.valueOf(splitDistanceSpinner.getValue());
        Boolean condition = edgeDistance.intValue() == 0 || step.intValue() == 0 || upAndDownEntryDistance.equals("0") || leftAndRightEntryDistance.equals("0") || splitDistance.equals("0");

        if (Boolean.TRUE.equals(condition)) {
            isAllowDrag = false;
            SwingUtil.showWarningDialog(this, "请设置边距/步长/进入距离/分割距离");
            return null;
        } else {
            isAllowDrag = true;
            List<List<Point[]>> result = null;
            if (Objects.equals(request, "ForEntryPoints")) {
                result = Point.getPointOfEdgeEntry(leftBottomPoint, leftTopPoint, rightTopPoint, rightBottomPoint,
                        edgeDistance,
                        step,
                        b,
                        BigDecimal.valueOf(Double.parseDouble(upAndDownEntryDistance)),
                        BigDecimal.valueOf(Double.parseDouble(leftAndRightEntryDistance)), BigDecimal.valueOf(Double.parseDouble(splitDistance)));
            }
            if (Objects.equals(request, "ForPointsInLine")) {
//                pointsInEdgeEntryLine
                List<List<Point[]>> allPointsInEntryLineList = Point.getPointOfEdgeEntry(leftBottomPoint, leftTopPoint, rightTopPoint, rightBottomPoint, edgeDistance,
                        step, b,
                        BigDecimal.valueOf(Double.parseDouble(upAndDownEntryDistance)),
                        BigDecimal.valueOf(Double.parseDouble(leftAndRightEntryDistance)), BigDecimal.valueOf(Double.parseDouble(splitDistance))
                );
                List<Point[]> screenCoordinateOfPointsInEdgeEntryLine = new ArrayList<>();
                for (List<Point[]> pointInEntryLineList : allPointsInEntryLineList) {
                    for (Point[] points : pointInEntryLineList) {
                        Point[] screenCoordinateOfPoints = new Point[points.length];
                        int i = 0;
                        for (Point point : points) {
                            screenCoordinateOfPoints[i++] = Point.calculateScreenPointByAnyRobotCoordinatePoint(point, leftBottomPoint, leftTopPoint, rightTopPoint);
                        }
                        screenCoordinateOfPointsInEdgeEntryLine.add(screenCoordinateOfPoints);
                    }
                }
                return screenCoordinateOfPointsInEdgeEntryLine;
            }
            return result;
        }

    }

    @Override
    public void createActions() {
        getEdgePointsBtn.addActionListener(e -> {
            if (!(leftBottomPoint.isNull() || leftTopPoint.isNull() || rightTopPoint.isNull() || rightBottomPoint.isNull())) {
                if (checkPoints()) {
                    calculateEdgePoints();
                } else {
                    SwingUtil.showWarningDialog(this, "请检查四个顶点坐标");
                }
            } else {
                SwingUtil.showWarningDialog(this, "请设置四个顶点坐标");
            }
        });

        edgeEntryBtn.addActionListener(e -> {
            isAllowDrag = false;
            java.util.List<java.util.List<Point[]>> allPointsInEntryLineList = (java.util.List<java.util.List<Point[]>>) edgeEntry("ForEntryPoints");
            if (isAllowDrag) {
                for (java.util.List<Point[]> line : allPointsInEntryLineList) {
                    for (Point[] points : line) {
                        List<MoveEntity> moveEntityList = new ArrayList<>();
                        for (Point point : points) {
                            MoveEntity moveEntity = new MoveEntity();
                            moveEntity.setX(point.getX().doubleValue());
                            moveEntity.setY(point.getY().doubleValue());
                            moveEntity.setZ(averageZ.doubleValue());
                            moveEntityList.add(moveEntity);
                        }
                        OperationResult operationResult = robotDevice.slide(moveEntityList);
                        if (operationResult.isFailed()) {
                            SwingUtil.showWebMessageDialog(EdgeTouchSettingsPanel.this, operationResult.getMessage());
                        }
                    }
                }
            }

        });

        getPointsInEdgeEntryLineBtn.addActionListener(e -> {
            ScreenDimension screenDimension = getScreenDimension();
            int resolutionWidth = screenConfig.getDisplay().getScreen().getWidth();
            int resolutionHeight = screenConfig.getDisplay().getScreen().getHeight();
            if (!(resolutionWidth == 0) && !(resolutionHeight == 0)) {
                List<Point[]> screenCoordinateOfPointsInEdgeEntryLine = (List<Point[]>) edgeEntry("ForPointsInLine");
                List<Point[]> resolutionCoordinatePointsList = new ArrayList<>();
                for (Point[] points : screenCoordinateOfPointsInEdgeEntryLine) {
                    Point[] resolutionCoordinatePointsArray = new Point[points.length];
                    int i = 0;
                    for (Point point : points) {
                        Point resolutionCoordinatePoint = PointUtils.getResolutionCoordinate(resolutionWidth, resolutionHeight,
                                screenDimension.getWidth(), screenDimension.getHeight(), point);
                        resolutionCoordinatePointsArray[i++] = resolutionCoordinatePoint;
                        System.out.println("resolutionCoordinatePoint:" + resolutionCoordinatePoint);
                    }
                    resolutionCoordinatePointsList.add(resolutionCoordinatePointsArray);
                }
            } else {
                SwingUtil.showWarningDialog(this, "请输入分辨率");
            }
        });

        getPointsInEdgeEntryLineBtn.addActionListener(e -> {
            ScreenDimension screenDimension = getScreenDimension();
            int resolutionWidth = screenConfig.getDisplay().getScreen().getWidth();
            int resolutionHeight = screenConfig.getDisplay().getScreen().getHeight();
            if (!(resolutionWidth == 0) && !(resolutionHeight == 0)) {
                List<Point[]> screenCoordinateOfPointsInEdgeEntryLine = (List<Point[]>) edgeEntry("ForPointsInLine");
                List<Point[]> resolutionCoordinatePointsList = new ArrayList<>();
                for (Point[] points : screenCoordinateOfPointsInEdgeEntryLine) {
                    Point[] resolutionCoordinatePointsArray = new Point[points.length];
                    int i = 0;
                    for (Point point : points) {
                        Point resolutionCoordinatePoint = PointUtils.getResolutionCoordinate(resolutionWidth, resolutionHeight,
                                screenDimension.getWidth(), screenDimension.getHeight(), point);
                        resolutionCoordinatePointsArray[i++] = resolutionCoordinatePoint;
                        System.out.println("resolutionCoordinatePoint:" + resolutionCoordinatePoint);
                    }
                    resolutionCoordinatePointsList.add(resolutionCoordinatePointsArray);
                }
            } else {
                SwingUtil.showWarningDialog(this, "请输入分辨率");
            }
        });

        getEdgePointsWithScreenCoordinateBtn.addActionListener(e -> {
            if (isPointsExist()) {
                if (checkPoints()) {
                    calculateScreenEdgePoints();
                } else {
                    SwingUtil.showWarningDialog(this, "请检查四个顶点坐标");
                }
            } else {
                SwingUtil.showWarningDialog(this, "请设置四个顶点坐标");
            }
        });
    }
}
