package ui.layout.left.display.components.container.picture;

import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import sdk.base.operation.Operation;
import sdk.constants.methods.ImageMethods;
import ui.base.BaseView;
import ui.config.json.devices.camera.CameraConfig;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.Objects;

import static sdk.base.BaseHttpClient.executeOperation;

/**
 * @author: QinHao
 * @description:
 * @date: 2025/6/3 11:42
 */
public class FrameRateControlPanel extends JPanel implements BaseView {
    private final MainModel mainModel;
    private final PictureContainer container;
    @Getter
    private final JComboBox<String> frameComboBox;
    private final JPanel framePanel;


    public FrameRateControlPanel(PictureContainer container, MainModel mainModel) {
        this.container = container;
        this.mainModel = mainModel;
        framePanel = new JPanel();
        frameComboBox = new JComboBox<>(new String[]{"15", "30", "60"});
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new BoxLayout(this, BoxLayout.Y_AXIS));

        Font smallFont = new Font(null, Font.PLAIN, 12);
        Color grayColor = new Color(101, 101, 101);

        JLabel areaEditLabel = createMenuLabel("相机参数");
        frameComboBox.setMaximumSize(new Dimension(160, 30));
        JLabel modeLabel = new JLabel("帧率");
        modeLabel.setFont(smallFont);
        modeLabel.setForeground(grayColor);


        add(areaEditLabel);
        add(modeLabel);
        add(frameComboBox);
        add(Box.createVerticalStrut(10));

        modeLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        frameComboBox.setAlignmentX(Component.LEFT_ALIGNMENT);

    }


    @Override
    public void createActions() {
        frameComboBox.addActionListener(e -> {
            setFrameRate(Integer.parseInt(Objects.requireNonNull(frameComboBox.getSelectedItem()).toString()));
            updateCameraConfig();
        });

    }

    private void setFrameRate(int frameRate) {
        Operation operation = Operation.buildOperation(container.getDevice());
        operation.setOperationMethod(ImageMethods.setCameraFrameRate);
        operation.setOperationObject(frameRate);
        executeOperation(operation);
    }

    private void updateCameraConfig() {
        CameraConfig cameraConfig = container.getDevice().loadConfig(mainModel.getAppInfo().getProject(), CameraConfig.class);
        cameraConfig.getCameraSettings().setFrameRate(Float.parseFloat(Objects.requireNonNull(frameComboBox.getSelectedItem()).toString()));
        container.getDevice().updateConfig(cameraConfig);
    }


    @NotNull
    private JLabel createMenuLabel(String text) {
        JLabel cameraExposureLabel = new JLabel(text) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g); // 默认内容
                // 绘制蓝色竖杠 (3px宽)
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setColor(new Color(30, 144, 255));
                g2.setStroke(new BasicStroke(3f));
                // 调整参数实现间距
                int lineX = 0;
                int verticalMargin = getHeight() / 4; // 上下边距
                g2.drawLine(lineX, verticalMargin,
                        lineX, getHeight() - verticalMargin);
                g2.dispose();
            }
        };
        // 通过边框留白让文字右移
        cameraExposureLabel.setBorder(BorderFactory.createEmptyBorder(0, 4, 0, 0));
        cameraExposureLabel.setFont(new Font("微软雅黑", Font.BOLD, 14));
        return cameraExposureLabel;
    }

}