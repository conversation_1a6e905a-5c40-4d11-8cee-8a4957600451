package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.oscilloscope.OscilloscopeContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

/**
 * 示波器控制界面
 */
public class OscilloscopeTabPaneView extends DeviceTabPaneView {

    public OscilloscopeTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        DeviceContainer deviceContainer;
        deviceContainer = new OscilloscopeContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, deviceContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
