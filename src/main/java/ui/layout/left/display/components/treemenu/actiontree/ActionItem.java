package ui.layout.left.display.components.treemenu.actiontree;

import lombok.Getter;
import lombok.Setter;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationTarget;
import sdk.entity.OperationTargetHolder;
import ui.base.treelist.AbstractTreeItem;
import ui.model.MainModel;

public class ActionItem extends AbstractTreeItem {
    @Setter
    @Getter
    private OperationTarget operationTarget;

    private Operation operation;
    private MainModel mainModel;

    private final Actionable actionable;

    @Getter
    private final OperationMethod operationMethod;

    public ActionItem(OperationMethod operationMethod, MainModel mainModel) {
        this(operationMethod, null, mainModel);
    }

    public ActionItem(OperationMethod operationMethod, String deviceType, MainModel mainModel) {
        this(operationMethod, (Actionable) null);
        this.operation = Operation.buildOperation(deviceType, 1, operationMethod);
        this.mainModel = mainModel;
    }

    public ActionItem(OperationMethod operationMethod, Actionable actionable) {
        this.actionable = actionable;
        this.operationMethod = operationMethod;
        setName(operationMethod.getMethodName());
    }


    public void show() {
        if (actionable instanceof ActionDialog) {
            ActionDialog actionDialog = (ActionDialog) actionable;
            actionDialog.applyOperationMethod(operationMethod);
            actionDialog.setVisible(true);
        }
        if (operation != null) {
            mainModel.getOperationModel().updateOperation(operation);
        }
    }

    public static class PowerAction extends ActionItem {

        public PowerAction(OperationMethod operationMethod, ActionDialog component) {
            super(operationMethod, component);
            setOperationTarget(OperationTargetHolder.getPowerDeviceManager());
        }
    }


}
