package ui.layout.left.display.components.container.autoclikcer;

import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.screen.AutoClickerDataPackage;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.AutoClickerDevice;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.util.Objects;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-28 18:09
 * @description :
 * @modified By :
 * @since : 2022-6-28
 */
@Slf4j
public class AutoClickerControlPanel extends DeviceControlPanel implements BaseView {

    private final JButton clickDebugButton;
    private final JButton clickAddToScriptButton;
    private final JSpinner timesSpinner;
    private final JSpinner numSpinner;
    private final JSpinner intervalSpinner;
    private final JSpinner holdSpinner;
    private final JSpinner pressNumSpinner;
    private final JButton pressButton;
    private final JButton releaseButton;
    private final JButton pressAddToScriptButton;
    private final JButton releaseAddToScriptButton;
    private final JButton checkPointAddToScriptButton;
    private final JComboBox<String> serialNameComboBox;
    private final MainModel mainModel;


    public AutoClickerControlPanel(DeviceContainer deviceContainer, MainModel mainModel) {
        super(deviceContainer, mainModel);
        this.mainModel = mainModel;
        holdSpinner = new JSpinner(new SpinnerNumberModel(1000, 1, Integer.MAX_VALUE, 100));
        timesSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));
        numSpinner = new JSpinner(new SpinnerNumberModel(1, 0, 12, 1));
        intervalSpinner = new JSpinner(new SpinnerNumberModel(1000, 1, Integer.MAX_VALUE, 500));

        clickDebugButton = SwingUtil.getDebugButton();
        pressNumSpinner = new JSpinner(new SpinnerNumberModel(1, 0, 12, 1));
        clickAddToScriptButton = SwingUtil.getAddToScriptButton("点击");
        pressButton = new JButton("按下↓");
        releaseButton = new JButton("释放↑");
        pressAddToScriptButton = SwingUtil.getAddToScriptButton("按下↓");
        releaseAddToScriptButton = SwingUtil.getAddToScriptButton("释放↑");
        checkPointAddToScriptButton = SwingUtil.getAddToScriptButton("报点检测");
        serialNameComboBox = new JComboBox<>(new String[]{"SerialPort#1", "SerialPort#2"});
        createView();
        createActions();
    }


    @Override
    public Integer getChannel() {
        return 1;
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout(0, 0));

        JPanel panel = new JPanel();
        add(panel, BorderLayout.NORTH);
        panel.setLayout(new GridLayout(2, 1, 0, 0));

        JPanel panel_1 = new JPanel();
        panel_1.setBorder(new TitledBorder(new LineBorder(new Color(0, 0, 0)), "单点操作", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        panel.add(panel_1);
        panel_1.setLayout(new GridLayout(3, 1, 0, 0));

        JPanel panel_3 = new JPanel();
        FlowLayout flowLayout = (FlowLayout) panel_3.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        panel_1.add(panel_3);

        panel_3.add(new JLabel("通道（0点击所有）："));
        panel_3.add(numSpinner);

        panel_3.add(new JLabel("点击次数"));
        panel_3.add(timesSpinner);
        panel_3.add(clickDebugButton);

        JPanel panel_4 = new JPanel();
        FlowLayout flowLayout_1 = (FlowLayout) panel_4.getLayout();
        flowLayout_1.setAlignment(FlowLayout.LEFT);
        panel_1.add(panel_4);
        panel_4.add(new JLabel("点击间隔（毫秒）"));
        panel_4.add(intervalSpinner);

        panel_4.add(new JLabel("按下时长（毫秒）"));
        panel_4.add(holdSpinner);
        panel_4.add(clickAddToScriptButton);

        JPanel panel_7 = new JPanel();
        FlowLayout flowLayout_4 = (FlowLayout) panel_7.getLayout();
        flowLayout_4.setAlignment(FlowLayout.LEFT);
        panel_1.add(panel_7);
        panel_7.add(new JLabel("选择串口"));
        panel_7.add(serialNameComboBox);
        panel_7.add(checkPointAddToScriptButton);


        JPanel panel_2 = new JPanel();
        panel_2.setBorder(new TitledBorder(new LineBorder(new Color(0, 0, 0)), "动作分解", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        panel.add(panel_2);
        panel_2.setLayout(new GridLayout(2, 1, 0, 0));

        JPanel panel_5 = new JPanel();
        FlowLayout flowLayout_2 = (FlowLayout) panel_5.getLayout();
        flowLayout_2.setAlignment(FlowLayout.LEFT);
        panel_2.add(panel_5);

        panel_5.add(new JLabel("通道（0为所有）："));
        panel_5.add(pressNumSpinner);

        JPanel panel_6 = new JPanel();
        FlowLayout flowLayout_3 = (FlowLayout) panel_6.getLayout();
        flowLayout_3.setAlignment(FlowLayout.LEFT);
        panel_2.add(panel_6);

        panel_6.add(pressButton);
        panel_6.add(pressAddToScriptButton);
        panel_6.add(releaseButton);
        panel_6.add(releaseAddToScriptButton);

    }

    private AutoClickerDataPackage getClickerValue() {
        return AutoClickerDataPackage.builder()
                .num((int) numSpinner.getValue())
                .interval((int) intervalSpinner.getValue())
                .hold((int) holdSpinner.getValue())
                .times((int) timesSpinner.getValue())
                .build();
    }

    @Override
    public void createActions() {
        clickDebugButton.addActionListener(e -> {
            AutoClickerDevice autoClickerDevice = (AutoClickerDevice) getDeviceContainer().getDevice();
            OperationResult operationResult = autoClickerDevice.startClick(getChannel(), getClickerValue());
            if (operationResult.isFailed()) {
                JOptionPane.showMessageDialog(AutoClickerControlPanel.this, operationResult.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        });
        clickAddToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
            operation.setOperationMethod(DeviceMethods.clickOneChannelStart);
            operation.setOperationObject(getClickerValue());
            getMainModel().getOperationModel().updateOperation(operation);
        });
        pressButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
            operation.setOperationMethod(DeviceMethods.channelDown);
            operation.setOperationObject(pressNumSpinner.getValue());
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                JOptionPane.showMessageDialog(AutoClickerControlPanel.this, operationResult.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        });
        releaseButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
            operation.setOperationMethod(DeviceMethods.channelUp);
            operation.setOperationObject(pressNumSpinner.getValue());
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                JOptionPane.showMessageDialog(AutoClickerControlPanel.this, operationResult.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        });
        pressAddToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
            operation.setOperationMethod(DeviceMethods.channelDown);
            operation.setOperationObject(pressNumSpinner.getValue());
            getMainModel().getOperationModel().updateOperation(operation);
        });
        releaseAddToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
            operation.setOperationMethod(DeviceMethods.channelUp);
            operation.setOperationObject(pressNumSpinner.getValue());
            getMainModel().getOperationModel().updateOperation(operation);
        });
        checkPointAddToScriptButton.addActionListener(e -> {
            String projectName = mainModel.getAppInfo().getProject();
            ScreenConfig screenConfig = OperationTargetHolder.getScreenKit().loadConfig(projectName);
            screenConfig.setSerialAliasName(Objects.requireNonNull(serialNameComboBox.getSelectedItem()).toString());
            if (screenConfig.getTouchPointMatrix().getProtocolHeader() == null) {
                JOptionPane.showMessageDialog(AutoClickerControlPanel.this, "请先连接串口设置报点协议", "错误", JOptionPane.ERROR_MESSAGE);
            } else {
                Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
                operation.setOperationMethod(DeviceMethods.autoClickCheckPoint);
                AutoClickerCheckPoint autoClickerCheckPoint = AutoClickerCheckPoint.builder()
                        .serialName((String) serialNameComboBox.getSelectedItem())
                        .fingers(1)
                        .screenConfig(screenConfig)
                        .autoClickerDataPackage(getClickerValue())
                        .build();
                operation.setOperationObject(autoClickerCheckPoint);
                getMainModel().getOperationModel().updateOperation(operation);
            }
        });

    }

}
