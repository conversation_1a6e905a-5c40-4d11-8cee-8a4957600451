package ui.layout.left.display.components.container.lin;

import lombok.Getter;
import lombok.Setter;
import sdk.domain.Device;
import sdk.domain.bus.LinConfig;
import ui.model.MainModel;

import javax.swing.*;
import java.io.File;

@Setter
@Getter
public class LinChannelView extends JTabbedPane {

    private Device device;
    private final int channel;
    private final LinContainer linContainer;
    private final MainModel mainModel;
    private final LinConfig linConfig;
    private final String savePath;
    private LinLdfSendSettingView linLdfSendSettingView;

    public LinChannelView(LinContainer linContainer, MainModel mainModel, LinConfig linConfig, int channel) {
        this.device = linContainer.getDevice();
        this.channel = channel;
        this.linContainer = linContainer;
        this.mainModel = mainModel;
        this.linConfig = linConfig;
        String basePath = String.format("D:\\FlyTest\\data\\client\\projects\\%s\\config\\ldf", mainModel.getAppInfo().getProject());
        savePath = new File(basePath, linContainer.getDevice().getDeviceName()).getAbsolutePath();
        linLdfSendSettingView = new LinLdfSendSettingView(linContainer, mainModel, linConfig, channel, savePath);

        addTab("LDF发送", linLdfSendSettingView);

    }
}
