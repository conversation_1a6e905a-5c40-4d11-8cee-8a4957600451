package ui.layout.left.display.components.container.power.kikusui.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class StartPulse extends KikusuiWavePulse {
    @JSONField(ordinal = 1)
    private boolean constantRandom;

    @JSONField(ordinal = 2)
    private List<Float> constantValues;

    @JSONField(ordinal = 3)
    private double fromVoltage;

    @JSONField(ordinal = 4)
    private double toVoltage;

    @JSONField(ordinal = 5)
    private double fromTime; //秒

    @JSONField(ordinal = 6)
    private double toTime; //秒

    @J<PERSON>NField(ordinal = 7)
    private int fromCycle;

    @JSONField(ordinal = 8)
    private int toCycle;

}
