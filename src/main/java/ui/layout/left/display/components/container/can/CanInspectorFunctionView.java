package ui.layout.left.display.components.container.can;

import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

public class CanInspectorFunctionView extends JPanel {

    private final CanSignalInspectorFunctionView canSignalInspectorFunctionView;
    private final CanMessageInspectorFunctionView canMessageInspectorFunctionView;

    public CanInspectorFunctionView(CanContainer canContainer, MainModel mainModel, int channel) {
        this.canSignalInspectorFunctionView = new CanSignalInspectorFunctionView(canContainer, mainModel, channel);
        this.canMessageInspectorFunctionView = new CanMessageInspectorFunctionView(canContainer, mainModel, channel);
        setLayout(new BorderLayout());
        add(this.canSignalInspectorFunctionView, BorderLayout.NORTH);
        add(this.canMessageInspectorFunctionView, BorderLayout.SOUTH);
    }
}
