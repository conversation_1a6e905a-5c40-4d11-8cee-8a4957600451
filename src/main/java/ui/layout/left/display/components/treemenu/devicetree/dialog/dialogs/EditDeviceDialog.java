package ui.layout.left.display.components.treemenu.devicetree.dialog.dialogs;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.base.treelist.AbstractTreeNode;
import ui.layout.left.display.components.treemenu.devicetree.manager.DialogPaneManager;
import ui.layout.right.components.operate.header.DeviceOperateHeaderManagerPanel;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/19 17:13
 * @description :
 * @modified By :
 * @since : 2023/7/19
 **/
public class EditDeviceDialog extends JDialog {
    private final JButton modifyConfigButton;
    private final Device device;
    private final ClientView clientView;
    private final MainModel mainModel;
    private final DeviceOperatePanel devicePane;
    private final AbstractTreeNode parentNode;

    public EditDeviceDialog(AbstractTreeNode parentNode, Device device, MainModel mainModel, ClientView clientView, DialogPaneManager dialogPaneManager) {
        this.device = device;
        this.clientView = clientView;
        this.mainModel = mainModel;
        this.parentNode = parentNode;
        setTitle("修改配置");
        setBounds(600, 300, 600, 300);
        modifyConfigButton = new JButton("修改");
        device.setDeviceTypeName(parentNode.getName());
        devicePane = dialogPaneManager.getPanelByDeviceName(device);
        DeviceOperateHeaderManagerPanel deviceOperateHeaderManagerPanel = devicePane.getDeviceOperateHeader().getDeviceOperateHeaderManagerPanel();
        deviceOperateHeaderManagerPanel.updateComboBox();
//        deviceOperateHeaderManagerPanel.setDataFromConfig();
        devicePane.getDeviceOperateHeader().getDeviceOperateHeaderManagerPanel().setPortComboBox(device.getFriendlyName());
        add(devicePane, BorderLayout.CENTER);
        add(modifyConfigButton, BorderLayout.SOUTH);
        createActions();
    }

    private void createActions() {
        modifyConfigButton.addActionListener(e -> {
            Device deviceInfo = devicePane.getDeviceOperateHeader().getDeviceOperateHeaderManagerPanel().getConfigDevice();
            //断开
            //配置
            //连接
            closeDialog();
        });
    }

    private void closeDialog() {
        setModal(false);
        dispose();
    }

}
