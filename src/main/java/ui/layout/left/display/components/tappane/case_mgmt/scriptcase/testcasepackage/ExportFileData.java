package ui.layout.left.display.components.tappane.case_mgmt.scriptcase.testcasepackage;

import lombok.Builder;
import lombok.Data;
import sdk.domain.complex.TemplatePicture;
import sdk.domain.robot.RobotCoordinates;
import sdk.domain.screen.ScreenConfig;

import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 导出数据
 * @date: 2024/8/19 11:37
 */
@Data
@Builder
public class ExportFileData {
    private String filePath;
    private List<String> jsonFilePaths;
    private List<String> imageFilePaths;
    private Map<String, List<RobotCoordinates>> deviceRobotCoordinatesMap;
    private ScreenConfig screenConfig;
    private List<TemplatePicture> templatePictures;
}
