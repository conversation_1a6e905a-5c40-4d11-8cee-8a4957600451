package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.picture.ExternalPictureContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;

public class ExternalPicturePaneView extends DeviceTabPaneView {

    public ExternalPicturePaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        ExternalPictureContainer externPictureContainer = new ExternalPictureContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, externPictureContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
    }
}
