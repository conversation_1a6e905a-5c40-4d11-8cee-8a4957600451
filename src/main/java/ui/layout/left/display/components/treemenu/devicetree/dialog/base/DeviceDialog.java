package ui.layout.left.display.components.treemenu.devicetree.dialog.base;

import sdk.domain.Device;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/10 10:49
 * @description :
 * @modified By :
 * @since : 2023/7/10
 **/
public class DeviceDialog extends JDialog {

    private final List<AddDeviceEventListener> addDeviceEventListenerList = new ArrayList<>();

    public void addDeviceEventListener(AddDeviceEventListener listener) {
        addDeviceEventListenerList.add(listener);
    }

    public void saveConfig(Device device) {
        for (AddDeviceEventListener addDeviceEventListener : addDeviceEventListenerList) {
            addDeviceEventListener.configDevice(device);
        }
    }

}
