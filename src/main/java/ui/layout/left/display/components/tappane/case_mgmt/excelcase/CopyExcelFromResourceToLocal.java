package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;

@Slf4j
public class CopyExcelFromResourceToLocal {
    public static void main(String[] args) {
        String resourceFilePath = "D:\\uidq9000\\IdeaProjects\\0219\\final_test\\aitestxclient\\src\\main\\resources\\action_sequences_template.xlsm";
        String localDestinationPath = System.getProperty("user.home") + File.separator + "output333.xlsm"; // 请替换为实际路径

        String command = String.format("copy \"%s\" \"%s\"",resourceFilePath,localDestinationPath);
//        String command = String.format("copy \"%s\" \"%s\"",
//                CopyExcelFromResourceToLocal.class.getResource(resourceFilePath).getFile(),
//                localDestinationPath.replace("\\", "\\\\")); // 注意Windows路径中的反斜杠转义

        try {
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("Excel file successfully copied from resource to local.");
            } else {
                System.err.println("Error copying Excel file. Exit code: " + exitCode);
            }
        } catch (InterruptedException | IOException e) {
            System.err.println("An error occurred while executing the copy command:");
            log.error(e.getMessage(), e);
        }
    }
}