package ui.layout.left.display.components.container.testbox.test.light;

import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.OperationTargetHolder;
import sdk.entity.TestBoxDevice;
import ui.config.json.devices.light_test_box.LightTestBoxConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;
import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.io.File;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 15:20
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Slf4j
public class LightTestBoxContainer extends DeviceContainer {
    private final ClientView clientView;
    private final MainModel mainModel;
    private JTextField filePathField;
    private JLabel tipLabel;
    private static File lastDirectory = null; // 记录上次打开的文件夹路径
    private TestBoxDevice device;

    public LightTestBoxContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.device = (TestBoxDevice) device;
        createView();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        JPanel topPanel = new JPanel();
        JButton loadButton = new JButton("加载配置文件");
        JButton initConfigButton = new JButton("初始化配置");
        filePathField = new JTextField(50);
        tipLabel = new JLabel();

        loadButton.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();
            // 设置初始目录（优先使用上次目录）
            if (lastDirectory != null) {
                fileChooser.setCurrentDirectory(lastDirectory);
            }

            FileNameExtensionFilter filter = new FileNameExtensionFilter(
                    "Excel文件", "xlsx", "xls");
            fileChooser.setFileFilter(filter);
            int result = fileChooser.showOpenDialog(null);
            if (result == JFileChooser.APPROVE_OPTION) {
                File selectedFile = fileChooser.getSelectedFile();
                filePathField.setText(selectedFile.getAbsolutePath());
                lastDirectory = selectedFile.getParentFile();
                JsonResponse<String> res = OperationTargetHolder.getLightConfigKit().loadConfigFile(selectedFile.getAbsolutePath());
                if (res.isOk()) {
                    tipLabel.setText("测试箱配置文件已加载！");
                    log.info("测试箱配置文件加载成功: {}", selectedFile.getAbsolutePath());
                    mainModel.getTestBoxModel().onConfigLoadSuccess(selectedFile.getAbsolutePath());
                } else {
                    tipLabel.setText("测试箱配置加载失败：" + res.getMessage());
                    log.error("测试箱配置文件加载失败: {}", res.getMessage());
                    mainModel.getTestBoxModel().onConfigLoadFailed(res.getMessage());
                }
            }
        });
        initConfigButton.addActionListener(e -> {
            String filePath = filePathField.getText();
            if (filePath == null || filePath.isEmpty()) {
                SwingUtil.showWarningDialog(this, "请加载配置文件");
                return;
            }
            initConfig(filePath.toString());
        });
        topPanel.add(new JLabel("测试箱配置表路径:"));
        topPanel.add(filePathField);
        topPanel.add(loadButton);
        topPanel.add(initConfigButton);
        add(topPanel, BorderLayout.NORTH);
    }

    private void initConfig(String filePath) {
        new SwingWorker<OperationResult, Void>() {
            @Override
            protected OperationResult doInBackground() {
                JsonResponse<Map<String, DeviceConfig>> res = OperationTargetHolder.getLightConfigKit().parseConfig(filePath);
                Map<String, DeviceConfig> data = res.getData();
                if (!res.isOk() || data == null) return null;
                return device.setAllAcquisitionBoardCardInit();
            }
            @Override
            protected void done() {
                try {
                    OperationResult operationResult = get();
                    SwingUtilities.invokeLater(() -> {
                        if (operationResult != null && operationResult.isOk()) {
                            SwingUtil.showInformationDialog(LightTestBoxContainer.this, "测试箱配置文件已初始化！");
                        } else {
                            SwingUtil.showWarningDialog(LightTestBoxContainer.this, "测试箱配置初始化失败：" + operationResult.getMessage());
                        }
                    });
                } catch (InterruptedException | ExecutionException e) {
                    log.error("--测试箱配置初始化失败：{}", e.getMessage());
                }
            }
        }.execute();

    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        super.deviceConnected(device, autoOpenChannel);
        clientView.updateCollapseIconAndSetRightView(true);
        SwingUtil.windowMaximized(clientView);
        //默认读取配置文件的内容，界面显示配置记忆文件加载路径
        if (LightTestBoxConfig.getInstance().initLightTestBoxConfig()) {
            filePathField.setText(LightTestBoxConfig.getInstance().getLoadConfigExcelFile().getAbsolutePath());
        }
    }

    @Override
    public void deviceDisconnected(Device device) {
        super.deviceDisconnected(device);
    }

}
