package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.Setter;
import ui.callback.InsertTestCaseListener;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.KeyEvent;

public class BatchAddInitScriptDialog extends JDialog {
    private static BatchAddInitScriptDialog instance;
    private JTextArea textArea;
    private JCheckBox allCheckBox, partialCheckBox;
    private JSpinner startSpinner, endSpinner;
    private JButton confirmBtn;
    private final MainModel mainModel;
    @Setter
    private InsertTestCaseListener listener;

    private BatchAddInitScriptDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        initComponents();
    }

    public static BatchAddInitScriptDialog getInstance(MainModel mainModel) {
        if (instance == null) {
            instance = new BatchAddInitScriptDialog(mainModel);
        }
        return instance;
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setSize(500, 400);
        // 文本输入区
        textArea = new JTextArea(10, 40);
        add(new JScrollPane(textArea), BorderLayout.CENTER);
        // 复选框面板
        JPanel checkPanel = new JPanel(new GridLayout(2, 1));
        allCheckBox = new JCheckBox("全选");
        partialCheckBox = new JCheckBox("部分选");
        setupCheckBoxes();
        checkPanel.add(allCheckBox);
        checkPanel.add(partialCheckBox);
        // 行数选择器
        JPanel spinnerPanel = new JPanel();
        startSpinner = new JSpinner(new SpinnerNumberModel(1, 1, 1000, 1));
        endSpinner = new JSpinner(new SpinnerNumberModel(1, 1, 1000, 1));
        spinnerPanel.add(new JLabel("起始行:"));
        spinnerPanel.add(startSpinner);
        spinnerPanel.add(new JLabel("结束行:"));
        spinnerPanel.add(endSpinner);
        spinnerPanel.setVisible(false);
        // 控制面板
        JPanel controlPanel = new JPanel(new BorderLayout());
        confirmBtn = new JButton("确定");
        setupConfirmButton();
        controlPanel.add(checkPanel, BorderLayout.NORTH);
        controlPanel.add(spinnerPanel, BorderLayout.CENTER);
        controlPanel.add(confirmBtn, BorderLayout.SOUTH);
        add(controlPanel, BorderLayout.SOUTH);
        setLocationRelativeTo(null);
        setModal(true);
        setResizable(true);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        getRootPane().registerKeyboardAction(e -> dispose(),
                KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0),
                JComponent.WHEN_IN_FOCUSED_WINDOW);
    }

    private void setupCheckBoxes() {
        ItemListener checkListener = e -> {
            JCheckBox source = (JCheckBox) e.getSource();
            boolean selected = (e.getStateChange() == ItemEvent.SELECTED);

            if (source == allCheckBox && selected) {
                partialCheckBox.setSelected(false);
            } else if (source == partialCheckBox && selected) {
                allCheckBox.setSelected(false);
            }
            updateSpinnerVisibility();
        };
        allCheckBox.addItemListener(checkListener);
        partialCheckBox.addItemListener(checkListener);
    }

    private void updateSpinnerVisibility() {
        boolean show = partialCheckBox.isSelected();
        startSpinner.getParent().setVisible(show);
        revalidate();
    }

    private void setupConfirmButton() {
        confirmBtn.addActionListener(e -> {
            String scriptText = textArea.getText();
            InitScriptModel initScriptModel = new InitScriptModel();
            initScriptModel.setInitScripts(scriptText);
            initScriptModel.setSelectedAll(allCheckBox.isSelected());
            if (!allCheckBox.isSelected()) {
                int start = (int) startSpinner.getValue();
                int end = (int) endSpinner.getValue();
                initScriptModel.setStartRow(start);
                initScriptModel.setEndRow(end);
            }
            mainModel.getTestCaseTableModel().insertInitScripts(initScriptModel, listener);
        });
    }

}