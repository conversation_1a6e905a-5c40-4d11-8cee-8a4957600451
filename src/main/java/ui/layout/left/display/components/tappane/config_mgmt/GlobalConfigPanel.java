package ui.layout.left.display.components.tappane.config_mgmt;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.miginfocom.swing.MigLayout;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

@Slf4j
public class GlobalConfigPanel extends JPanel implements BaseView {
    private final MainModel mainModel;
    private final JPanel mainPanel;

    private final GlobalConfigUIHolder globalConfigUIHolder;

    @Data
    private class GlobalConfigUIHolder {
        private JSpinner userCoordinates;
        private JCheckBox autoLoadConstantWaveVoltage;
        private J<PERSON>heckBox recordPower;
        private JSpinner recordPowerDuration;
        @Getter
        private final GlobalConfig globalConfig;

        public GlobalConfigUIHolder(GlobalConfig globalConfig) {
            this.globalConfig = globalConfig;
        }

        public void init() {
            // 初始化组件状态
            userCoordinates.setValue(globalConfig.getUserCoordinates());
            autoLoadConstantWaveVoltage.setSelected(globalConfig.isAutoLoadConstantWaveVoltage());
            recordPower.setSelected(globalConfig.isRecordPower());
            recordPowerDuration.setValue(globalConfig.getRecordPowerDuration());

            // 更新全局配置
            updateGlobalConfig();

            // 监听 userCoordinates 的变化
            userCoordinates.addChangeListener(e -> {
                globalConfig.setUserCoordinates((Integer) userCoordinates.getValue());
                updateGlobalConfig();
            });

            // 监听 autoLoadConstantWaveVoltage 的变化
            autoLoadConstantWaveVoltage.addItemListener(e -> {
                globalConfig.setAutoLoadConstantWaveVoltage(autoLoadConstantWaveVoltage.isSelected());
                updateGlobalConfig();
            });

            // 监听 recordPower 复选框的变化
            recordPower.addItemListener(e -> {
                boolean isSelected = recordPower.isSelected();
                recordPowerDuration.setVisible(isSelected);
                globalConfig.setRecordPower(isSelected);
                if (isSelected) {
                    // 如果选中了复选框，确保记录时间也同步更新
                    globalConfig.setRecordPowerDuration((Integer) recordPowerDuration.getValue());
                }
                updateGlobalConfig();
                mainPanel.revalidate(); // 重新验证布局
                mainPanel.repaint();    // 重新绘制面板
            });

            // 监听 recordPowerDuration 的变化
            recordPowerDuration.addChangeListener(e -> {
                if (recordPower.isSelected()) {
                    globalConfig.setRecordPowerDuration((Integer) recordPowerDuration.getValue());
                    updateGlobalConfig();
                }
            });
        }
    }

    private void updateGlobalConfig() {
        OperationTargetHolder.getConfigKit().updateGlobalConfig(globalConfigUIHolder.getGlobalConfig());
    }

    public GlobalConfigPanel(MainModel mainModel) {
        this.mainModel = mainModel;
        mainPanel = new JPanel(new MigLayout("", "[][grow]", "[]10[]10[]10[]10")); // 使用MigLayout进行更灵活的布局
        GlobalConfig globalConfig = OperationTargetHolder.getConfigKit().loadGlobalConfig();
        globalConfig.setRecordPowerProject(mainModel.getAppInfo().getProject());
        globalConfigUIHolder = new GlobalConfigUIHolder(globalConfig);
        createView();
        restoreView();
        setLayout(new GridLayout(1, 1));
        add(mainPanel);
    }

    private <T extends JComponent> T createConfigRow(String label, T component, String layoutConstraint) {
        JLabel lbl = new JLabel(label + ":");
        mainPanel.add(lbl, "right"); // 标签居右对齐
        mainPanel.add(component, layoutConstraint != null ? layoutConstraint : "wrap");
        return component;
    }

    @Override
    public void createView() {
        globalConfigUIHolder.setUserCoordinates(createConfigRow("机械臂全局坐标系", new JSpinner(new SpinnerNumberModel(0, 0, 10, 1)), "span"));
        globalConfigUIHolder.setAutoLoadConstantWaveVoltage(createConfigRow("脉冲一个循环后是否自动加载恒压12V", new JCheckBox(), "span"));
        globalConfigUIHolder.setRecordPower(createConfigRow("监控电流/电压失败后，是否录制电流/电压数据", new JCheckBox(), "span"));

        // 创建录制时间的 JSpinner，但初始状态下根据 recordPower 的状态决定是否可见
        JSpinner recordPowerDurationSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 300, 1));
        globalConfigUIHolder.setRecordPowerDuration(createConfigRow("录制电流/电压数据时间(s)", recordPowerDurationSpinner, "span"));
        recordPowerDurationSpinner.setVisible(globalConfigUIHolder.getGlobalConfig().isRecordPower()); // 初始状态根据 recordPower 的状态决定
    }

    @Override
    public void restoreView() {
        globalConfigUIHolder.init();
    }
}