package ui.layout.left.display.components.container.testbox.test;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

import static ui.base.cosntants.TestBoxConstants.BORDER_COLOR;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/12 16:46
 * @description :
 * @modified By :
 * @since : 2023/6/12
 **/
@Getter
public class PanelGenerator {
    private final List<JPanel> controlList = new ArrayList<>();
    private JComponent component;
    private JPanel rootPanel;
    private static final int HORIZONTAL = 0;
    private static final int VERTICAL = 1;


    public JPanel generatePanel(int channels, String labelText, String title, int align, LayoutGenerator layoutGenerator) {
        CustomizeGridLayout layout = layoutGenerator.formLayout();
        if (align == VERTICAL) {
            rootPanel = new JPanel(new GridLayout(1, layout.getColumn(), 1, 1));
            JPanel panel = null;
            for (int channel = 0; channel < channels; channel++) {
                if (channel % layout.getRow() == 0) {
                    panel = new JPanel(new GridLayout(layout.getRow(), 1, 1, 2));
                    rootPanel.add(panel);
                }
                JPanel subPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
                JLabel label = new JLabel(String.format("%s%s", labelText, layoutGenerator.labelGenerator(channel)));
                label.setFont(new Font(null, Font.BOLD, 15));
                label.setForeground(Color.BLUE);
                subPanel.add(label);
                component = layoutGenerator.componentGenerator(channel);
                subPanel.add(component);
                panel.add(subPanel);
                controlList.add(subPanel);
            }
        } else {
            rootPanel = new JPanel(new GridLayout(layout.getRow(), layout.getColumn(), 1, 2));
            for (int channel = 0; channel < channels; channel++) {

                JPanel subPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
                JLabel label = new JLabel(String.format("%s%s", labelText, layoutGenerator.labelGenerator(channel)));
                label.setFont(new Font(null, Font.BOLD, 15));
                if ((channel / layout.getColumn()) % 2 == 0) {
                    label.setForeground(Color.BLUE);
                }else {
                    label.setForeground(BORDER_COLOR);
                }
                subPanel.add(label);
                component = layoutGenerator.componentGenerator(channel);
                subPanel.add(component);
                rootPanel.add(subPanel);
                controlList.add(subPanel);
            }
        }
        if (!StringUtils.isEmpty(title)) {
            TitledBorder titledBorder = new TitledBorder(BorderFactory.createLineBorder(Color.BLUE, 2),title);
            titledBorder.setTitleFont(new Font("微软雅黑", Font.BOLD, 16));
            titledBorder.setTitleColor(BORDER_COLOR);
            rootPanel.setBorder(titledBorder);
        }
        return rootPanel;
    }

    public static void main(String[] args) {
        JFrame frame = new JFrame();
        frame.setSize(600, 500);
        PanelGenerator generator = new PanelGenerator();
//        JPanel panel = generator.generatePanel(8, "继电器", "继电器设定", new LayoutGenerator() {
//            @Override
//            public CustomizeGridLayout formLayout() {
//                return new CustomizeGridLayout(1, 8);
//            }
//
//            @Override
//            public int labelGenerator(int channel) {
//                return channel + 1;
//            }
//
//            @Override
//            public JComponent componentGenerator() {
//                return new JToggleButton();
//            }
//        });
//        JPanel panel = generator.generatePanel(44, "", "电阻设定", new LayoutGenerator() {
//            @Override
//            public CustomizeGridLayout formLayout() {
//                return new CustomizeGridLayout(8, 6);
//            }
//
//            @Override
//            public String labelGenerator(int channel) {
//                return String.format("%d#%d", channel / 4 + 1, channel % 4 + 1);
//            }
//
//            @Override
//            public JComponent componentGenerator() {
//                return new JSpinner();
//            }
//        });
//        JPanel panel = generator.generatePanel(10, "输出", "PWM输出设定", new LayoutGenerator() {
//            @Override
//            public CustomizeGridLayout formLayout() {
//                return new CustomizeGridLayout(4, 3);
//            }
//
//            @Override
//            public String labelGenerator(int channel) {
//                return String.valueOf(channel + 1);
//            }
//
//            @Override
//            public JComponent componentGenerator() {
//                return new CompositePanel();
//            }
//        });


        PanelGenerator generator2 = new PanelGenerator();
//        frame.setContentPane(panel);
        frame.setVisible(true);
    }
}
