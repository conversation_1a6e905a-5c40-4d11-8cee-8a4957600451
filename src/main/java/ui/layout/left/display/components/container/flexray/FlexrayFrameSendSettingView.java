package ui.layout.left.display.components.container.flexray;

import com.alibaba.fastjson2.JSONArray;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.bus.FlexrayMessage;
import sdk.entity.CanDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Flexray发送设置界面
 */
public class FlexrayFrameSendSettingView extends DeviceControlPanel implements BaseView {

    private JTextField stateTextField;

    private JButton listSendButton; //TODO:后续支持列表发送
    private JButton stopSendButton;
    private JButton startTsMasterToScriptButton;
    private JButton stopTsMasterToScriptButton;
    private JButton checkFlexrayMessageByIdButton;
    private JButton checkFlexrayMessageByIdToScriptButton;
    private JButton addStopMessageToScriptButton;
    private JButton addSendMessageToScriptButton;

    private FlexrayFrameSendPanel flexrayFrameSendPanel;
    private CanDevice flexrayDevice;
    private final int channel;

    public FlexrayFrameSendSettingView(DeviceContainer deviceContainer,
                                       MainModel mainModel,
                                       int channel) {
        super(deviceContainer, mainModel);
        this.channel = channel;
        if (getDeviceContainer() != null) {
            flexrayDevice = (CanDevice) getDeviceContainer().getDevice();
        }
        if (!DeviceModel.Bus.VECTOR_CAN.equals(flexrayDevice.getDeviceModel())) {
            createView();
            createActions();
        }
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        //Canoe面板不显示里面的内容，周立功Can面板要显示
        if (!DeviceModel.Bus.VECTOR_CAN.equals(flexrayDevice.getDeviceModel())) {
            listSendButton.setEnabled(isDeviceConnected);
            stopSendButton.setEnabled(isDeviceConnected);
        }
    }

    @Override
    public Integer getChannel() {
        return channel;
    }

    private void setupButtonPanel(JPanel mainPanel) {
        JPanel buttonPanel = new JPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        startTsMasterToScriptButton = new JButton("添加[启动同星设备]到脚本");
        stopTsMasterToScriptButton = new JButton("添加[停止同星设备]到脚本");
        checkFlexrayMessageByIdButton = new JButton("通过ID检查接收FlexRay报文");
        checkFlexrayMessageByIdToScriptButton = new JButton("添加[通过ID检查接收FlexRay报文]到脚本");

        listSendButton = new JButton("发送报文");
        addSendMessageToScriptButton = new JButton("添加[发送报文]到脚本");

        stopSendButton = new JButton("停止发送");
        addStopMessageToScriptButton = new JButton("添加[停止发送]到脚本");
        buttonPanel.setLayout(new GridLayout(4, 4, 0, 0));

        buttonPanel.add(startTsMasterToScriptButton);
        buttonPanel.add(stopTsMasterToScriptButton);
        buttonPanel.add(checkFlexrayMessageByIdButton);
        buttonPanel.add(checkFlexrayMessageByIdToScriptButton);
        buttonPanel.add(listSendButton);
        buttonPanel.add(addSendMessageToScriptButton);
        buttonPanel.add(stopSendButton);
        buttonPanel.add(addStopMessageToScriptButton);
    }

    private void setupStatePanel(JPanel mainPanel) {
        JPanel statePanel = new JPanel();
        statePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        JLabel label = new JLabel("正在运行的报文id:"); //TODO: 定期轮询状态
        statePanel.add(label);
        stateTextField = new JTextField();
        stateTextField.setEditable(false);
        statePanel.add(stateTextField);
        mainPanel.add(statePanel, BorderLayout.CENTER);
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(0, 1, 0, 0));
        setBorder(new EmptyBorder(5, 5, 5, 5));

        //主布局
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout(10, 10));
        //帧发送布局
        flexrayFrameSendPanel = new FlexrayFrameSendPanel(getChannel());
        mainPanel.add(flexrayFrameSendPanel, BorderLayout.NORTH);
        //列表发送布局
//        setupListSendPanel(mainPanel);
        setupStatePanel(mainPanel);
        //按钮布局
        setupButtonPanel(mainPanel);
        //添加主布局
        add(mainPanel);
    }

    @Override
    public void createActions() {
        startTsMasterToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(flexrayDevice);
            operation.setOperationMethod(DeviceMethods.startTsMaster);
            getMainModel().getOperationModel().updateOperation(operation);
        });
        stopTsMasterToScriptButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(flexrayDevice);
            operation.setOperationMethod(DeviceMethods.stopTsMaster);
            getMainModel().getOperationModel().updateOperation(operation);
        });

        checkFlexrayMessageByIdButton.addActionListener(e -> {
            FlexrayMessage messageData = promptForFlexrayData();
            if (messageData != null) {
                flexrayDevice.readFlexrayDataByIdCycle(
                        messageData.getChannel(),
                        messageData);
            }
        });

        checkFlexrayMessageByIdToScriptButton.addActionListener(e -> {
            FlexrayMessage messageData = promptForFlexrayData();
            if (messageData != null) {
                Operation operation = Operation.buildOperation(flexrayDevice);
                operation.getOperationTarget().setChannel(messageData.getChannel());
                operation.setOperationMethod(DeviceMethods.readFlexrayDataByIdCycle);
                operation.setOperationObject(messageData);
                getMainModel().getOperationModel().updateOperation(operation);
            }
        });
        listSendButton.addActionListener(e -> {
            if (!flexrayFrameSendPanel.checkUserInput()) {
                return;
            }
            flexrayDevice.sendFlexrayMessage(getChannel(), flexrayFrameSendPanel.getCurrentFlexrayMessage());
            fetchRunningFlexrayMessage();
        });
        stopSendButton.addActionListener(e -> {
            selectRunningCanMessageAndStop();
            fetchRunningFlexrayMessage();
        });
        addSendMessageToScriptButton.addActionListener(e -> {
            FlexrayMessage message = flexrayFrameSendPanel.getCurrentFlexrayMessage();
            if (message != null) {
                Operation operation = Operation.buildOperation(flexrayDevice);
                operation.getOperationTarget().setChannel(getChannel());
                operation.setOperationMethod(DeviceMethods.sendFlexrayMessage);
                operation.setOperationObject(message);
                getMainModel().getOperationModel().updateOperation(operation);
            }
        });
        addStopMessageToScriptButton.addActionListener(e -> {
            String messageId = JOptionPane.showInputDialog("请输入一个要停止的十六进制报文id（不填任何数据表示停止所有报文）").trim();
            Operation operation = Operation.buildOperation(flexrayDevice);
            operation.getOperationTarget().setChannel(getChannel());
            if (messageId.isEmpty()) {
                operation.setOperationMethod(DeviceMethods.stopAllFlexrayMessage);
            } else {
                operation.setOperationMethod(DeviceMethods.stopFlexrayMessage);
                operation.setOperationObject(messageId);
            }
            getMainModel().getOperationModel().updateOperation(operation);
        });
    }

    private FlexrayMessage promptForFlexrayData() {
        JTextField channelField = new JTextField(5);
        JTextField slotIdField = new JTextField(5);
        JTextField cycleOffsetField = new JTextField(5);
        JTextField cycleRepetitionField = new JTextField(5);

        JPanel myPanel = new JPanel();
        myPanel.setLayout(new GridLayout(5, 2));

        myPanel.add(new JLabel("通道:"));
        myPanel.add(channelField);
        channelField.setEditable(false);
        channelField.setText(String.valueOf(getChannel()));

        myPanel.add(new JLabel("SlotID:0x"));
        myPanel.add(slotIdField);

        myPanel.add(new JLabel("Cycle Offset:"));
        myPanel.add(cycleOffsetField);

        myPanel.add(new JLabel("Cycle Repetition:"));
        myPanel.add(cycleRepetitionField);

        int result = JOptionPane.showConfirmDialog(null, myPanel,
                "请输入数据", JOptionPane.OK_CANCEL_OPTION);

        if (result == JOptionPane.OK_OPTION) {
            String channel = channelField.getText().trim();
            String slotId = slotIdField.getText().trim();
            String cycleOffset = cycleOffsetField.getText().trim();
            String cycleRepetition = cycleRepetitionField.getText().trim();

            if (!channel.isEmpty() && !slotId.isEmpty() && !cycleOffset.isEmpty() && !cycleRepetition.isEmpty()) {
                FlexrayMessage messageData = new FlexrayMessage();
                messageData.setChannel(getChannel());
                messageData.setSlotId(Integer.parseInt(slotId,16));
                messageData.setOffest(Integer.parseInt(cycleOffset));
                messageData.setRepetitior(Integer.parseInt(cycleRepetition));
                return messageData;
            }
        }
        return null;
    }

    private String[] toStringIds(List<String> runningCanMessage) {
        String[] strIds = new String[runningCanMessage.size()];
        for (int i = 0; i < strIds.length; i++) {
            strIds[i] = runningCanMessage.get(i);
        }
        return strIds;
    }

    private void selectRunningCanMessageAndStop() {
        String[] runningFlexrayMessage = fetchRunningFlexrayMessage();
        if (runningFlexrayMessage.length == 0) {
            SwingUtil.showWarningDialog(this, "当前没有在运行的报文");
        } else {
            List<String> messages = Arrays.stream(runningFlexrayMessage).collect(Collectors.toList());
            messages.add(0, "全部停止");
            runningFlexrayMessage = messages.toArray(new String[0]);
            int index = JOptionPane.showOptionDialog(this, "选择一个需要停止的CAN报文id", "报文停止",
                    JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, runningFlexrayMessage, runningFlexrayMessage[0]);
            if (index != JOptionPane.CLOSED_OPTION) {
                int channel = getChannel();
                if (index == 0) {
                    flexrayDevice.stopAllFlexrayMessage(channel);
                } else {
                    flexrayDevice.stopFlexrayMessage(channel, runningFlexrayMessage[index]);
                }
            }
        }
    }

    private String[] fetchRunningFlexrayMessage() {
        OperationResult result = flexrayDevice.fetchRunningFlexrayMessage(getChannel());
        if (result.isOk()) {
            JSONArray array = (JSONArray) result.getData();
            String[] ids = toStringIds(array.toJavaList(String.class));
            SwingUtil.invokeLater(() -> {
                stateTextField.setText(Arrays.toString(ids));
                stateTextField.invalidate();
                stateTextField.repaint();
            });
            return ids;
        }
        return new String[0];
    }


    public static void main(String[] args) {
//        System.out.println(Arrays.toString(hexToBytes("00 13")));
        JFrame frame = new JFrame();
        frame.setSize(900, 500);
        FlexrayFrameSendSettingView view = new FlexrayFrameSendSettingView(null, null, 0);
        frame.setContentPane(view);
        frame.setVisible(true);
    }
}
