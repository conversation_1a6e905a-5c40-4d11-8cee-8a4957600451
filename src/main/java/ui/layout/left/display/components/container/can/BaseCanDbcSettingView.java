package ui.layout.left.display.components.container.can;

import lombok.extern.slf4j.Slf4j;
import sdk.domain.bus.CanConfig;
import ui.layout.left.display.components.container.can.model.DbcPathModel;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class BaseCanDbcSettingView extends JPanel {
    private final String DEFAULT_DBC_PATH = System.getProperty("user.home") + File.separator + "Desktop";
    protected JPanel topPanel = new JPanel();
    ;
    protected JPanel centerPanel;
    protected final List<JTextField> pathFields = new ArrayList<>();
    protected final List<JButton> loadButtons = new ArrayList<>();
    protected final int channel;
    protected final CanConfig canConfig;
    protected final String deviceName;
    protected CanContainer canContainer;
    protected final DbcPathModel model;

    public BaseCanDbcSettingView(CanContainer canContainer, int channel, CanConfig canConfig, DbcPathModel model) {
        this.deviceName = canContainer.getDevice().getDeviceName();
        this.canContainer = canContainer;
        this.channel = channel;
        this.canConfig = canConfig;
        this.model = model;
        setLayout(new BorderLayout());
        centerPanel = new JPanel(new BorderLayout());
        initUI();
        add(centerPanel, BorderLayout.CENTER);
        model.addListener(this::updateUIComponents);
        revalidate();
        repaint();
    }

    public List<String> getDbcPaths() {
        return new ArrayList<>(model.getPaths());
    }

    /**
     * 初始化UI组件
     */
    private void initUI() {
        rebuildTopPanel(); // 构建顶部面板
    }

    /**
     * 更新UI组件（模型变更时触发）
     */
    public void updateUIComponents(List<String> paths) {
        SwingUtilities.invokeLater(() -> {
            topPanel.removeAll();
            model.setPaths(paths);
            rebuildTopPanel();    // 重新构建UI
            topPanel.revalidate();
            topPanel.repaint();
        });
    }

    /**
     * 构建顶部面板
     */
    private void rebuildTopPanel() {
        pathFields.clear();
        loadButtons.clear();
        topPanel.setLayout(new BoxLayout(topPanel, BoxLayout.Y_AXIS));

        // 构建首行（带操作按钮）
        topPanel.add(createFirstRow());

        // 构建其他行（普通样式）
        for (int i = 1; i < model.getPaths().size(); i++) {
            topPanel.add(createCommonRow(i));
        }


        add(topPanel, BorderLayout.NORTH);
    }

    /**
     * 创建首行（带操作按钮）
     */
    private JPanel createFirstRow() {
        JPanel firstRowPanel = new JPanel(new GridBagLayout());

        // 路径组件面板（左对齐）
        // 修改路径面板布局为GridBagLayout以获得更精确控制
        JPanel pathPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();

        // 标签占10%
        gbc.weightx = 0.02;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        pathPanel.add(new JLabel("1:"), gbc);

        // 按钮占10%
        gbc.weightx = 0.08;
        pathPanel.add(createLoadButton(0), gbc);

        // 文本框占90%
        gbc.weightx = 0.9;
        JTextField pathField = createPathField(0);
        pathField.setMaximumSize(new Dimension(Integer.MAX_VALUE, 40)); // 允许横向扩展
        pathPanel.add(pathField, gbc);

        // 操作按钮面板（右对齐）
        JPanel controlPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        controlPanel.add(createAddButton());
        controlPanel.add(createRemoveButton());

//        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // pathPanel占70%宽度
        gbc.weightx = 0.7;
        gbc.gridx = 0;
        firstRowPanel.add(pathPanel, gbc);

        // controlPanel占30%宽度
        gbc.weightx = 0.3;
        gbc.gridx = 1;
        firstRowPanel.add(controlPanel, gbc);

        return firstRowPanel;
    }

    /**
     * 创建普通行
     */
    private JPanel createCommonRow(int index) {
        JPanel rowPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 2));
        rowPanel.add(new JLabel((index + 1) + ":"));
        rowPanel.add(createLoadButton(index));
        rowPanel.add(createPathField(index));
        return rowPanel;
    }

    /**
     * 创建加载按钮
     */
    private JButton createLoadButton(int index) {
        JButton loadButton = new JButton("加载DBC");
        loadButton.setPreferredSize(new Dimension(120, 40));
        loadButton.addActionListener(e -> {
            String path = loadDbcPath(index);
            if (path != null && !path.isEmpty()) {
                updateModelPath(index, path);
                onLoadDbcClicked(index, path);
            }
        });
        loadButtons.add(loadButton);
        return loadButton;
    }

    /**
     * 创建路径输入框
     */
    private JTextField createPathField(int index) {
        String path;
        if (index == 0 && model.getPaths().isEmpty()) {
            path = "";
        } else {
            path = model.getPaths().get(index);
        }
        JTextField pathField = new JTextField(path, 50);
        pathField.setPreferredSize(new Dimension(2000, 40));
        pathField.getDocument().addDocumentListener(new DocumentAdapter(() ->
                updateModelPath(index, pathField.getText())
        ));
        // 关键修改：设置为不可编辑
        pathField.setEditable(false);
        pathFields.add(pathField);
        return pathField;
    }

    /**
     * 创建添加按钮
     */
    private JButton createAddButton() {
        JButton addButton = new JButton("+");
        addButton.setPreferredSize(new Dimension(40, 40));
        addButton.addActionListener(e -> model.setPaths(addPath()));
        return addButton;
    }

    /**
     * 创建删除按钮
     */
    private JButton createRemoveButton() {
        JButton removeButton = new JButton("-");
        removeButton.setPreferredSize(new Dimension(40, 40));
        removeButton.setEnabled(model.getPaths().size() > 1); // 动态禁用
        removeButton.addActionListener(e -> model.setPaths(removePath()));
        return removeButton;
    }

    /**
     * 更新模型中的路径
     */
    private void updateModelPath(int index, String path) {
        List<String> newPaths = new ArrayList<>(model.getPaths());
        if (newPaths.size() > index) {
            newPaths.set(index, path);
        } else {
            newPaths.add(path);
        }
        model.setPaths(newPaths);
    }

    /**
     * 添加新路径
     */
    private List<String> addPath() {
        List<String> newPaths = new ArrayList<>(model.getPaths());
        newPaths.add("");
        SwingUtilities.invokeLater(() ->
                pathFields.get(pathFields.size() - 1).requestFocus()
        );
        return newPaths;
    }

    /**
     * 移除最后一个路径
     */
    private List<String> removePath() {
        List<String> newPaths = new ArrayList<>(model.getPaths());
        if (!newPaths.isEmpty()) {
            int lastIndex = newPaths.size() - 1;
            newPaths.remove(lastIndex);
            // 同步删除对应的 pathFields 和 loadButtons 数据
            if (lastIndex < pathFields.size()) {
                pathFields.remove(lastIndex);
            }
            if (lastIndex < loadButtons.size()) {
                loadButtons.remove(lastIndex);
            }
            model.setPaths(newPaths);
            removeLastDbc(lastIndex);
        }
        return newPaths;
    }

    public String loadDbcPath(int index) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new FileNameExtensionFilter("dbc Files", "dbc")); // 设置扩展过滤器

        List<String> specifiedFilePaths = getDbcPaths();
        File initialDir = null;

        if (specifiedFilePaths != null && !specifiedFilePaths.isEmpty()
                && index < getDbcPaths().size() && !specifiedFilePaths.get(index).trim().isEmpty()) {
            File specifiedFile = new File(specifiedFilePaths.get(index));
            if (specifiedFile.exists()) {
                File parentDir = specifiedFile.getParentFile();
                if (parentDir != null && parentDir.exists()) {
                    initialDir = parentDir;
                }
            }
        }

        if (initialDir != null) {
            fileChooser.setCurrentDirectory(initialDir);
        } else {
            File defaultDir = new File(DEFAULT_DBC_PATH);
            if (defaultDir.exists()) {
                fileChooser.setCurrentDirectory(defaultDir);
            } else {
                log.warn("指定的DBC路径和桌面路径都不存在。请手动选择DBC文件。");
            }
        }

        int result = fileChooser.showOpenDialog(null);
        File selectedFile = (result == JFileChooser.APPROVE_OPTION)
                ? fileChooser.getSelectedFile() : null;
        return selectedFile != null ? selectedFile.getAbsolutePath() : null;
    }

    /**
     * 加载按钮点击事件（由子类实现）
     */
    protected abstract void onLoadDbcClicked(int index, String path);

    protected abstract void removeLastDbc(int index);

    /**
     * DocumentAdapter工具类（简化DocumentListener实现）
     */
    private static class DocumentAdapter implements DocumentListener {
        private final Runnable callback;

        DocumentAdapter(Runnable callback) {
            this.callback = callback;
        }

        @Override
        public void insertUpdate(DocumentEvent e) {
            callback.run();
        }

        @Override
        public void removeUpdate(DocumentEvent e) {
            callback.run();
        }

        @Override
        public void changedUpdate(DocumentEvent e) {
            callback.run();
        }
    }
}
