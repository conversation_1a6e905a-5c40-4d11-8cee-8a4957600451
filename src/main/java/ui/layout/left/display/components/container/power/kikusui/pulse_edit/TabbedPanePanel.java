package ui.layout.left.display.components.container.power.kikusui.pulse_edit;

import lombok.Getter;
import ui.base.OperationAssembler;
import ui.layout.left.display.components.container.power.kikusui.entity.KikusuiWavePulse;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

public class TabbedPanePanel extends JPanel {
    @Getter
    private final JPanel startPulsePanel;
    @Getter
    private final JPanel timeLevelResetPulsePanel;
    @Getter
    private final JPanel voltageLevelResetPulsePanel;
    private final JPanel powerTemporarilyInterruptPanel;
    private final JPanel normalTypePanelView;
    private final JPanel arbitraryResetPulsePanel;
    private final JTabbedPane tabbedPane;

    public TabbedPanePanel(MainModel mainModel) {
        tabbedPane = new JTabbedPane();
        normalTypePanelView = new NormalPulseTypePanel(mainModel);
        startPulsePanel = new StartPulsePanel();
        arbitraryResetPulsePanel = new ArbitraryResetPulsePanel();
        timeLevelResetPulsePanel = new TimeLevelResetPulsePanel();
        voltageLevelResetPulsePanel = new VoltageLevelResetPulsePanel();
        powerTemporarilyInterruptPanel = new PowerTemporarilyInterruptPulsePanel();
        tabbedPane.addTab("通用类型脉冲参数设置", normalTypePanelView);
        tabbedPane.addTab("启动脉冲参数设置", startPulsePanel);
        tabbedPane.addTab("时间级复位脉冲参数设置", timeLevelResetPulsePanel);
        tabbedPane.addTab("电压级复位脉冲参数设置", voltageLevelResetPulsePanel);
        tabbedPane.addTab("任意复位脉冲参数设置", arbitraryResetPulsePanel);
        tabbedPane.addTab("电源短暂中断脉冲参数设置", powerTemporarilyInterruptPanel);
        setLayout(new GridLayout(1, 1, 5, 5));
        add(tabbedPane, BorderLayout.CENTER);
    }

    /**
     * 获取当前显示面板
     *
     * @return 当前显示面板
     */
    @SuppressWarnings("unchecked")
    public OperationAssembler<KikusuiWavePulse> getCurrentWavePulseParser() {
        return (OperationAssembler<KikusuiWavePulse>) tabbedPane.getSelectedComponent();
    }

}
