package ui.layout.left.display.components.container.can.model;

import lombok.Data;

@Data
public class CanFrameDetail {
    private String orderNum;//序号
    private boolean check;
    private String state;//状态
    private String id;//"ID(0x)
    private String protocol;//协议
    private Integer length;//长度
    private String name;//名称
    private String data;//数据
    private String frameType;//帧类型
    private Integer framesPerSend;//每次发送帧数
    private Integer sendCycle;//发送次数
    private Integer everyInterval;//每次间隔(ms)
    private String sendMode;
}
