package ui.layout.left.display.components.container.testbox.test;

import javax.swing.*;
import javax.swing.plaf.basic.BasicSliderUI;
import java.awt.*;

public class CustomSliderUI extends BasicSliderUI {

    public CustomSliderUI(JSlider slider) {
        super(slider);
    }

    @Override
    public void paintTrack(Graphics g) {
        Graphics2D g2d = (Graphics2D) g;
        g2d.setPaint(new Color(0xC6D4E2));  // 设置轨道颜色
        g2d.fillRect(trackRect.x, trackRect.y + trackRect.height / 2 - 5, trackRect.width,
                trackRect.height / 2 + 10);  // 设置轨道高度

        g2d.setPaint(new Color(0x98F6DC));  // 设置滑块颜色
//        g2d.fillRect(thumbRect.x, thumbRect.y, thumbRect.width, thumbRect.height);
        g2d.fillPolygon(new int[]{thumbRect.x + 2, thumbRect.x + thumbRect.width / 2, thumbRect.x + thumbRect.width - 2},
                new int[]{thumbRect.y + thumbRect.height, thumbRect.y, thumbRect.y + thumbRect.height}, 3);  // 设置滑块形状为三角形

        g2d.dispose();
    }

    @Override
    public void paintThumb(Graphics g) {
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setPaint(new Color(0xC0C0C1));  // 设置滑块颜色
        g2d.fillPolygon(new int[]{thumbRect.x + 2, thumbRect.x + thumbRect.width / 2, thumbRect.x + thumbRect.width - 2},
                new int[]{thumbRect.y + thumbRect.height, thumbRect.y, thumbRect.y + thumbRect.height}, 10);  // 设置滑块形状为三角形

        g2d.dispose();
    }
}