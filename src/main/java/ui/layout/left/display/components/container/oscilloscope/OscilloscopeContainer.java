package ui.layout.left.display.components.container.oscilloscope;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;

public class OscilloscopeContainer extends DeviceContainer {



    public OscilloscopeContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        createView();
        createActions();
    }


    @Override
    public void createView() {
        super.createView();

    }

}
