package ui.layout.left.display.components.tappane.upgrade_mgmt;

import lombok.Getter;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

public class UpgradeObjectPanel extends JPanel implements BaseView {
    private final ScreenConfig screenConfig;
    private final MainModel mainModel;
    @Getter
    private final DisplayUpgradePanel displayUpgradePanel;
    private final ClientView clientView;

    public UpgradeObjectPanel(ClientView clientView, MainModel mainModel) {
        this.clientView = clientView;
        this.mainModel = mainModel;
        screenConfig = OperationTargetHolder.getScreenKit().loadConfig(mainModel.getAppInfo().getProject());
        displayUpgradePanel = new DisplayUpgradePanel(clientView, mainModel, screenConfig);
        createView();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout(0, 0));
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.addTab("Display升级", displayUpgradePanel);
        add(tabbedPane);
    }

}
