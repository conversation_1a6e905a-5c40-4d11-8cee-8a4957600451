package ui.layout.left.display.components.container.screen.touchPoint;

import common.utils.StringUtils;
import lombok.Data;
import lombok.Getter;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.ScreenKit;
import ui.base.BaseView;
import ui.base.Filters;
import ui.base.table.rowHeader.RowHeaderTable;
import ui.layout.left.display.components.container.serial.CheckSumConstant;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.text.AbstractDocument;
import java.awt.*;

/**
 * 触摸报点协议设置面板
 */
public class TouchPointProtocolSettingsPanel extends JPanel implements BaseView {

    @Data
    private static class ProtocolCommandDisplay {
        private String header = "";
        private String data = "";
        private String checksum = "";

        @Override
        public String toString() {
            return String.format("%s %s %s", header.trim(), data.trim(), checksum).trim();
        }
    }

    @Getter
    private final TouchMatrixTable touchMatrixTable;
    private final JTextField protocolHeaderTextField;
    //    private final JSpinner orderLengthSpinner;
//    private final JSpinner dataLengthSpinner;
    private final JSpinner dataLengthSpinner;
    private final JSpinner pressLengthSpinner;
    private final JSpinner releaseLengthSpinner;
    private final JCheckBox lengthDiffCheckBox;
    private final JPanel diffLengthPanel;
    private final JComboBox<String> protocolCheckSumAlgorithmComboBox;
    private final JTextField protocolCommandDisplayTextField;
    private final ScreenConfig screenConfig;
    private final ProtocolCommandDisplay protocolCommandDisplay;
    private final JCheckBox ignoreMiddle;

    private final ScreenKit screenKitManager = OperationTargetHolder.getScreenKit();

    public TouchPointProtocolSettingsPanel(MainModel mainModel, ScreenConfig screenConfig) {
        this.screenConfig = screenConfig;
        protocolCommandDisplay = new ProtocolCommandDisplay();
        touchMatrixTable = new TouchMatrixTable(screenConfig);

        // TODO: 可以考虑改成十六进制输入
        protocolCommandDisplayTextField = new JTextField();
        protocolCommandDisplayTextField.setEditable(false);
        touchMatrixTable.setDataChangeListener(hexString -> {
            protocolCommandDisplay.setData(hexString);
            protocolCommandDisplayTextField.setText(String.valueOf(protocolCommandDisplay));
        });
        protocolHeaderTextField = new JTextField(20);
        protocolCheckSumAlgorithmComboBox = new JComboBox<>(new String[]{CheckSumConstant.ADD8, CheckSumConstant.ADD16, CheckSumConstant.ANY_BYTE1, CheckSumConstant.ANY_BYTE2});
//        orderLengthSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 1000, 1));
//        dataLengthSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 1000, 1));
        lengthDiffCheckBox = new JCheckBox("事件长度不同:");
        dataLengthSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 1000, 1));
        pressLengthSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 1000, 1));
        releaseLengthSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 1000, 1));
        diffLengthPanel = new JPanel();
        ignoreMiddle = new JCheckBox("忽略中间事件");
        SwingUtil.setPreferredWidth(dataLengthSpinner, 80);
        createView();
        restoreView();
        createActions();
        protocolCommandDisplayTextField.setText(String.valueOf(protocolCommandDisplay));
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        topPanel.add(new JLabel("固定协议头十六进制:"));
        ((AbstractDocument) protocolHeaderTextField.getDocument()).setDocumentFilter(new Filters.BlankHexFilter());
        topPanel.add(protocolHeaderTextField);

//        topPanel.add(new JLabel("报点命令长度(Order Length):"));
//        topPanel.add(orderLengthSpinner);
//        topPanel.add(new JLabel("报点数据长度(Data Length):"));
//        topPanel.add(dataLengthSpinner);

        topPanel.add(new JLabel("最长可变数据协议长度:"));
        topPanel.add(dataLengthSpinner);
        JPanel diffLengthPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        diffLengthPanel.add(lengthDiffCheckBox);
        diffLengthPanel.add(new JLabel("按下事件长度"));
        diffLengthPanel.add(pressLengthSpinner);
        diffLengthPanel.add(new JLabel("释放事件长度"));
        diffLengthPanel.add(releaseLengthSpinner);
        diffLengthPanel.add(ignoreMiddle);
        pressLengthSpinner.setEnabled(false);
        releaseLengthSpinner.setEnabled(false);
        topPanel.add(new JLabel("checksum算法:"));
        topPanel.add(protocolCheckSumAlgorithmComboBox);
        JPanel pointSettings = new JPanel(new GridLayout(2, 1));
        pointSettings.add(topPanel);
        pointSettings.add(diffLengthPanel);
        JPanel centerPanel = new JPanel(new BorderLayout());
        JLabel topLabel = new JLabel("                    Bit");
        JLabel leftLabel = new JLabel("Byte");
        leftLabel.setVerticalAlignment(SwingConstants.TOP);
        RowHeaderTable rowHeaderTable = new RowHeaderTable(touchMatrixTable, 0);
        JScrollPane scrollPane = new JScrollPane(touchMatrixTable);
        scrollPane.setRowHeaderView(rowHeaderTable);
        centerPanel.add(topLabel, BorderLayout.NORTH);
        centerPanel.add(scrollPane, BorderLayout.CENTER);
        centerPanel.add(leftLabel, BorderLayout.WEST);
        centerPanel.setPreferredSize(new Dimension(800, 640));
        add(pointSettings, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        Box box = Box.createHorizontalBox();
        box.add(new JLabel("命令参考:"));
        box.add(protocolCommandDisplayTextField);
        add(box, BorderLayout.SOUTH);
        setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)),"报点协议配置" , TitledBorder.LEADING, TitledBorder.TOP, null, null));

    }

    @Override
    public void restoreView() {
        if (screenConfig != null) {
//            orderLengthSpinner.setValue(screenConfig.getTouchPointMatrix().getOrderLength());
//            dataLengthSpinner.setValue(screenConfig.getTouchPointMatrix().getDataLength());
            protocolHeaderTextField.setText(screenConfig.getTouchPointMatrix().getProtocolHeader());
            if (screenConfig.getTouchPointMatrix().getCheckSumAlgorithm() == null ||
                    StringUtils.isBlank(screenConfig.getTouchPointMatrix().getCheckSumAlgorithm())) {
                protocolCheckSumAlgorithmComboBox.setSelectedIndex(0);
            } else {
                protocolCheckSumAlgorithmComboBox.setSelectedItem(screenConfig.getTouchPointMatrix().getCheckSumAlgorithm());
            }
            int dataLength = screenConfig.getTouchPointMatrix().getDataLength();
            int pressLength = screenConfig.getTouchPointMatrix().getPressLength();
            int releaseLength = screenConfig.getTouchPointMatrix().getReleaseLength();
            dataLengthSpinner.setValue(dataLength);
            pressLengthSpinner.setValue(pressLength);
            releaseLengthSpinner.setValue(releaseLength);
            touchMatrixTable.changeDataLength(dataLength);
            touchMatrixTable.restoreView();
        }
        protocolCommandDisplay.setChecksum(String.format("CS(%s)", protocolCheckSumAlgorithmComboBox.getSelectedItem()));
        protocolCommandDisplay.setHeader(protocolHeaderTextField.getText());
    }

    @Override
    public void createActions() {
//        orderLengthSpinner.addChangeListener(e -> {
//            //更新命令长度配置
//            screenConfig.getTouchPointMatrix().setOrderLength((Integer) orderLengthSpinner.getValue());
//            screenManager.updateConfig(screenConfig);
//        });
//        dataLengthSpinner.addChangeListener(e -> {
//            //更新数据长度配置
//            screenConfig.getTouchPointMatrix().setDataLength((Integer) dataLengthSpinner.getValue());
//            screenManager.updateConfig(screenConfig);
//        });
        lengthDiffCheckBox.addActionListener(e -> {
            if (lengthDiffCheckBox.isSelected()) {
                pressLengthSpinner.setEnabled(true);
                releaseLengthSpinner.setEnabled(true);
            } else {
                pressLengthSpinner.setEnabled(false);
                releaseLengthSpinner.setEnabled(false);
            }
        });
        ignoreMiddle.addActionListener(e -> {
            //更新忽略中间点配置
            screenConfig.getTouchPointMatrix().setIgnoreMiddle(ignoreMiddle.isSelected());
            screenKitManager.updateConfig(screenConfig);
        });
        protocolCheckSumAlgorithmComboBox.addItemListener(e -> {
            protocolCommandDisplay.setChecksum(String.format("CS(%s)", protocolCheckSumAlgorithmComboBox.getSelectedItem()));
            protocolCommandDisplayTextField.setText(String.valueOf(protocolCommandDisplay));
            screenConfig.getTouchPointMatrix().setCheckSumAlgorithm((String) protocolCheckSumAlgorithmComboBox.getSelectedItem());
            screenKitManager.updateConfig(screenConfig);
        });
        dataLengthSpinner.addChangeListener(e -> {
            int dataLength = (Integer) dataLengthSpinner.getValue();
            touchMatrixTable.changeDataLength(dataLength);
            //更新协议长度配置
            screenConfig.getTouchPointMatrix().setDataLength(dataLength);
            screenKitManager.updateConfig(screenConfig);
        });
        pressLengthSpinner.addChangeListener(e -> {
            int pressLength = (Integer) pressLengthSpinner.getValue();
            screenConfig.getTouchPointMatrix().setPressLength(pressLength);
            screenKitManager.updateConfig(screenConfig);
        });
        releaseLengthSpinner.addChangeListener(e -> {
            int releaseLength = (Integer) releaseLengthSpinner.getValue();
            screenConfig.getTouchPointMatrix().setReleaseLength(releaseLength);
            screenKitManager.updateConfig(screenConfig);
        });
        protocolHeaderTextField.getDocument().addDocumentListener(new DocumentListener() {
            private void update() {
                protocolCommandDisplay.setHeader(protocolHeaderTextField.getText());
                protocolCommandDisplayTextField.setText(String.valueOf(protocolCommandDisplay));
                screenConfig.getTouchPointMatrix().setProtocolHeader(protocolHeaderTextField.getText().trim());
                screenKitManager.updateConfig(screenConfig);
            }

            @Override
            public void insertUpdate(DocumentEvent e) {
                update();
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                update();
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                update();
            }
        });

    }
}
