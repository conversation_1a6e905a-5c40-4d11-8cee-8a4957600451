package ui.layout.left.display.components.container.can;

import sdk.domain.screen.ScreenConfig;
import ui.base.BaseView;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: CAN报点检测按钮面板
 * @date: 2024/1/24 11:02
 * @version: 1.0
 */
public class CanPointCheckButtonPanel extends JPanel implements BaseView {
    private final CanPointCheckSettingsPanel keepPointCheckSettingsPanel;
    private final CanPointCheckSettingsPanel releasePointCheckSettingsPanel;

    public enum TouchType {
        TOUCH, PRESS, RELEASE
    }

    public CanPointCheckButtonPanel(CanContainer canContainer, MainModel mainModel, ScreenConfig screenConfig) {
        keepPointCheckSettingsPanel = new CanPointCheckSettingsPanel(canContainer, mainModel, screenConfig, "按下报点检测", TouchType.PRESS);
        keepPointCheckSettingsPanel.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "按下报点检测", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        releasePointCheckSettingsPanel = new CanPointCheckSettingsPanel(canContainer, mainModel, screenConfig, "释放报点检测", TouchType.RELEASE);
        releasePointCheckSettingsPanel.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "释放报点检测", TitledBorder.LEADING, TitledBorder.TOP, null, null));

        createView();
        restoreView();

    }

    public void createView() {
        setLayout(new GridLayout(3, 1, 5, 5));
        setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "报点检测", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        //add(touchPointCheckSettingsPanel);
        add(keepPointCheckSettingsPanel);
        add(releasePointCheckSettingsPanel);
    }


}
