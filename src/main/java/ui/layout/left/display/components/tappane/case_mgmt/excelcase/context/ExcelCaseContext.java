package ui.layout.left.display.components.tappane.case_mgmt.excelcase.context;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ExcelCaseContext extends CaseContext {
    private List<ExcelCaseTable> excelCaseTableList;
    private boolean pressureMode;
    private int sumTestCycles;
    private int rowExecuteIntervalTime;
    private boolean smokingTest;
    private boolean simulated;
}
