package ui.layout.left.display.components.container.speaker;

import lombok.Getter;
import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;

import java.awt.*;

/**
 * 声音输出控制界面
 */
@Getter
public class SpeakerContainer extends DeviceContainer {

    private final SpeakerControlPanel speakerControlPanel;

    public SpeakerContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        speakerControlPanel = new SpeakerControlPanel(device, mainModel);
        createView();
    }

    @Override
    public void createView() {
        super.createView();
        add(speakerControlPanel, BorderLayout.SOUTH);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
    }

}
