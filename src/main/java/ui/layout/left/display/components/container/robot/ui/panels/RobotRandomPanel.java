package ui.layout.left.display.components.container.robot.ui.panels;

import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.robot.MoveEntity;
import sdk.entity.RobotDevice;
import ui.base.BaseView;
import ui.config.json.devices.robot.RobotConfig;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.robot.RobotExceptions;
import ui.layout.left.display.components.container.robot.ui.RobotContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 机械臂随机点击面板
 */
public class RobotRandomPanel extends JPanel implements BaseView {

    private final JLabel operationDescLabel;
    private final JButton cancelButton;
    private final JButton randomTouchButton;
    private final JButton addToScriptButtonForRandomTouch;
    private final JButton addToScriptButtonForRandomTouchPoint;
    private final JButton centerTouchButton;
    private final JButton addToScriptButtonForCenterTouch;
    private String lastCoordinateName;
    private final List<String> robotCoordinatesRandomList = new ArrayList<>();
    private final DeviceContainer deviceContainer;
    private final RobotDevice robotDevice;
    private final MainModel mainModel;

    public RobotRandomPanel(MainModel mainModel, DeviceContainer deviceContainer, RobotConfig robotConfig) {
        boolean touchCheckMode = false;
        this.deviceContainer = deviceContainer;
        this.mainModel = mainModel;
        robotDevice = (RobotDevice) deviceContainer.getDevice();
        operationDescLabel = new JLabel();
        operationDescLabel.setText("    ");
        cancelButton = new JButton("撤销");
        randomTouchButton = SwingUtil.getDebugButton();

        addToScriptButtonForRandomTouch = SwingUtil.getAddToScriptButton();
        addToScriptButtonForRandomTouchPoint = SwingUtil.getAddToScriptButton("随机点击报点检测");

        centerTouchButton = SwingUtil.getDebugButton();
        addToScriptButtonForCenterTouch = SwingUtil.getAddToScriptButton();
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new BoxLayout(this, BoxLayout.X_AXIS));
        add(Box.createHorizontalStrut(6));
        add(new JLabel("随机范围:"));
        add(operationDescLabel);
        add(cancelButton);
        add(new JLabel("随机点击:   "));
        add(randomTouchButton);
        add(addToScriptButtonForRandomTouch);
        add(addToScriptButtonForRandomTouchPoint);
        add(new JLabel("点击中心点:   "));
        add(centerTouchButton);
        add(addToScriptButtonForCenterTouch);
        add(Box.createHorizontalGlue());
        setBorder(BorderFactory.createLineBorder(Color.black));
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        cancelButton.setEnabled(isDeviceConnected);
        randomTouchButton.setEnabled(isDeviceConnected);
    }

    @Override

    public void createActions() {
        cancelButton.addActionListener(e -> undoRandomOperation());
        randomTouchButton.addActionListener(e -> randomOrCenterTouchOperation("random"));
        addToScriptButtonForRandomTouch.addActionListener(e -> addToScriptButtonForRandomTouch());
        addToScriptButtonForRandomTouchPoint.addActionListener(e -> addToScriptButtonForRandomTouchPoint());
        centerTouchButton.addActionListener(e -> randomOrCenterTouchOperation(""));
        addToScriptButtonForCenterTouch.addActionListener(e -> addToScriptButtonForCenterTouch());

    }

    private void addToScriptButtonForCenterTouch() {
        if (robotCoordinatesRandomList.size() < 2) {
            SwingUtil.showWarningDialog(RobotRandomPanel.this, "请先设置点击范围");
        } else {
            Operation operation = Operation.buildOperation(robotDevice);
            operation.setOperationMethod(DeviceMethods.centerTouchByName);
            operation.setOperationObject(robotCoordinatesRandomList);
            mainModel.getOperationModel().updateOperation(operation);
        }
    }

    private void addToScriptButtonForRandomTouchPoint() {
        if (robotCoordinatesRandomList.size() < 2) {
            SwingUtil.showWarningDialog(RobotRandomPanel.this, "请先设置点击范围");
        } else {
            Operation operation = Operation.buildOperation(robotDevice);
            operation.setOperationMethod(DeviceMethods.randomAndCheckTouchPoint);
            operation.setOperationObject(robotCoordinatesRandomList);
            deviceContainer.getMainModel().getOperationModel().updateOperation(operation);
        }
    }

    private void addToScriptButtonForRandomTouch() {
        if (robotCoordinatesRandomList.size() < 2) {
            SwingUtil.showWarningDialog(RobotRandomPanel.this, "请先设置点击范围");
        } else {
            Operation operation = Operation.buildOperation(robotDevice);
            operation.setOperationMethod(DeviceMethods.randomTouchByName);
            operation.setOperationObject(robotCoordinatesRandomList);
            mainModel.getOperationModel().updateOperation(operation);
        }
    }

    private void randomOrCenterTouchOperation(String type) {
        if (robotCoordinatesRandomList.size() < 2) {
            SwingUtil.showWarningDialog(RobotRandomPanel.this, "请先设置点击范围");
        } else {
            MoveEntity moveEntity = new MoveEntity();
            boolean goAhead = true;
            double minX = Double.MAX_VALUE;
            double minY = Double.MAX_VALUE;
            double maxX = Double.MIN_VALUE;
            double maxY = Double.MIN_VALUE;
            double minZ = Double.MAX_VALUE;
            double minR = Double.MAX_VALUE;
            for (String name : robotCoordinatesRandomList) {
                try {
                    double x = ((RobotContainer) deviceContainer).getRobotOperationPanel()
                            .getRobotActionPanel().getRobotCoordinatesTable().getMovLEntity(name).getX();
                    double y = ((RobotContainer) deviceContainer).getRobotOperationPanel()
                            .getRobotActionPanel().getRobotCoordinatesTable().getMovLEntity(name).getY();
                    double z = ((RobotContainer) deviceContainer).getRobotOperationPanel()
                            .getRobotActionPanel().getRobotCoordinatesTable().getMovLEntity(name).getZ();
                    double r = ((RobotContainer) deviceContainer).getRobotOperationPanel()
                            .getRobotActionPanel().getRobotCoordinatesTable().getMovLEntity(name).getR();
                    minX = Math.min(minX, x);
                    minY = Math.min(minY, y);
                    minZ = Math.min(minZ, z);
                    minR = Math.min(minR, r);
                    maxX = Math.max(maxX, x);
                    maxY = Math.max(maxY, y);
                } catch (RobotExceptions.RobotCoordinateNamesNotFoundException ex) {
                    goAhead = false;
                    SwingUtil.showWarningDialog(RobotRandomPanel.this, ex.getMessage());
                    break;
                }
            }
            double width = Math.abs(maxX - minX);
            double height = Math.abs(maxY - minY);
            Rectangle2D.Double rectangle = new Rectangle2D.Double(minX, minY, width, height);
            double touchX;
            double touchY;
            if (type.equals("random")) {
                // 随机生成点在矩形范围内
                Random random = new Random();
                touchX = rectangle.x + random.nextDouble() * rectangle.width;
                touchY = rectangle.y + random.nextDouble() * rectangle.height;
            } else {
                //中间点
                touchX = rectangle.getCenterX();
                touchY = rectangle.getCenterY();
            }
            moveEntity.setX(touchX);
            moveEntity.setY(touchY);
            moveEntity.setZ(minZ);
            moveEntity.setR(minR);
            if (goAhead) {
                OperationResult operationResult = robotDevice.touch(moveEntity);
                if (operationResult.isFailed()) {
                    SwingUtil.showWebMessageDialog(RobotRandomPanel.this, operationResult.getMessage());
                }
            }
        }
    }

    private void undoRandomOperation() {
        if (robotCoordinatesRandomList.isEmpty()) {
            SwingUtil.showWarningDialog(RobotRandomPanel.this, "无操作可撤销");
        } else {
            removeRandom();
        }
    }


    /**
     * 更新随机点击操作描述
     */
    private void updateOperationDesc() {
        String suffix = "-->";
        StringBuilder sb = new StringBuilder(!robotCoordinatesRandomList.isEmpty() ? "从 " : "");

        for (String robotCoordinates : robotCoordinatesRandomList) {
            sb.append(robotCoordinates).append(suffix);
        }

        String desc = sb.toString();
        if (desc.endsWith(suffix)) {
            desc = desc.substring(0, desc.length() - suffix.length());
        }
        operationDescLabel.setText(desc);
    }

    public void leftRandom(String robotCoordinatesName) {
        robotCoordinatesRandomList.clear();
        addRandom(robotCoordinatesName);
    }


    public void removeRandom() {
        robotCoordinatesRandomList.remove(robotCoordinatesRandomList.size() - 1);
        if (!robotCoordinatesRandomList.isEmpty()) {
            lastCoordinateName = robotCoordinatesRandomList.get(robotCoordinatesRandomList.size() - 1);
        } else {
            lastCoordinateName = "";
        }
        updateOperationDesc();
    }

    public void addRandom(String robotCoordinatesName) {
        lastCoordinateName = robotCoordinatesName;
        robotCoordinatesRandomList.add(robotCoordinatesName);
        updateOperationDesc();
    }

    public String getLastRandomName() {
        return lastCoordinateName;
    }

    public boolean isRandomStarted() {
        return !robotCoordinatesRandomList.isEmpty();
    }
}
