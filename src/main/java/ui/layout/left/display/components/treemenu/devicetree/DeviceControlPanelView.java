package ui.layout.left.display.components.treemenu.devicetree;

import common.constant.DeviceCategory;
import sdk.domain.Device;
import ui.config.json.tree.DeviceConfigManager;
import ui.config.json.tree.TreeDeviceConfig;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static common.constant.ResourceConstant.getDeviceImagePath;

/**
 * 设备控制窗口
 **/
public class DeviceControlPanelView extends JPanel {
    private final MainModel mainModel;
    private final Map<String, JButton> buttonMap;
    private final List<DeviceCategory> deviceCategories;

    public DeviceControlPanelView(MainModel mainModel, List<DeviceCategory> deviceCategories) {
        this.mainModel = mainModel;
        buttonMap = new HashMap<>();
        setLayout(new GridLayout(deviceCategories.size(), 1));
        this.deviceCategories = deviceCategories;
        initView(deviceCategories);
        updateButtonStatus();
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentShown(ComponentEvent e) {
                refreshUIState();
            }
        });
    }

    private JButton addButton(DeviceCategory deviceCategory) {
        JButton button = new JButton(deviceCategory.getDeviceOfficialName());
        button.setPreferredSize(new Dimension(200, 60));
        button.addActionListener(new DefaultButtonActionListener(deviceCategory));
        //设置设备图标
        button.setIcon(SwingUtil.getResourceAsImageIcon(getDeviceImagePath(deviceCategory.getDeviceType())));
        add(button);
        buttonMap.put(button.getText(), button);
        return button;
    }

    private void initView(List<DeviceCategory> deviceCategories) {
        for (DeviceCategory deviceCategory : deviceCategories) {
            addButton(deviceCategory);
        }
    }

    public void updateButtonStatus() {
        TreeDeviceConfig treeDeviceConfig = DeviceConfigManager.getSingletonDeviceConfig();
        for (DeviceCategory deviceCategory : deviceCategories) {
            List<Device> devices = treeDeviceConfig.getDevicesByType(deviceCategory.getDeviceType());
            JButton targetButton = buttonMap.get(deviceCategory.getDeviceOfficialName());
            if (!devices.isEmpty()) {
                boolean isAnyDeviceConnected = devices.stream().anyMatch(Device::isConnected);
                targetButton.setBackground(isAnyDeviceConnected ? Color.GREEN : Color.RED);
            }
        }

    }

    private void refreshUIState() {
        //被动刷新
        //切换到界面才会刷新
        updateButtonStatus();
    }

    /**
     * 默认按钮动作监听器
     */
    private class DefaultButtonActionListener implements ActionListener {
        private final DeviceCategory deviceCategory;

        public DefaultButtonActionListener(DeviceCategory deviceCategory) {
            this.deviceCategory = deviceCategory;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            TreeDeviceConfig treeDeviceConfig = DeviceConfigManager.getSingletonDeviceConfig();
            List<Device> devices = treeDeviceConfig.getDevicesByType(deviceCategory.getDeviceType());
            if (!devices.isEmpty()) {
                boolean isAnyDeviceConnected = devices.stream().anyMatch(Device::isConnected);
                new SwingWorker<Void, Void>() {
                    @Override
                    protected Void doInBackground() throws Exception {
                        ((JButton) e.getSource()).setEnabled(false);
                        if (isAnyDeviceConnected) {
                            //全部断开连接
                            mainModel.getDeviceTreeModel().disconnectDevicesByDeviceType(deviceCategory.getDeviceType());
                        } else {
                            mainModel.getDeviceTreeModel().connectDevicesByDeviceType(deviceCategory.getDeviceType());
                        }
                        return null;
                    }

                    @Override
                    protected void done() {
                        ((JButton) e.getSource()).setEnabled(true);
                        refreshUIState();
                    }
                }.execute();
            }
        }
    }

    /**
     * 刷新界面按钮(用于隐藏设备项)
     *
     * @param selectedCategories 设备项
     */
    public void refreshButtons(List<DeviceCategory> selectedCategories) {
        removeAll();
        buttonMap.clear();
        deviceCategories.clear();
        deviceCategories.addAll(selectedCategories);
        setLayout(new GridLayout(deviceCategories.size(), 1));
        initView(deviceCategories);
        updateButtonStatus();
        revalidate();
        repaint();
    }

}
