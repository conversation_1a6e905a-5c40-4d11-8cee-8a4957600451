package ui.layout.left.display.components.treemenu.actiontree.dialogs;

import lombok.Getter;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import ui.base.BaseView;
import ui.base.OperationAssembler;
import ui.layout.left.display.components.treemenu.actiontree.ActionDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

public abstract class ActionPopupDialog<T> extends ActionDialog implements BaseView, OperationAssembler<T> {

    private final JButton addToScriptButton;

    @Getter
    private final MainModel mainModel;

    public ActionPopupDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        addToScriptButton = SwingUtil.getAddToScriptButton();
    }

    @Override
    public void createView() {
        setTitle("动作列表");
        setSize(400, 300);
        setLayout(new BorderLayout());
        add(addToScriptButton, BorderLayout.SOUTH);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        setModal(true);
        setLocationRelativeTo(null);
    }

    @Override
    public void createActions() {
        addToScriptButton.addActionListener(e -> addToScript());
    }

    protected boolean addToScript() {
        if (!checkUserInput()) {
            return false;
        }
        return getMainModel().getOperationModel().updateOperation(assembleOperation(new Operation()));
    }

    protected abstract JPanel assemblePanel();

    @Override
    public JComponent getAssembleContainer() {
        return assemblePanel();
    }

    protected void assembleOperationAndExecute() {
        if (!checkUserInput()) {
            return;
        }
        Operation operation = assembleOperation(new Operation());
        if (operation != null) {
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        }
    }

}
