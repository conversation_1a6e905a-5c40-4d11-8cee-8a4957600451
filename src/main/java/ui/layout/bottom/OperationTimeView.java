package ui.layout.bottom;

import common.constant.UiConstants;
import sdk.base.operation.OperationResult;
import ui.base.BaseView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.context.CaseContext;
import ui.model.MainModel;
import ui.model.test_executor.TestExecuteStatusObserver;
import ui.model.testcase.TestCaseTableEventObserver;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class OperationTimeView extends JPanel implements BaseView, TestExecuteStatusObserver, TestCaseTableEventObserver {

    private final JLabel startTimeStampLabel;
    private final JLabel executeTimeInfoLabel;
    private final JLabel pauseTimeInfoLabel;

    private final JLabel loginTimeLabel;
    private final MainModel mainModel;

    private long executeTime = 0;
    private long pauseTime = 0;


    Action executeTimeAction = new AbstractAction() {
        @Override
        public void actionPerformed(ActionEvent e) {
            executeTime++;
            executeTimeInfoLabel.setText("已测试时间：" + formatTime(executeTime) + " | ");
        }
    };

    Action pauseTimeAction = new AbstractAction() {
        @Override
        public void actionPerformed(ActionEvent e) {
            pauseTime++;
            pauseTimeInfoLabel.setText("已暂停时间：" + formatTime(pauseTime));
        }
    };

    Timer executeTimer = new Timer(1000, executeTimeAction);
    Timer pauseTimer = new Timer(1000, pauseTimeAction);


    public OperationTimeView(MainModel mainModel) {
        this.mainModel = mainModel;
        //startTimeStampLabel = new JLabel("开始测试时间：xxxx:xx:xx xx:xx:xx | ");
        //executeTimeInfoLabel = new JLabel("已测试时间：00天 00小时 00分钟 00秒 | ");
        //pauseTimeInfoLabel = new JLabel("已暂停时间：00天 00小时 00分钟 00秒");

        startTimeStampLabel = new JLabel();
        executeTimeInfoLabel = new JLabel();
        pauseTimeInfoLabel = new JLabel();
        loginTimeLabel = new JLabel();
        startTimeStampLabel.setFont(startTimeStampLabel.getFont().deriveFont(UiConstants.BOTTOM_FONT_SIZE));
        executeTimeInfoLabel.setFont(executeTimeInfoLabel.getFont().deriveFont(UiConstants.BOTTOM_FONT_SIZE));
        pauseTimeInfoLabel.setFont(pauseTimeInfoLabel.getFont().deriveFont(UiConstants.BOTTOM_FONT_SIZE));
        loginTimeLabel.setFont(pauseTimeInfoLabel.getFont().deriveFont(UiConstants.BOTTOM_FONT_SIZE));
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        //new
        setLayout(new FlowLayout(FlowLayout.RIGHT));
        add(loginTimeLabel);
        loginTimeLabel.setText("启动时间：" + friendlyTimeShow(LocalDateTime.now()));
        add(startTimeStampLabel);
        add(executeTimeInfoLabel);
        add(pauseTimeInfoLabel);
    }


    @Override
    public void registerModelObservers() {
        //mainModel.getTestScriptEventModel().registerObserver(this);
        mainModel.getTestExecuteStatusModel().registerObserver(this);
        mainModel.getTestCaseTableModel().registerObserver(this);
    }

    private String formatTime(long time) {
        long day = time / (24 * 60 * 60);
        long hour = (time % (24 * 60 * 60)) / (60 * 60);
        long minute = (time % (60 * 60)) / 60;
        long second = time % 60;
        //return day + "天 " + hour + "小时 " + minute + "分钟 " + second + "秒";
        return String.format("%02d天 %02d小时 %02d分钟 %02d秒", day, hour, minute, second);
    }

    private String friendlyTimeShow(LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTimeFormatter.format(localDateTime);
    }

    @Override
    public void testStarted(String testSuiteName) {
        loginTimeLabel.setVisible(false);
        // 重置下时间
        executeTime = 0;
        pauseTime = 0;
        startTimeStampLabel.setText("开始测试时间：" + friendlyTimeShow(LocalDateTime.now()));
        executeTimeInfoLabel.setText("已测试时间：" + formatTime(executeTime) + " | ");
        pauseTimeInfoLabel.setText("已暂停时间：" + formatTime(pauseTime));
        executeTimer.start();
    }

    @Override
    public void testPausing(OperationResult operationResult) {
        executeTimer.stop();
        pauseTimer.start();
    }

    @Override
    public void testResumed() {
        executeTimer.start();
        pauseTimer.stop();
    }

    @Override
    public void testStopped() {
        executeTimer.stop();
        pauseTimer.stop();
    }

    @Override
    public void testCompleted() {
        executeTimer.stop();
        pauseTimer.stop();
    }

    //动作序列
    @Override
    public void startTest(CaseContext caseContext) {
        loginTimeLabel.setVisible(false);
        // 重置下时间
        executeTime = 0;
        pauseTime = 0;
        startTimeStampLabel.setText("开始测试时间：" + friendlyTimeShow(LocalDateTime.now()));
        executeTimeInfoLabel.setText("已测试时间：" + formatTime(executeTime) + " | ");
        pauseTimeInfoLabel.setText("已暂停时间：" + formatTime(pauseTime));
        executeTimer.start();
    }

    @Override
    public void pauseTestTime() {
        //pauseTimeInfoLabel.setText("暂停咯");
        executeTimer.stop();
        pauseTimer.start();
    }

    @Override
    public void resumeTestTime() {
        executeTimer.start();
        pauseTimer.stop();
    }

    @Override
    public void stopTest() {
        executeTimer.stop();
        pauseTimer.stop();
    }

    @Override
    public void completedTest() {
        executeTimer.stop();
        pauseTimer.stop();
    }
}
