package ui.layout.bottom;

import sdk.constants.MonitorType;
import sdk.domain.PointInt;
import sdk.domain.monitor.TouchDataMonitorListener;
import sdk.entity.OperationTargetHolder;
import sdk.entity.ScreenKit;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.test_executor.TestExecuteStatusObserver;

public class RealTimeTouchPointView implements TestExecuteStatusObserver, BaseView {

    private final ClientView clientView;
    private final MainModel mainModel;

    public RealTimeTouchPointView(ClientView clientView, MainModel mainModel) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        registerModelObservers();
    }

    @Override
    public void createView() {

    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestExecuteStatusModel().registerObserver(this);
    }

    /**
     * 显示等待报点反馈
     */
    private void setPointFromSerialReady() {
        clientView.getBottomPanelView().setExtraInfo("串口坐标: 等待报点反馈...");
    }

    /**
     * 显示报点数据
     *
     * @param point 报点
     */
    private void setPointFromSerial(PointInt point) {
        clientView.getBottomPanelView().setExtraInfo("串口坐标:" + point);
    }

    /**
     * 清除报点数据
     */
    public void clearPointFromSerial() {
        clientView.getBottomPanelView().setExtraInfo(null);
    }

    public void enableTouchPointTransfer(String serialDeviceAliasName) {
        ScreenKit screenKitManager = OperationTargetHolder.getScreenKit();
        screenKitManager.monitorTouchPoint(serialDeviceAliasName);
        setPointFromSerialReady();
        OperationTargetHolder.getWebsocketDataMonitor().monitor(serialDeviceAliasName,
                MonitorType.TOUCH_POINT_DATA,
                (TouchDataMonitorListener) touchPointArray -> {
                    for (PointInt touchPoint : touchPointArray) {
                        setPointFromSerial(touchPoint);
                    }
                });
    }

    @Override
    public void testStarted(String testSuiteName) {
        OperationTargetHolder.getWebsocketDataMonitor().monitor(
                MonitorType.TOUCH_POINT_DATA,
                (TouchDataMonitorListener) touchPointArray -> {
                    for (PointInt touchPoint : touchPointArray) {
                        setPointFromSerial(touchPoint);
                        mainModel.getReportedPointModel().transportReportedPoint(touchPoint);
                    }
                });
    }
}
