package ui.layout.login;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import common.constant.AppConstants;
import common.utils.ComputerInfo;
import common.utils.NetworkUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.domain.Project;
import sdk.entity.OperationTargetHolder;
import ui.base.AppInfo;
import ui.base.BaseView;
import ui.config.xml.app.AppConfiguration;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static sdk.constants.UrlConstants.PolyTestAPIUrls.*;


/**
 * 登录界面
 */
@Slf4j
public class LoginDialog extends JDialog implements BaseView {
    private boolean offlineLogin = false;
    private final JComboBox<String> userIdComboBox; //uid
    private final JComboBox<String> departmentComboBox;
    private final JComboBox<String> carFactoryComboBox;
    private final SearchComboBox<String> projectComboBox;
    private final JTextField caseSumTextField;
    private final JTextField autoCaseSumTextField;
    private final static int caseMinValue = 100;
    private final static int caseMaxValue = 50000;
    private Box infoBox;
    private final JLabel loginInfoLabel;
    private final JLabel extraInfoLabel;
    private final JCheckBox offlineCheckBox;
    private final JButton loginButton;
    private final JButton cancelButton;
    private final JPasswordField passwordTextField;
    private String userIdListStr;
    private Map<String, String> buMap;
    private final Map<String, Integer> projectMap;

    @Getter
    private boolean isLogin = false;
    private final MainModel mainModel;
    private String userName;
    private Integer projectId;
    private String userMail;
    private String userLogPath;
    private String[] emails;
    private String[] scriptEmails;
    private String[] urls;
    private String[] multiTableUrls;//多维度表格url
    private boolean pauseWhenTestFailed;//测试失败时是否暂停
    private boolean enableEmailSending;//是否启用邮件发送
    private boolean enableSequenceEmailSending;//动作序列是否启用邮件发送
    private boolean enableSequenceRobotSending;//动作序列是否启用飞书机器人发送
    private boolean enableSequenceCloudDocSending;//是否启用云文档发送
    private boolean isSmokeTest;//是否是冒烟测试还是功能测试
    private String secretKey;
    private int passwordLength;
    private List<String> projectList;

    public LoginDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        buMap = new HashMap<>();
        projectList = new ArrayList<>();
        setTitle("登录" + AppConstants.APP_NAME);
        // 获取屏幕尺寸
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();

        // 设置JDialog的宽度和高度
        int width = (int) (screenSize.width / 2.1);
        int height = (int) (screenSize.height / 1.7);

        setPreferredSize(new Dimension(width, height));
        userIdComboBox = new JComboBox<>();
        userIdComboBox.setToolTipText("例如:uidxxx");
        userIdComboBox.setEditable(true);
        passwordTextField = new JPasswordField();
        passwordTextField.setEchoChar('*');

        departmentComboBox = new JComboBox<>();
        carFactoryComboBox = new JComboBox<>();
        loginInfoLabel = new JLabel();
        extraInfoLabel = new JLabel();
        offlineCheckBox = new JCheckBox("离线登陆");
        offlineCheckBox.setEnabled(false);
        projectComboBox = new SearchComboBox<>();
        caseSumTextField = new JTextField("0");
        autoCaseSumTextField = new JTextField("0");

        loginButton = new JButton("登录");
        cancelButton = new JButton("取消");
        projectMap = new HashMap<>();

        // 添加环境变量检查
        String skipLogin = System.getenv("FLYTEST_SKIP_LOGIN");
//        String skipLogin = "true";
        if ("true".equalsIgnoreCase(skipLogin)) {
            this.isLogin = true;
            configAppInfo();
            this.dispose();
            return;
        }

        createView();
        createActions();
        initData();
        restoreView();
        setVisible(true);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        log.info("获取云平台数据...");
        initDepartment();
        if (offlineLogin) {
            setExtraLayout("当前是离线模式");
            setOfflineLoginData(offlineLogin);
        }
    }

    private void setOfflineLoginData(boolean offlineLogin) {
        if (offlineLogin) {
            AppInfo appInfo = AppConfiguration.getInstance().getAppConfig().getAppInfo();
            if (!StringUtils.isEmpty(appInfo.getBuName())) {
                departmentComboBox.addItem(appInfo.getBuName());
            } else {
                departmentComboBox.addItem("Div.IC_ST");
                departmentComboBox.setSelectedIndex(0);
            }
            if (!StringUtils.isEmpty(appInfo.getCarFactoryName())) {
                carFactoryComboBox.addItem(appInfo.getCarFactoryName());
            } else {
                carFactoryComboBox.addItem("德赛西威");
                carFactoryComboBox.setSelectedIndex(0);
            }
            if (!StringUtils.isEmpty(appInfo.getProject())) {
                projectComboBox.addItem(appInfo.getProject());
            } else {
                projectComboBox.addItem("自动化工具调试");
                projectComboBox.setSelectedIndex(0);
            }
            projectList.add(appInfo.getProject());
            userMail = appInfo.getEmail();
            emails = appInfo.getEmails();
            scriptEmails = appInfo.getScriptEmails();
            multiTableUrls = appInfo.getMultiTableUrls();
            urls = appInfo.getUrls();
            pauseWhenTestFailed = appInfo.isPauseWhenTestFailed();
            enableEmailSending = appInfo.isEnableEmailSending();
            enableSequenceEmailSending = appInfo.isEnableSequenceEmailSending();
            enableSequenceRobotSending = appInfo.isEnableSequenceRobotSending();
            enableSequenceCloudDocSending = appInfo.isEnableSequenceCloudDocSending();
            isSmokeTest = appInfo.isSmokeTest();
            userName = appInfo.getUserName();
            projectId = appInfo.getProjectId();
            userLogPath = appInfo.getUserLogPath();
        } else {
            departmentComboBox.removeAllItems();
            carFactoryComboBox.removeAllItems();
            projectComboBox.removeAllItems();
            projectList.clear();
            userMail = null;
            userName = null;
            emails = null;
            scriptEmails = null;
            multiTableUrls = null;
            urls = null;
            pauseWhenTestFailed = true;
            enableEmailSending = true;
            enableSequenceEmailSending = true;
            enableSequenceRobotSending = true;
            enableSequenceCloudDocSending = false;
            isSmokeTest = false;
            projectId = null;
            userLogPath = null;
        }
    }

    @Override
    public void restoreView() {
        AppInfo appInfo = AppConfiguration.getInstance().getAppConfig().getAppInfo();
        initUserId(appInfo);
        departmentComboBox.setSelectedItem(appInfo.getBuName());
        userIdComboBox.setSelectedItem(appInfo.getUserId());
        carFactoryComboBox.setSelectedItem(appInfo.getCarFactoryName());
        projectComboBox.setSelectedItem(appInfo.getProject());
        passwordLength = appInfo.getPasswordLength();
        caseSumTextField.setText(String.valueOf(appInfo.getCaseSum()));
        autoCaseSumTextField.setText(String.valueOf(appInfo.getAutoTestCaseSum()));
        secretKey = appInfo.getSecretKey();
        if (passwordLength != 0) {
            if (!StringUtils.isEmpty(appInfo.getSecretKey())) {
                passwordTextField.setText(appInfo.getSecretKey().substring(0, appInfo.getPasswordLength()));
            }
            userName = appInfo.getUserName();
        }
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        JPanel inputPanel = new JPanel(new GridBagLayout());
        inputPanel.setBorder(new EmptyBorder(70, 100, 0, 100));
        inputPanel.add(new JLabel("用户名:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(userIdComboBox, SwingUtil.getRowGridBagConstraints(0, 0, 0));
        inputPanel.add(new JLabel("密码:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(passwordTextField, SwingUtil.getRowGridBagConstraints(0, 0, 0));
        inputPanel.add(new JLabel("事业单元:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(departmentComboBox, SwingUtil.getRowGridBagConstraints(0, 1, 0));
        inputPanel.add(new JLabel("车厂:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(carFactoryComboBox, SwingUtil.getRowGridBagConstraints(0, 0, 0));
        inputPanel.add(new JLabel("项目名:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        projectComboBox.addToJPanel(inputPanel, SwingUtil.getRowGridBagConstraints(0, 1, 0));
        inputPanel.add(new JLabel("全功能用例总数:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(caseSumTextField, SwingUtil.getRowGridBagConstraints(0, 0, 0));
        inputPanel.add(new JLabel("压力测试总数:"), SwingUtil.getRowGridBagConstraints(1, 0, 0));
        inputPanel.add(autoCaseSumTextField, SwingUtil.getRowGridBagConstraints(0, 0, 0));
        JPanel buttonPanel = new JPanel(new GridLayout(3, 1));
        loginButton.setPreferredSize(new Dimension(loginButton.getWidth(), 60));

        infoBox = Box.createHorizontalBox();
        infoBox.add(Box.createHorizontalGlue());
        infoBox.add(loginInfoLabel);
        infoBox.add(extraInfoLabel);
        infoBox.add(offlineCheckBox);
        infoBox.add(Box.createHorizontalGlue());
        buttonPanel.add(infoBox);
        buttonPanel.add(loginButton);
        buttonPanel.add(cancelButton);
        add(inputPanel, BorderLayout.NORTH);
        add(buttonPanel, BorderLayout.SOUTH);
        setLocationRelativeTo(null);
        setResizable(true);
        setModal(true);
        getRootPane().setDefaultButton(loginButton);
        SwingUtil.centerInScreen(this);
        setAlwaysOnTop(true);
        infoBox.setVisible(offlineLogin);
        offlineCheckBox.setVisible(false);
    }

    /**
     * 离线登陆
     */
    private void loginOffline() {
        isLogin = true;
        configAppInfo();
        setVisible(false);
    }

    /**
     * 在线登录
     */
    private void loginOnline() {
        if (projectList == null) {
            setExtraLayout("项目列表获取失败");
            return;
        }
        String caseSumString = caseSumTextField.getText().trim();
        long caseSum;
        try {
            caseSum = Long.parseLong(caseSumString);
            if (caseSum < caseMinValue || caseSum > caseMaxValue) {
                setExtraLayout(String.format("全功能用例总数要求在%d~%d范围", caseMinValue, caseMaxValue));
                return;
            }
        } catch (NumberFormatException e) {
            setExtraLayout("全功能用例总数要求输入数字");
            return;
        }

        String autoCaseSumString = autoCaseSumTextField.getText().trim();
        try {
            long autoTestCaseSum = Long.parseLong(autoCaseSumString);
            if (autoTestCaseSum > caseSum) {
                setExtraLayout(String.format("压力测试总数不能大于全功能自动化测试总数:%d", caseSum));
                return;
            }
            if (autoTestCaseSum <= 0) {
                setExtraLayout("压力测试总数不能小于等于0");
                return;
            }
        } catch (NumberFormatException e) {
            setExtraLayout("压力测试总数要求输入数字");
            return;
        }

        String projectName = (String) projectComboBox.getSelectedItem();
        if (!StringUtils.isEmpty(projectName)) {
            if (projectList.stream().noneMatch(x -> x.equals(projectName))) {
                setExtraLayout("该项目不存在,检查项目名");
                return;
            }
            if (!isCreatedProject(projectName)) {
                createProject(projectName);
            }
            String userId = (String) userIdComboBox.getSelectedItem();
            String password = String.valueOf(passwordTextField.getPassword());

            if (Objects.equals(userId, "") || Objects.equals(password, "")) {
                setExtraLayout("用户名或密码不能为空");
            } else {
                if (projectComboBox.getItemCount() > 0) {
                    try {
                        if (passwordLength != 0 && secretKey.substring(0, passwordLength).equals(password)) {
                            isLogin = verifySecretKey(userId);
                        } else {
                            isLogin = verifyPassword(userId, password);
                        }
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                        return;
                    }

                    if (!isLogin) {
                        setExtraLayout("用户名或密码错误");
                        return;
                    }
                    configAppInfo();
                }
//                else {
//                    setExtraLayout();
//                    extraInfoLabel.setText("项目名不能为空");
//                }
            }
        } else {
            setExtraLayout("项目名不能为空");
        }
    }

    private void configAppInfo() {
        AppInfo appInfo = mainModel.getAppInfo();
        String userId = (String) userIdComboBox.getSelectedItem();
        String projectName = (String) projectComboBox.getSelectedItem();
        ComputerInfo computerInfo = NetworkUtils.getComputerInfo();
        appInfo.setTestUnit(String.format("%s/%s", computerInfo.getComputerName(), computerInfo.getUserName()));
        appInfo.setBuName((String) departmentComboBox.getSelectedItem());
        appInfo.setIpAddress(NetworkUtils.getIpAddress());
        appInfo.setUserName(userName);
        appInfo.setSecretKey(secretKey);
        appInfo.setUserId(userId);
        appInfo.setEmail(userMail);
        appInfo.setProject(projectName);
        appInfo.setProjectId(projectId != null ? projectId : projectMap.get(projectName));
        appInfo.setPlatformCode(AppConstants.PLATFORM_CODE);
        appInfo.setCarFactoryName((String) carFactoryComboBox.getSelectedItem());
        appInfo.setPasswordLength(passwordTextField.getPassword().length);
        appInfo.setCaseSum(Integer.parseInt(caseSumTextField.getText()));
        appInfo.setAutoTestCaseSum(Integer.parseInt(autoCaseSumTextField.getText()));
        emails = getEmailsByProjectName(projectName);
        appInfo.setEmails(emails);
        scriptEmails = getScriptEmailsByProjectName(projectName);
        appInfo.setScriptEmails(scriptEmails);
        multiTableUrls = getMultiTableUrlsByProjectName(projectName);
        appInfo.setMultiTableUrls(multiTableUrls);
        urls = getUrlsByProjectName(projectName);
        appInfo.setUrls(urls);
        pauseWhenTestFailed = isPauseWhenTestFailedByProjectName(projectName);
        appInfo.setPauseWhenTestFailed(pauseWhenTestFailed);
        enableEmailSending = isEnableEmailSendingByProjectName(projectName);
        appInfo.setEnableEmailSending(enableEmailSending);
        enableSequenceEmailSending = isEnableSequenceEmailSendingByProjectName(projectName);
        appInfo.setEnableSequenceEmailSending(enableSequenceEmailSending);
        enableSequenceRobotSending = isEnableSequenceRobotSendingByProjectName(projectName);
        appInfo.setEnableSequenceRobotSending(enableSequenceRobotSending);
        enableSequenceCloudDocSending = isEnableSequenceCloudDocSendingByProjectName(projectName);
        appInfo.setEnableSequenceCloudDocSending(enableSequenceCloudDocSending);
        isSmokeTest = isSmokeTestByProjectName(projectName);
        appInfo.setSmokeTest(isSmokeTest);
        userLogPath = getUserLogPathByProjectName(projectName);
        appInfo.setUserLogPath(userLogPath);
        addAllUserId(appInfo, userId);
        setVisible(false);
        AppConfiguration.getInstance().getAppConfig().setAppInfo(appInfo).save();
    }

    private String[] getEmailsByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().getEmailsByProjectName(projectName);
    }

    private String[] getScriptEmailsByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().getScriptEmailsByProjectName(projectName);
    }

    private String[] getMultiTableUrlsByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().getMultiTableUrlsByProjectName(projectName);
    }

    private String[] getUrlsByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().getUrlsByProjectName(projectName);
    }

    private boolean isPauseWhenTestFailedByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isPauseWhenTestFailedByProjectName(projectName);
    }

    private boolean isEnableEmailSendingByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isEnableEmailSendingByProjectName(projectName);
    }

    private boolean isEnableSequenceEmailSendingByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isEnableSequenceEmailSendingByProjectName(projectName);
    }

    private boolean isEnableSequenceRobotSendingByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isEnableSequenceRobotSendingByProjectName(projectName);
    }

    private boolean isEnableSequenceCloudDocSendingByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isEnableSequenceCloudDocSendingByProjectName(projectName);
    }

    private boolean isSmokeTestByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().isSmokeTestByProjectName(projectName);
    }
    private String getUserLogPathByProjectName(String projectName) {
        return AppConfiguration.getInstance().getAppConfig().getUserLogPathByProjectName(projectName);
    }

    /**
     * 关闭界面
     */
    private void close() {
        dispose();
    }

    private void switchDepartment() throws IOException {
        if (offlineLogin) {
            return;
        }
        try {
            String currentDepartment = (String) departmentComboBox.getSelectedItem();
            if (currentDepartment != null) initCarFactoryName(buMap.get(currentDepartment));
        } catch (SocketTimeoutException e) {
            log.error(e.getMessage(), e);
            loginInfoLabel.setText("云平台无法访问，请尝试离线登陆！");
            setOfflineLayout();
        }
    }

    private void switchCarFactory() throws IOException {
        if (offlineLogin) {
            return;
        }
        try {
            String currentCarFactory = (String) carFactoryComboBox.getSelectedItem();
            String currentDepartment = (String) departmentComboBox.getSelectedItem();
            initProjectName(currentCarFactory, buMap.get(currentDepartment));
        } catch (SocketTimeoutException e) {
            log.error(e.getMessage(), e);
            loginInfoLabel.setText("云平台无法访问，请尝试离线登陆！");
            setOfflineLayout();
        }
    }

    /**
     * 增加项目
     */
    private void addProject() {
        String projectName;
        while (true) {
            projectName = JOptionPane.showInputDialog(this, "输入项目名");
            if (projectName == null) {
                break;
            }
            projectName = projectName.trim().replaceAll("\\s+", "_");
            if (projectName.isEmpty()) {
                setExtraLayout("项目名不能为空");
                continue;
            }
            if (projectName.equals(AppConstants.PUBLIC_PROJECT)) {
                setExtraLayout("系统保留项目，请指定其他项目名");
                continue;
            }
            Project project = new Project();
            project.setName(projectName);
            project.setCommunal(false);
            JsonResponse<Project> resp = OperationTargetHolder.getTestProjectKit().registerProject(project);
            if (resp.isOk()) {
                projectComboBox.addItem(projectName);
                projectComboBox.setSelectedItem(projectName);
            } else {
                setExtraLayout("添加项目出错:" + resp.getMessage());
            }
            break;
        }

    }

    @Override
    public void createActions() {
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        offlineCheckBox.addItemListener(e -> {
            offlineLogin = offlineCheckBox.isSelected();
            setOfflineLoginData(offlineLogin);
            passwordTextField.setEnabled(!offlineLogin);
        });
        loginButton.addActionListener(e -> loginApp());
        cancelButton.addActionListener(e -> close());
        userIdComboBox.addItemListener(e -> passwordTextField.setText(""));
        departmentComboBox.addItemListener(e -> {
            try {
                switchDepartment();
            } catch (IOException ex) {
                log.warn(ex.getMessage(), ex);
            }
        });
        carFactoryComboBox.addItemListener(e -> {
            try {
                switchCarFactory();
            } catch (IOException ex) {
                log.warn(ex.getMessage(), ex);
            }
        });
        projectComboBox.addItemListener(e -> {
            caseSumTextField.setText("0");
            autoCaseSumTextField.setText("0");
        });
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosed(WindowEvent e) {
                super.windowClosed(e);
                ComputerInfo computerInfo = NetworkUtils.getComputerInfo();
                mainModel.getAppInfo().setTestUnit(computerInfo.getComputerName());
                System.exit(0);
            }
        });
    }

    private void loginApp() {
        if (offlineLogin) {
            loginOffline();
        } else {
            loginOnline();
        }
        mainModel.getAppInfo().setOfflineMode(offlineLogin);
    }

    public Boolean loginVerify(String userId, String password) throws IOException {
        Map<String, String> param = new HashMap<>();
        param.put("appid", "8");
        param.put("secretkey", "cdfvfggs");
        param.put("username", userId);
        param.put("password", password);
        String responseBody = BaseHttpClient.postByFormBody(LOGIN_VERIFY, param, false);
        JSONObject jsonResponseBody = JSONObject.parseObject(responseBody);
        if (jsonResponseBody.get("message").equals("成功")) {
            JSONObject jsonObject = jsonResponseBody.getJSONObject("data").getJSONObject("userinfo");
            userName = jsonObject.getString("displayName");
            userMail = jsonObject.getString("mail");
        } else {
            log.warn("请检查用户名或者密码是否正确");
        }
        return Boolean.parseBoolean(jsonResponseBody.get("success").toString());
    }

    public void addAllUserId(AppInfo appInfo, String userId) {
        if (userIdListStr != null && !userIdListStr.isEmpty()) {
            if (!userIdListStr.contains(userId)) appInfo.setUserIdList(userIdListStr + "," + userId);
        } else {
            appInfo.setUserIdList(userId);
        }
    }

    public Map<String, String> getBuList() throws IOException {
        Map<String, String> buMap = new HashMap<>();
        JSONArray data = sendRequest(GET_BU_LIST_URL);
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                buMap.put(data.getJSONObject(i).getString("name"), data.getJSONObject(i).getString("id"));
            }
        }
        return buMap;
    }

    public List<String> getCarFacList(String buID) throws IOException {
//        log.info("获取云平台车厂列表...");
        String url = GET_CAR_FAC_LIST_URL + "?departmentId=" + buID;
        JSONArray data = sendRequest(url);
        List<String> carFactoryList = new ArrayList<>();
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                carFactoryList.add(data.getString(i));
            }
        }
        return carFactoryList;
    }

    public List<String> getProjectList(String carFactoryName, String buId) throws IOException {
//        log.info("获取云平台项目列表...");
        String url = GET_PROJECT_LIST_BY_DEPT_ID_AD_CAR_FAC_URL + "?carFac=" + carFactoryName + "&departmentId=" + buId;
        JSONArray data = sendRequest(url);
        List<String> projectList = new ArrayList<>();
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                String project = data.getJSONObject(i).getString("project");
                int projectId = Integer.parseInt(data.getJSONObject(i).getString("id"));
                projectList.add(project);
                projectMap.put(project, projectId);
            }
        }
        return projectList;
    }

    public void initDepartment() {
        String mode = System.getenv("flytest_mode");
        if (mode != null && mode.equalsIgnoreCase("offline")) {
            setOfflineLayout();
        } else {
            try {
                buMap = getBuList();
                for (Map.Entry<String, String> entry : buMap.entrySet()) {
                    departmentComboBox.addItem(entry.getKey());
                }
            } catch (Exception e) {
                log.warn("Get {}: {}", GET_BU_LIST_URL, e.getMessage());
                loginInfoLabel.setText("云平台无法访问，请尝试离线登陆！");
                setOfflineLayout();
            }
        }
    }

    private void setOfflineLayout() {
        infoBox.setVisible(true);
        offlineCheckBox.setVisible(true);
        offlineCheckBox.setEnabled(true);
    }

    private void setExtraLayout(String text) {
        infoBox.setVisible(true);
//        offlineCheckBox.setVisible(false);
        extraInfoLabel.setText(text);
    }

    public void initUserId(AppInfo appInfo) {
        userIdListStr = appInfo.getUserIdList();
        String[] userIdList = appInfo.getUserIdList().split(",");
        for (String userId : userIdList) {
            userIdComboBox.addItem(userId);
        }
    }

    public void initCarFactoryName(String buId) throws IOException {
        carFactoryComboBox.removeAllItems();
        List<String> carFacList = getCarFacList(buId);
        for (String carFactoryName : carFacList) {
            carFactoryComboBox.addItem(carFactoryName);
        }
    }

    public void initProjectName(String carFactoryName, String buId) throws IOException {
        projectComboBox.removeAllItems();
        if (carFactoryName != null) {
            projectList = getProjectList(carFactoryName, buId);
            projectComboBox.setSearchComboBoxItems(projectList);
        }
    }

    public JSONArray sendRequest(String url) throws IOException {
        String responseBody = BaseHttpClient.getForString(url, false, 10);
        JSONObject jsonResponseBody = JSONObject.parseObject(responseBody);
        return jsonResponseBody.getJSONArray("data");
    }

    public boolean verifySecretKey(String userId) throws IOException {
        return loginVerify(userId, secretKey);
    }

    public boolean verifyPassword(String userId, String password) throws IOException {
        secretKey = PasswordEncryption.getEncryptedPassword(password);
        if (secretKey == null) {
            return false;
        }
        return loginVerify(userId, secretKey);
    }

    public boolean isCreatedProject(String projectName) {
//      未被创建的项目->创建项目 ,原规则:项目不同名, 调接口存在的问题->不同车厂存在同名项目怎么解决？？？
        boolean isCreatedProject = false;
        JsonResponse<List<Project>> resp = OperationTargetHolder.getTestProjectKit().fetchAllProjects();
        if (resp.isOk()) {
            for (Project project : resp.getData().stream().filter(p -> !p.getName().equals(AppConstants.PUBLIC_PROJECT)).collect(Collectors.toList())) {
                if (projectName.equals(project.getName())) {
                    isCreatedProject = true;
                    break;
                }
            }
        } else {
            setExtraLayout("获取项目失败:" + resp.getMessage());
        }
        return isCreatedProject;
    }

    public void createProject(String projectName) {
        if (projectName.equals(AppConstants.PUBLIC_PROJECT)) {
            setExtraLayout("系统保留项目，请指定其他项目名");
            return;
        }

        Project project = new Project();
        project.setName(projectName);
        project.setCommunal(false);
        JsonResponse<Project> resp = OperationTargetHolder.getTestProjectKit().registerProject(project);
        if (resp.isOk()) {
            projectComboBox.addItem(projectName);
            projectComboBox.setSelectedItem(projectName);
        } else {
            setExtraLayout("添加项目出错:" + resp.getMessage());
        }
    }
}
