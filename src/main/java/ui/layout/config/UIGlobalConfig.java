package ui.layout.config;

import com.alibaba.fastjson2.JSON;
import common.utils.FileUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import ui.config.xml.app.AppFileManager;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/9/2 18:13
 * @description :
 * @modified By :
 * @since : 2023/9/2
 **/
@Setter
@Slf4j
public class UIGlobalConfig {
    private UILayout uiLayout;
    private static final String UI_CONFIG_FILE_NAME = "uiGlobalConfig.json";
    private static final UIGlobalConfig INSTANCE = new UIGlobalConfig();

    public static UIGlobalConfig getInstance() {
        return INSTANCE;
    }

    public UILayout getUILayout() {
        if (uiLayout != null) {
            return uiLayout;
        }
        try {
            File uiConfigFile = AppFileManager.getInstance().getConfigFolder().createFile(UI_CONFIG_FILE_NAME);
            uiLayout = JSON.parseObject(FileUtils.readStringFromFile(uiConfigFile), UILayout.class);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        if (uiLayout == null) {
            uiLayout = new UILayout();
            Layout splitLayout = new Layout();
            // 修改默认初始位置设置
            splitLayout.setLeftTopPane(new Location(0, 0, 200, 300));  // 调整默认宽高
            splitLayout.setLeftCenterPane(new Location(0, 300, 200, 300));
            splitLayout.setLeftRightPane(new Location(0, 600, 200, 300));
            splitLayout.setLeftBottomPane(new Location(0, 900, 200, 300));
            splitLayout.setRightPane(new RightPane());

            uiLayout.setLayout(splitLayout);
            uiLayout.setViewTabIndex(0);
            save();
        }
        return uiLayout;
    }

    public void save() {
        String config = JSON.toJSONString(uiLayout);
        try {
            File configFile = AppFileManager.getInstance().getConfigFolder().createFile(UI_CONFIG_FILE_NAME);
            FileWriter fileWriter = new FileWriter(configFile);
            fileWriter.write(config);
            fileWriter.flush();
            fileWriter.close();
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
    }

}
