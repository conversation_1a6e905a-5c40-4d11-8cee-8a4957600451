package ui.config.json;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.SmokingTestConfigModel;
import ui.config.xml.app.AppFileManager;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@Data
@Slf4j
public class SmokingTestJsonConfig {
    private volatile static SmokingTestJsonConfig smokingTestJsonConfig;
    private static final String SMOKING_TEST_CONFIG_FILE_NAME = "smokingTestConfig.json";
    private SmokingTestConfigModel smokingTestConfigModel;

    public static SmokingTestJsonConfig getInstance() {
        if (smokingTestJsonConfig == null) {
            synchronized (SmokingTestJsonConfig.class) {
                if (smokingTestJsonConfig == null) {
                    smokingTestJsonConfig = new SmokingTestJsonConfig();
                }
            }
        }
        return smokingTestJsonConfig;
    }

    public SmokingTestConfigModel getSmokingTestConfig() {
        if (smokingTestConfigModel != null) {
            return smokingTestConfigModel;
        }
        try {
            File configFile = AppFileManager.getInstance().getConfigFolder().createFile(SMOKING_TEST_CONFIG_FILE_NAME);
            try (InputStreamReader reader = new InputStreamReader(Files.newInputStream(configFile.toPath()), StandardCharsets.UTF_8);
                 BufferedReader bufferedReader = new BufferedReader(reader)) {

                StringBuilder contentBuilder = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    contentBuilder.append(line);
                }
                smokingTestConfigModel = JSON.parseObject(contentBuilder.toString(), SmokingTestConfigModel.class);
            }
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        if (smokingTestConfigModel == null) {
            smokingTestConfigModel = new SmokingTestConfigModel();
        }
        return smokingTestConfigModel;
    }

    public void save() {
        String config = JSON.toJSONString(smokingTestConfigModel);
        try {
            File configFile = AppFileManager.getInstance().getConfigFolder().createFile(SMOKING_TEST_CONFIG_FILE_NAME);
            try (Writer writer = Files.newBufferedWriter(configFile.toPath(), StandardCharsets.UTF_8)) {
                writer.write(config);
                writer.flush();
            }
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
    }
}
