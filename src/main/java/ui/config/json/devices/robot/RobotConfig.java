package ui.config.json.devices.robot;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ui.config.json.devices.DeviceConfig;

/**
 * 机械臂配置
 */
//TODO：设备配置文件考虑放到后端存储
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotConfig extends DeviceConfig {

    private ScaledMonitorAreaConfig monitorArea;

    private RobotParametersConfig parameters;

    //FIXME: 改成存储第几个设备，int类型
    private String calibrationCameraAliasName;

    public RobotConfig() {
        monitorArea = new ScaledMonitorAreaConfig();
        parameters = new RobotParametersConfig();
    }

}
