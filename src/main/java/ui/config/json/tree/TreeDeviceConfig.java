package ui.config.json.tree;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.Device;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@Data
@Slf4j
public class TreeDeviceConfig {

    private Map<String, DeviceTypeCollections> deviceTypes = new ConcurrentHashMap<>(); //key:deviceType

    /**
     * 添加json设备配置
     *
     * @param device 设备实例
     */
    public void addDevice(Device device) {
        if (!deviceTypes.containsKey(device.getDeviceType())) {
            deviceTypes.put(device.getDeviceType(), new DeviceTypeCollections());
        }
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(device.getDeviceType());
        deviceTypeCollections.add(device);
        log.info("添加->第{}个设备配置:{}", device.getDeviceIndex(), device.getFriendlyName());
    }

    /**
     * 移除json设备配置
     *
     * @param device 设备实例
     */
    public void removeDevice(Device device) {
        int deviceIndex = device.getDeviceIndex();
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(device.getDeviceType());
        if (deviceTypeCollections != null) {
            deviceTypeCollections.remove(deviceIndex);
            log.info("移除->第{}个设备配置:{}", deviceIndex, device.getFriendlyName());
        }
    }

    public void clearDevice(String deviceType) {
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(deviceType);
        if (deviceTypeCollections != null) {
            deviceTypeCollections.clear();
            log.info("清除所有设备配置:{}", deviceType);
        }
    }

    /**
     * 连接json设备配置
     *
     * @param device 设备实例
     */
    public void connectDevice(Device device) {
        int deviceIndex = device.getDeviceIndex();
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(device.getDeviceType());
        if (deviceTypeCollections != null) {
            deviceTypeCollections.setConnected(deviceIndex, device.isConnected());
            log.info("连接->第{}个设备配置:{}", deviceIndex, device.getFriendlyName());
        }
    }

    /**
     * 断开json设备配置
     *
     * @param device 设备实例
     */
    public void disconnectDevice(Device device) {
        int deviceIndex = device.getDeviceIndex();
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(device.getDeviceType());
        if (deviceTypeCollections != null) {
            deviceTypeCollections.setConnected(deviceIndex, device.isConnected());
            log.info("断开->第{}个设备配置:{}", deviceIndex, device.getFriendlyName());
        }
    }

    /**
     * 获取设备列表
     *
     * @return 设备列表
     */
    @JSONField(serialize = false)
    public List<Device> getDevices() {
        List<Device> devices = new LinkedList<>();
        for (DeviceTypeCollections typeCollections : deviceTypes.values()) {
            devices.addAll(typeCollections.getDevices().values());
        }
        return devices;
    }

    /**
     * 根据设备类别获取设备列表
     *
     * @return 设备列表
     */
    @JSONField(serialize = false)
    public List<Device> getDevicesByType(String deviceType) {
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(deviceType);
        return deviceTypeCollections != null ? new ArrayList<>(deviceTypeCollections.getDevices().values()) : new ArrayList<>();
    }

    public void renameDevice(Device device) {
        String newDeviceName = device.getUserDeviceName();
        int deviceIndex = device.getDeviceIndex();
        DeviceTypeCollections deviceTypeCollections = deviceTypes.get(device.getDeviceType());
        if (deviceTypeCollections != null) {
            deviceTypeCollections.rename(deviceIndex, newDeviceName);
            log.info("自定义用户名->第{}个设备配置:{}", deviceIndex, newDeviceName);
        }
    }
}
