package ui.config.xml.project;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.config.base.FileManager;
import ui.config.base.Folder;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 10:50
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
@Slf4j
public abstract class ProjectFileManager extends FileManager {
    private static final Map<String, ProjectFileManager> fileManagerMap = new ConcurrentHashMap<>();
    public static final File baseTestProjectPath = new File(baseAppDataPath, "projects");

    static {
        createFolder(baseTestProjectPath);
    }

    private final File testProjectPath;

    @Getter
    public Folder configPath;

    @Getter
    public Folder reportPath;
    @Getter
    public Folder templatePath;
    @Getter
    private Folder devicesPath;
    @Getter
    private Folder functionalTestPath;
    @Getter
    private Folder smokingTestPath;

    public ProjectFileManager(String projectName) {
        testProjectPath = new File(baseTestProjectPath, projectName);
        initPaths();
    }

    public synchronized static ProjectFileManager of(String projectName) {
        if (projectName == null) {
            return null;
        }
        if (!fileManagerMap.containsKey(projectName)) {
            DefaultProjectFileManager fileManager = new DefaultProjectFileManager(projectName);
            fileManagerMap.put(projectName, fileManager);
        }
        return fileManagerMap.get(projectName);
    }

    @SuppressWarnings("unchecked")
    public synchronized static <T extends ProjectFileManager> T of(String projectName, Class<T> tClass) {
        if (!fileManagerMap.containsKey(projectName)) {
            ProjectFileManager projectFileManager;
            try {
                projectFileManager = tClass.getConstructor(String.class).newInstance(projectName);
            } catch (InvocationTargetException | InstantiationException | IllegalAccessException |
                     NoSuchMethodException e) {
                log.error(e.getMessage(), e);
                projectFileManager = new DefaultProjectFileManager(projectName);
            }
            fileManagerMap.put(projectName, projectFileManager);
        }
        return (T) fileManagerMap.get(projectName);
    }


    public void initPaths() {
        createFolder(testProjectPath);
        configPath = createFolder(testProjectPath, "config");
        devicesPath = createFolder(configPath, "devices");
        reportPath = createFolder(testProjectPath, "report");
        templatePath = createFolder(testProjectPath, "template");
        functionalTestPath = createFolder(reportPath, "功能测试");
        smokingTestPath = createFolder(reportPath, "冒烟测试");
    }

}
