package ui.config.xml.project;

import org.dom4j.Element;
import ui.config.base.XmlConfiguration;
import ui.config.xml.device.WaveConfig;

/**
 * 设备配置
 * TODO：XML->对象转换，各个选项卡都要独立保存数据
 */
public class DeviceXmlConfig extends ProjectXmlConfig {
    //设备类型名，如电源、串口
    private final Element deviceTypeIndexElement;

    //设备型号名，如IT68
    private final Element deviceModelIndexElement;

    private final Element kikusuiSequenceNameConfig;

    private final Element kikusuiSequenceBuiltinConfig;

    public DeviceXmlConfig(XmlConfiguration projectConfiguration) {
        super(projectConfiguration);
        Element deviceElement = addElement(getProjectElement(), "device");
        deviceTypeIndexElement = addElement(deviceElement, "deviceTypeIndex");
        deviceModelIndexElement = addElement(deviceElement, "deviceModelIndex");
        Element kikusuiElement = addElement(deviceElement, "kikusui");
        kikusuiSequenceNameConfig = addElement(kikusuiElement, "sequenceName");
        kikusuiSequenceBuiltinConfig = addElement(kikusuiElement, "builtin");

    }

    private int getIndex(Element element) {
        String text = element.getTextTrim();
        if (text.isEmpty()) {
            return 0;
        }
        return Integer.parseInt(text);
    }


    /**
     * 设置设备型号索引
     *
     * @param deviceModelIndex 设备型号索引
     * @return DeviceConfig
     */
    public DeviceXmlConfig setDeviceModelIndex(int deviceModelIndex) {
        deviceModelIndexElement.setText(String.valueOf(deviceModelIndex));
        return this;
    }

    /**
     * 获取设备型号索引
     *
     * @return 设备型号索引
     */
    public int getDeviceModelIndex() {
        return getIndex(deviceModelIndexElement);
    }

    /**
     * 设置设备类型索引
     *
     * @param deviceTypeIndex 设备类型索引
     * @return DeviceConfig
     */
    public DeviceXmlConfig setDeviceTypeIndex(int deviceTypeIndex) {
        deviceTypeIndexElement.setText(String.valueOf(deviceTypeIndex));
        return this;
    }

    /**
     * 获取设备类型索引
     *
     * @return 设备类型索引
     */
    public int getDeviceTypeIndex() {
        return getIndex(deviceTypeIndexElement);
    }


    public WaveConfig getKikusuiSequenceNameConfig() {
        String sequenceName = kikusuiSequenceNameConfig.getTextTrim();
        if (sequenceName.isEmpty()) {
            return null;
        }

        WaveConfig waveConfig = new WaveConfig();
        waveConfig.setSequenceName(sequenceName);
        waveConfig.setBuiltin(kikusuiSequenceBuiltinConfig.getTextTrim().equals("true"));
        return waveConfig;
    }

    public DeviceXmlConfig setKikusuiSequenceNameConfig(WaveConfig waveConfig) {
        kikusuiSequenceNameConfig.setText(waveConfig.getSequenceName());
        kikusuiSequenceBuiltinConfig.setText(String.valueOf(waveConfig.isBuiltin()));
        return this;
    }
}
