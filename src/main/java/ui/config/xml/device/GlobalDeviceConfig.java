package ui.config.xml.device;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.config.base.BaseJsonConfig;
import ui.config.base.JsonConfiguration;

/**
 * 设备全局配置
 */
@Getter
@Slf4j
public class GlobalDeviceConfig extends BaseJsonConfig {

    private final KikusuiDeviceConfig kikusuiDeviceConfig;


    public GlobalDeviceConfig(JsonConfiguration configuration) {
        super(configuration);
        kikusuiDeviceConfig = new KikusuiDeviceConfig(configuration);
    }

}
