package ui.config.xml.device;

import com.alibaba.fastjson2.JSON;
import common.utils.FileUtils;
import common.utils.JsonUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.config.xml.app.AppFileManager;
import ui.config.base.BaseJsonConfig;
import ui.config.base.Folder;
import ui.config.base.JsonConfiguration;

import java.io.File;
import java.io.IOException;

/**
 * 菊水电源配置类
 */
@Getter
@Slf4j
public class KikusuiDeviceConfig extends BaseJsonConfig {
    private static final String waveConfigFileName = "waveConfig.json";

    private final Folder sequenceFolder; //菊水配置文件夹

//    @Getter
//    private final Folder userSequenceFolder; //用户定义的菊水配置文件夹


    public KikusuiDeviceConfig(JsonConfiguration configuration) {
        super(configuration);
        sequenceFolder = AppFileManager.getInstance().getDevicesFolder().getKikusuiFolder().createFolder("pulseSequences");
//        userSequenceFolder = sequenceFolder.createFolder("user");
    }

    /**
     * 写入菊水电源波形配置
     *
     * @param waveConfig 脉冲配置
     * @return 是否写入成功
     */
    public boolean writeWavePulseSequenceOfUser(Folder folder, WaveConfig waveConfig) {
        try {
            File targetFile = folder.createFile(waveConfigFileName);
            String jsonString = JsonUtils.convertObjectToJSON(waveConfig);
            FileUtils.writeFileFromString(targetFile, jsonString, false);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 删除用户脉冲系列配置
     *
     * @param sequenceName 用户脉冲系列配置名称
     * @return 是否删除成功
     */
    public boolean deleteWavePulseSequenceOfUser(String sequenceName) {
        return sequenceFolder.delete(sequenceName);
    }

    /**
     * 获取所有用户脉冲系列配置名称
     *
     * @return 所有用户脉冲系列配置名称
     */
    public String[] getUserWavePulseSequenceNames() {
        File[] files = sequenceFolder.files();
        String[] names = new String[files.length];
        for (int i = 0; i < files.length; i++) {
            names[i] = files[i].getName();
        }
        return names;
    }


    /**
     * 获取用户脉冲波形
     *
     * @param sequenceName 用户脉冲系列配置名称
     * @return 波形配置
     */
    public WaveConfig getWavePulseSequenceOfUser(String sequenceName) {
        File File = sequenceFolder.getFolder(sequenceName).getFile(waveConfigFileName);
        try {
            return JSON.parseObject(FileUtils.readStringFromFile(File), WaveConfig.class);
        } catch (IOException e) {
            return new WaveConfig();
        }
    }

    /**
     * 获取系统内置脉冲波形
     *
     * @param sequenceName 波形序列名称
     * @return 波形配置
     */
    public WaveConfig getBuiltinWavePulseSequence(String sequenceName) {
        if (sequenceName.equals(BuiltinWaveConfig.headUnitWaveConfig.getSequenceName())) {
            return BuiltinWaveConfig.headUnitWaveConfig;
        } else if (sequenceName.equals(BuiltinWaveConfig.displayWaveConfig.getSequenceName())) {
            return BuiltinWaveConfig.displayWaveConfig;
        } else {
            return BuiltinWaveConfig.undefinedWaveConfig;
        }
    }

}
