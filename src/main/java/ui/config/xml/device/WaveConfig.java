package ui.config.xml.device;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class WaveConfig {

    private String sequenceName; //序列名称

    private String friendlyName; //友好名称
    private List<WavePulse> pulses; //波形数组

    private boolean isBuiltin; //是否内置

    public WaveConfig() {
        pulses = new ArrayList<>();
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WaveConfig that = (WaveConfig) o;
        return isBuiltin == that.isBuiltin && sequenceName.equals(that.sequenceName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sequenceName, isBuiltin);
    }

    @JSONField(serialize = false)
    public boolean isValid() {
        return pulses != null && !pulses.isEmpty();
    }

    public static void main(String[] args) {
        Field[] fields = WaveConfig.class.getDeclaredFields();
        for (Field field : fields) {
            System.out.println(field.getName() + "->" + field.getType());
            System.out.println(field.getType() == field.getAnnotatedType().getType());
        }
    }

}
