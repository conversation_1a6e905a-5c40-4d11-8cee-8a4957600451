package ui.entry;

import bibliothek.extension.gui.dock.theme.SmoothTheme;
import bibliothek.gui.DockFrontend;
import bibliothek.gui.DockStation;
import bibliothek.gui.Dockable;
import bibliothek.gui.dock.DefaultDockable;
import bibliothek.gui.dock.SplitDockStation;
import bibliothek.gui.dock.event.DockFrontendAdapter;
import bibliothek.gui.dock.station.split.SplitDockGrid;
import bibliothek.gui.dock.station.support.PlaceholderStrategy;
import bibliothek.gui.dock.station.support.PlaceholderStrategyListener;
import bibliothek.gui.dock.themes.NoStackTheme;
import bibliothek.util.Path;
import common.config.GlobalExceptionHandler;
import common.constant.AppConstants;
import common.constant.ResourceConstant;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.SseSession;
import sdk.constants.UrlConstants;
import sdk.domain.SmokingTestConfigModel;
import sdk.domain.TestScriptFile;
import sdk.entity.OperationTargetHolder;
import ui.UIDataLoader;
import ui.base.BaseView;
import ui.base.TestView;
import ui.config.json.AllDeviceConfig;
import ui.config.json.DeviceJsonManager;
import ui.config.json.SmokingTestJsonConfig;
import ui.layout.aichat.AIChatMgmtPane;
import ui.layout.bottom.BottomPanelView;
import ui.layout.config.Layout;
import ui.layout.config.UIGlobalConfig;
import ui.layout.left.display.LeftPanelController;
import ui.layout.left.display.LeftPanelView;
import ui.layout.left.display.components.tappane.case_mgmt.CaseMgmtTabPaneView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseControlPanel;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTabPaneView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.TemplateRow;
import ui.layout.left.display.components.tappane.report_mgmt.ReportMgmtPanel;
import ui.layout.left.display.components.treemenu.actiontree.action_mgmt.ActionListMgmtPane;
import ui.layout.left.display.components.treemenu.devicetree.DeviceTree;
import ui.layout.left.display.components.treemenu.devicetree.LeftDeviceMgmtPane;
import ui.layout.left.display.components.treemenu.devicetree.SystemLogPaneView;
import ui.layout.left.display.components.treemenu.templatetree.TemplateListMgmtPane;
import ui.layout.left.display.dialogs.SmokingTestMgmtDialog;
import ui.layout.right.RightPanelController;
import ui.layout.right.RightToolbarPanelView;
import ui.layout.right.components.testcase.TestStep;
import ui.layout.right.components.testcase.TestStepView;
import ui.layout.right.instrument.InstrumentTestPanel;
import ui.layout.top.menubar.MainMenuBar;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.model.testScript.TestScriptEventObserver;
import ui.model.testcase.TestCaseTableEventObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * FlyTest主界面视图
 */
@Slf4j
public class ClientView extends JFrame
        implements BaseView, TestScriptEventObserver, TestCaseTableEventObserver, AppObserver {
    private SplitDockStation station;
    @Getter
    private final JSplitPane mainFrontPane;
    @Getter
    private final DockFrontend frontend = new DockFrontend(this);

    private final MainModel mainModel;
    private final String fixedTitle;
    @Getter
    private final TestStepView testStepView;
    @Getter
    private final LeftDeviceMgmtPane deviceMgmtPane;
    private final ActionListMgmtPane actionMgmtPane;
    private final TemplateListMgmtPane templateMgmtPane;
    private final AIChatMgmtPane aiChatMgmtPane;
    private final SystemLogPaneView systemLogPane;

    @Getter
    private final LeftPanelController leftPanelController;
    @Getter
    private final RightPanelController rightPanelController;
    @Getter
    private final Dimension windowsSize;
    @Getter
    private final Dimension leftDisplaySize;
    @Getter
    private final RightToolbarPanelView rightToolbarPanelView;

    @Getter
    private JButton deviceMgmtButton;
    @Getter
    private JButton actionMgmtButton;
    private JButton templateMgmtButton;
    @Getter
    private JButton smokingTestButton;
    private JButton copilotButton;
    @Getter
    private JButton logMgmtButton;
    private JButton testOperationButton; // 仪表测试面板按钮
    @Getter
    private DefaultDockable deviceMgmtDockable;
    private DefaultDockable copilotDockable;
    @Getter
    private DefaultDockable actionListDockable;
    private DefaultDockable templateListDockable;
    private DefaultDockable mainFrontDockable;
    private DefaultDockable systemLogDockable;
    private InstrumentTestPanel instrumentTestPanel;

    private final ImageIcon connectedIcon;
    private final ImageIcon disconnectIcon;
    private final SseSession sseSession;
    private final ExecutorService executorService;
    private final List<Runnable> runOnClose = new ArrayList<>();
    private String title; //窗口标题
    @Getter
    private List<TemplateRow> selectedRowTemplateList = new ArrayList<>();
    private MainMenuBar mainMenuBar;
    private TestView currentTestView = TestView.OPERATION_STEP;
    private final static String UPGRADE_NOTIFICATION_SUBSCRIBE_ID = "upgradeNotification";
    private final static String DEVICE_DISCONNECT_WARNING = "deviceDisconnectWarning";

    private void configDevice() {
        AllDeviceConfig allDeviceConfig = DeviceJsonManager.getDeviceJsonManager().readDeviceConfig();
        if (allDeviceConfig != null) {
            //初始化配置
            mainModel.getAllDeviceConfig().setDevices(allDeviceConfig.getDevices());
        }
    }

    public ClientView(MainModel mainModel) throws HeadlessException {
        // 设置全局异常处理器
        UIDataLoader.load();
        this.mainModel = mainModel;
        windowsSize = new Dimension(AppConstants.APP_DEFAULT_WIDTH, AppConstants.APP_DEFAULT_HEIGHT);
        connectedIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.CommonLayout.connectedIconPath);
        disconnectIcon = SwingUtil.getResourceAsImageIcon(ResourceConstant.CommonLayout.disconnectIconPath);
        mainFrontPane = new JSplitPane();
        leftPanelController = new LeftPanelController(this, mainModel);
        leftDisplaySize = leftPanelController.getPanelView().getPreferredSize();
        rightPanelController = new RightPanelController(this, mainModel);
        rightToolbarPanelView = new RightToolbarPanelView(this, mainModel);
        testStepView = new TestStepView(this, mainModel);
        deviceMgmtPane = new LeftDeviceMgmtPane(this, mainModel);
        //设定为左右拆分布局
        actionMgmtPane = new ActionListMgmtPane(this, mainModel);
        templateMgmtPane = new TemplateListMgmtPane(this, mainModel);
        aiChatMgmtPane = new AIChatMgmtPane(mainModel);
        systemLogPane = new SystemLogPaneView(this, mainModel);
        sseSession = new SseSession();
        //executorService = Executors.newSingleThreadExecutor();
        //监控两个SSE会话
        executorService = Executors.newFixedThreadPool(2, r -> {
            Thread thread = new Thread(r);
            thread.setName(String.format("ClientViewThread-%d", thread.getId()));
            return thread;
        });
        SwingUtil.windowMaximized(this);
        setIconImage(Objects.requireNonNull(SwingUtil.getResourceAsImageIcon(ResourceConstant.IconLayout.faviconIconPath)).getImage());
        configDevice();
        title = String.format("%s %s", AppConstants.APP_NAME, mainModel.getAppInfo().getAppVersion());
        setTitle(title);
        fixedTitle = getTitle();
        registerModelObservers();
        createView();
        createActions();
        updateButtonMenu();
        GlobalExceptionHandler handler = GlobalExceptionHandler.getInstance();
        handler.setParentComponent(this);
        startDeviceDisconnectMonitor();
    }

    private void startDeviceDisconnectMonitor() {
        executorService.submit(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl(DEVICE_DISCONNECT_WARNING),
                        line -> {
                            String msg = line.substring(line.lastIndexOf(":") + 1).replace("\"", "");
                            SwingUtil.showWarningDialog(this, msg);
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });

    }

    public boolean isStepScriptView() {
        return currentTestView == TestView.OPERATION_STEP;
    }

    public boolean isActionSequenceView() {
        return currentTestView == TestView.ACTION_SEQUENCE;
    }

    public void updateButtonMenu() {
        //按钮禁用、点击
        int index = leftPanelController.getPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().getSelectedIndex();
        if (index == 0) {
            smokingTestButton.setEnabled(false);
            mainMenuBar.iconStatusSetting(true);
        } else {
            actionMgmtButton.setEnabled(false);
            logMgmtButton.setEnabled(false);
            mainMenuBar.iconStatusSetting(false);
        }
    }

    public void updateTitle() {
        if (!isNotNull()) {
            title = String.format("%s %s", AppConstants.APP_NAME, mainModel.getAppInfo().getAppVersion());
            setTitle(title);
            return;
        }
        boolean saveExcel = leftPanelController.getPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().
                getExcelCaseTabPaneView().getExcelCaseControlPanel().isSaveExcel();
        title = String.format("%s %s %s", AppConstants.APP_NAME, mainModel.getAppInfo().getAppVersion(), saveExcel ? "" : "[未保存用例]");
        setTitle(title);
    }


    public boolean isNotNull() {
//        return Optional.ofNullable(leftPanelController)
//                .map(LeftPanelController::getPanelView)
//                .map(LeftPanelView::getDisplayTabPane)
//                .map(DisplayTabPaneView::getCaseMgmtTabPaneView)
//                .map(CaseMgmtTabPaneView::getExcelCaseTabPaneView)
//                .map(ExcelCaseTabPaneView::getExcelCaseControlPanel).isPresent();
        LeftPanelController leftPanelController = getLeftPanelController();
        if (leftPanelController == null) return false;

        LeftPanelView panelView = leftPanelController.getPanelView();
        if (panelView == null) return false;

        CaseMgmtTabPaneView caseMgmtTabPaneView = panelView.getDisplayTabPane().getCaseMgmtTabPaneView();
        if (caseMgmtTabPaneView == null) return false;

        ExcelCaseTabPaneView excelCaseTabPaneView = caseMgmtTabPaneView.getExcelCaseTabPaneView();
        if (excelCaseTabPaneView == null) return false;

        ExcelCaseControlPanel excelCaseControlPanel = excelCaseTabPaneView.getExcelCaseControlPanel();
        if (excelCaseControlPanel == null) return false;
        return true;
    }

    public DeviceTree getDeviceTree() {
        return deviceMgmtPane.getDeviceManagerTreeView().getDeviceTree();
    }

    public ReportMgmtPanel getTestReportPanel() {
        return getLeftPanelController().getLeftPanelView().getDisplayTabPane().getReportMgmtPanel();
    }

    @Override
    public void createView() {
        Layout layout = UIGlobalConfig.getInstance().getUILayout().getLayout();
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setSize(windowsSize);
        mainFrontPane.setOneTouchExpandable(true);//让分割线显示出箭头
        mainFrontPane.setContinuousLayout(true);//操作箭头，重绘图形
        mainFrontPane.setOrientation(JSplitPane.HORIZONTAL_SPLIT);//设置分割线方向
        mainFrontPane.setDividerSize(3);//设置分割线的宽度
        mainFrontPane.setLeftComponent(leftPanelController.getPanelView());
        setDrawerComponent(rightPanelController.getPanelView());
        mainMenuBar = new MainMenuBar(mainModel, this);
        mainMenuBar.setLeftDeviceMgmtPane(deviceMgmtPane);
        setJMenuBar(mainMenuBar);
        destroyOnClose(frontend);
        // 使用无堆叠主题，并设置特殊属性来隐藏标题栏
        frontend.getController().setTheme(new NoStackTheme(new SmoothTheme()));

        // 注释：设置属性来最小化标题栏高度（这些属性可能不被支持）
        // frontend.getController().getProperties().set("basic.title.height", 0);
        // frontend.getController().getProperties().set("title.height", 0);
        station = new SplitDockStation();
        add(station);
        frontend.addRoot("split", station);
        deviceMgmtDockable = new DefaultDockable(deviceMgmtPane, "设备管理");
        actionListDockable = new DefaultDockable(actionMgmtPane, "动作列表");
        templateListDockable = new DefaultDockable(templateMgmtPane, "图像列表");
        // 最终方案：直接使用JSplitPane替代Dockable实现无框效果
        mainFrontDockable = createTrulyFramelessDockable(mainFrontPane, "主页面板");
        // 创建一个包装容器，完全绕过Dockable的标题栏机制
//        mainFrontDockable = createDirectFramelessDockable(mainFrontPane);
        copilotDockable = new DefaultDockable(aiChatMgmtPane, "AI助手");
        systemLogDockable = new DefaultDockable(systemLogPane, "系统日志");
        frontend.addDockable("leftTop", deviceMgmtDockable);
        frontend.addDockable("leftBottom", copilotDockable);
        frontend.addDockable("rightTop", actionListDockable);
        frontend.addDockable("rightCenter", templateListDockable);
        frontend.addDockable("right", mainFrontDockable);
        frontend.addDockable("bottom", systemLogDockable);
        frontend.getController().getProperties().set(
                PlaceholderStrategy.PLACEHOLDER_STRATEGY,
                new CustomPlaceholderStrategy());
        SplitDockGrid grid = new SplitDockGrid();

        // 初始只添加deviceMgmtDockable，copilotDockable默认隐藏
        grid.addDockable(layout.getLeftTopPane().getX(), layout.getLeftTopPane().getY(),
                layout.getLeftTopPane().getWidth(), layout.getLeftTopPane().getHeight(), deviceMgmtDockable);
//        grid.addDockable(leftTopPane.getX(), leftTopPane.getY(), leftTopPane.getWidth(), leftTopPane.getHeight(), copilotDockable);

        grid.addDockable(layout.getLeftCenterPane().getX(), layout.getLeftCenterPane().getY(),
                layout.getLeftCenterPane().getWidth(), layout.getLeftCenterPane().getHeight(), actionListDockable);

        grid.addDockable(layout.getLeftRightPane().getX(), layout.getLeftRightPane().getY(),
                layout.getLeftRightPane().getWidth(), layout.getLeftRightPane().getHeight(), templateListDockable);

        grid.addDockable(layout.getRightPane().getMainFrontView().getX(), layout.getRightPane().getMainFrontView().getY(),
                layout.getRightPane().getMainFrontView().getWidth(), layout.getRightPane().getMainFrontView().getHeight(), mainFrontDockable);

        grid.addDockable(layout.getRightPane().getMainFrontView().getX(), (double) getHeight() / 2,
                layout.getRightPane().getMainFrontView().getWidth(), (double) getHeight() / 2, systemLogDockable);

        int MINIMUM_WIDTH = 150;
        deviceMgmtDockable.getComponent().setMinimumSize(new Dimension(MINIMUM_WIDTH, 0));
        actionListDockable.getComponent().setMinimumSize(new Dimension(MINIMUM_WIDTH, 0));
        templateListDockable.getComponent().setMinimumSize(new Dimension(MINIMUM_WIDTH, 0));
        station.dropTree(grid.toTree());

        //左边侧工具栏
        JToolBar leftSideToolbar = new JToolBar(JToolBar.VERTICAL);
        leftSideToolbar.setOrientation(JToolBar.VERTICAL);
        leftSideToolbar.setLayout(new BoxLayout(leftSideToolbar, BoxLayout.Y_AXIS));
        deviceMgmtButton = createButton(deviceMgmtDockable, frontend);
        actionMgmtButton = createButton(actionListDockable, frontend);
        templateMgmtButton = createButton(templateListDockable, frontend);
        logMgmtButton = createButton(systemLogDockable, frontend);
        copilotButton = createButton(copilotDockable, frontend);
        smokingTestButton = new JButton(SwingUtil.convertToVertical("冒烟配置"));
        smokingTestButton.setHorizontalTextPosition(SwingConstants.CENTER);
        smokingTestButton.setVerticalTextPosition(SwingConstants.BOTTOM);
        smokingTestButton.addActionListener(e -> new SmokingTestMgmtDialog(mainModel, this).setVisible(true));
        leftSideToolbar.addSeparator();
        leftSideToolbar.add(deviceMgmtButton);
        leftSideToolbar.addSeparator();
        leftSideToolbar.add(smokingTestButton);
        leftSideToolbar.addSeparator();
        leftSideToolbar.add(copilotButton);

        JToolBar bottomSideToolbar = new JToolBar(JToolBar.VERTICAL);
        bottomSideToolbar.setOrientation(JToolBar.VERTICAL);
        bottomSideToolbar.setLayout(new BoxLayout(bottomSideToolbar, BoxLayout.Y_AXIS));
        bottomSideToolbar.addSeparator();
        bottomSideToolbar.add(logMgmtButton);
        bottomSideToolbar.addSeparator();

        JPanel panel = new JPanel(new BorderLayout());
        panel.add(leftSideToolbar, BorderLayout.NORTH);
        panel.add(bottomSideToolbar, BorderLayout.SOUTH);

        add(panel, BorderLayout.WEST);
        add(getBottomPanelView(), BorderLayout.SOUTH);
        add(rightToolbarPanelView, BorderLayout.EAST);

        //右边侧工具栏
        JToolBar rightSideToolbar = new JToolBar(JToolBar.VERTICAL);
        rightSideToolbar.setOrientation(JToolBar.VERTICAL);
        rightSideToolbar.setLayout(new BoxLayout(rightSideToolbar, BoxLayout.Y_AXIS));
        rightSideToolbar.addSeparator();
        rightSideToolbar.add(actionMgmtButton);
        rightSideToolbar.addSeparator();
        rightSideToolbar.add(templateMgmtButton);
        rightSideToolbar.addSeparator();

        // 添加测试操作按钮到右侧工具栏，位于图像列表下方
        testOperationButton = new JButton(SwingUtil.convertToVertical("仪表测试面板"));
        testOperationButton.setHorizontalTextPosition(SwingConstants.CENTER);
        testOperationButton.setVerticalTextPosition(SwingConstants.BOTTOM);

        rightSideToolbar.add(testOperationButton);
        rightSideToolbar.addSeparator();

        rightToolbarPanelView.add(rightSideToolbar, BorderLayout.NORTH);
        SwingUtil.centerInScreen(this, false);
        setVisible(true);
        syncSmokingTestConfig();
        //获取脚本还是动作序列视图
        leftPanelController.getPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().setSelectedIndex(
                UIGlobalConfig.getInstance().getUILayout().getViewTabIndex());
        hideLog();
        setDefaultLayout();

        // 应用无标题栏配置
        applyFramelessConfig();

        repaint();
        invalidate();
    }

    @Override
    public void showTemplateInfo(List<TemplateRow> selectedRowTemplateList, List<TemplateRow> selectedRowFailPicList) {
        this.selectedRowTemplateList = selectedRowTemplateList;
        SwingUtilities.invokeLater(() -> {
            if (selectedRowTemplateList != null) {
                frontend.show(templateListDockable);
            }
            if (selectedRowTemplateList != null && selectedRowTemplateList.isEmpty()) {
                frontend.hide(templateListDockable);
            }
        });
    }

    private void syncSmokingTestConfig() {
        new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                SwingUtilities.invokeLater(() -> setSmokingTestIcon());
                SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
                OperationTargetHolder.getSmokingTestKit().smokingTestConfig(smokingTestConfig);
                return null;
            }
        }.execute();
    }

    public void setSmokingTestIcon() {
        SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
        if (smokingTestConfig.isListeningUpgradeNotification()) {
            smokingTestButton.setIcon(connectedIcon);
            startUpgradeMonitor();
        } else {
            smokingTestButton.setIcon(disconnectIcon);
            OperationTargetHolder.getSmokingTestKit().closeUpgradeMonitor();
        }
    }


    private void startUpgradeMonitor() {
        executorService.submit(() -> {
            try {
                sseSession.readStream(UrlConstants.getSseUrl(UPGRADE_NOTIFICATION_SUBSCRIBE_ID),
                        line -> {
                            String msg = line.substring(line.lastIndexOf(":") + 1).replace("\"", "");
                            mainModel.getTestCaseTableModel().upgradeOperation(msg);
                        });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private void saveUILayout() {
        Layout layout = UIGlobalConfig.getInstance().getUILayout().getLayout();

        // 更新deviceMgmtPane的实际位置到配置
        Component deviceMgmtComponent = deviceMgmtDockable.getComponent();
        Rectangle bounds = deviceMgmtComponent.getBounds();
        layout.getLeftTopPane().setX(bounds.x);
        layout.getLeftTopPane().setY(bounds.y);
        layout.getLeftTopPane().setWidth(bounds.width);
        layout.getLeftTopPane().setHeight(bounds.height);

        layout.getRightPane().getMainFrontView().setX(deviceMgmtPane.getWidth());
        layout.getRightPane().getMainFrontView().setWidth(mainFrontPane.getWidth());
        layout.getRightPane().getMainFrontView().setHeight(mainFrontPane.getHeight());

        layout.getLeftCenterPane().setY(actionMgmtPane.getY());
        layout.getLeftCenterPane().setX(mainFrontPane.getWidth() + deviceMgmtPane.getWidth());
        layout.getLeftCenterPane().setWidth(actionMgmtPane.getWidth());
        layout.getLeftCenterPane().setHeight(actionMgmtPane.getHeight());

        layout.getLeftRightPane().setY(actionMgmtPane.getY());
        layout.getLeftRightPane().setX(mainFrontPane.getWidth() + deviceMgmtPane.getWidth());
        layout.getLeftRightPane().setWidth(actionMgmtPane.getWidth());
        layout.getLeftRightPane().setHeight(templateMgmtPane.getHeight());

        UIGlobalConfig.getInstance().getUILayout().setViewTabIndex(leftPanelController.
                getPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().getSelectedIndex());

        // 保存配置
        UIGlobalConfig.getInstance().save();
    }

    @Override
    public void switchView(TestView testView) {
        currentTestView = testView;
        adaptDrawerWidth();
    }

    private void adaptDrawerWidth() {
        SwingUtil.invokeLater(() -> {
            Layout layout = UIGlobalConfig.getInstance().getUILayout().getLayout();
            // 获取默认工具包
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            // 获取屏幕尺寸
            Dimension screenSize = toolkit.getScreenSize();
            // 获取屏幕宽度
            int screenWidth = (int) screenSize.getWidth();
            if (currentTestView == TestView.OPERATION_STEP) {
                int location = (int) (layout.getRightPane().getStepViewRatio() * screenWidth);
//            log.info("设置步骤视图位置: {}", location);
                mainFrontPane.setDividerLocation(location);
            }
            if (currentTestView == TestView.ACTION_SEQUENCE) {
                int location = (int) (layout.getRightPane().getActionSequenceViewRatio() * screenWidth);
//            log.info("设置动作序列视图位置: {}", location);
                mainFrontPane.setDividerLocation(location);
            }
        });

    }

    @Override
    public void createActions() {
        mainFrontPane.addPropertyChangeListener(JSplitPane.DIVIDER_LOCATION_PROPERTY, e -> {
            Layout layout = UIGlobalConfig.getInstance().getUILayout().getLayout();
            if (mainFrontPane.getRightComponent().isVisible()) {
                // 获取默认工具包
                Toolkit toolkit = Toolkit.getDefaultToolkit();
                // 获取屏幕尺寸
                Dimension screenSize = toolkit.getScreenSize();
                // 获取屏幕宽度
                int screenWidth = (int) screenSize.getWidth();
                if (isStepScriptView()) {
                    double scriptViewRatio = (double) mainFrontPane.getDividerLocation() / screenWidth;
                    layout.getRightPane().setStepViewRatio(scriptViewRatio);
                    UIGlobalConfig.getInstance().save();
                }
                if (isActionSequenceView()) {
                    double actionSequenceViewRatio = (double) mainFrontPane.getDividerLocation() / screenWidth;
                    layout.getRightPane().setActionSequenceViewRatio(actionSequenceViewRatio);
                    UIGlobalConfig.getInstance().save();
                }
            }
        });


        // 设置按钮点击事件监听器，点击时显示仪表测试面板
        testOperationButton.addActionListener(e -> {
            // 先关闭已存在的窗口
            if (instrumentTestPanel != null) {
                instrumentTestPanel.closeTestPanel(); // 或者用 setVisible(false) 取决于你的InstrumentTestPanel实现
            }
            // 创建新窗口
            instrumentTestPanel = new InstrumentTestPanel(mainModel);
        });

        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                int buttonValue = JOptionPane.showConfirmDialog(null,
                        "确定要退出程序？",
                        "系统提示",
                        JOptionPane.YES_NO_OPTION);
                if (buttonValue == JOptionPane.YES_OPTION) {
                    shutdownApp();
                    System.exit(0);
                }
            }

            public void windowClosed(WindowEvent e) {
                for (Runnable onClose : runOnClose) {
                    onClose.run();
                }
            }

            @Override
            public void windowDeiconified(WindowEvent e) {

            }
        });
        //统一处理
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdownApp));
    }

    public void shutdownApp() {
        log.info("客户端人为关闭，通知服务端退出");
        if (executorService != null) {
            executorService.shutdownNow();
        }
        mainModel.getAppModel().appExit();
        rightPanelController.getCaseToolkitView().forceStopScript();
        OperationTargetHolder.getTestClientKit().exitClient(mainModel.getAppInfo());
        saveUILayout();
        OperationTargetHolder.unregisterAllDevices();
    }

    public void setRightPanelViewContent(int tabbedIndex) {
        SwingUtil.invokeLater(() -> {
            if (tabbedIndex == 1) {
                setDrawerComponent(testStepView);
            } else {
                setDrawerComponent(rightPanelController.getPanelView());
            }
            repaint();
        });
    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestScriptEventModel().registerObserver(this);
        mainModel.getTestCaseTableModel().registerObserver(this);
        mainModel.getAppModel().registerObserver(this);
        mainModel.getAppModel().uiStable();
    }

    /**
     * 设置右侧抽屉面板
     *
     * @param component
     */
    public void setDrawerComponent(JComponent component) {
        SwingUtil.invokeLater(() -> {
            mainFrontPane.setRightComponent(component);//添加动作序列面板
        });
    }

    public void displayDrawerView(boolean visible) {
        SwingUtil.invokeLater(() -> {
            mainFrontPane.getRightComponent().setVisible(visible);
            if (visible) {
                if (mainFrontPane.getRightComponent().isVisible()) {
                    switchView(TestView.OPERATION_STEP);
                } else {
                    switchView(TestView.ACTION_SEQUENCE);
                }
            }
            mainFrontPane.revalidate();
            mainFrontPane.repaint();
        });
    }

    public void setDefaultLayout() {
        SwingUtil.invokeLater(() -> {
            templateMgmtButton.doClick();
            actionMgmtButton.doClick();
//            copilotButton.doClick();
        });
    }

    @Override
    public void newScript(TestScriptFile testScriptFile) {
        displayDrawerView(true);
    }

    @Override
    public void switchScript(TestScriptFile testScriptFile) {

    }

    @Override
    public void clearScript() {

    }

    @Override
    public void testCaseLoaded() {

    }

    @Override
    public void updateExcelCaseTable(int row, List<TestStep> testStepList) {
        SwingUtil.invokeLater(() -> leftPanelController.getPanelView().getDisplayTabPane().getCaseMgmtTabPaneView()
                .getExcelCaseTabPaneView().getExcelCaseRenderTabbedPane().
                getSelectedExcelCaseTable().updateExcelCaseTableData(row, testStepList));

    }

    public BottomPanelView getBottomPanelView() {
        return leftPanelController.getPanelView().getBottomPanelView();
    }

    public void setAppTitle(String title) {
        setTitle(String.format("%s <%s>", fixedTitle, title));
    }

    public void updateCollapseIconAndSetRightView(boolean isClose) {
        SwingUtil.invokeLater(() -> rightToolbarPanelView.updateStepOrActionSideView(isClose));
    }

    public void runOnClose(Runnable run) {
        runOnClose.add(run);
    }

    public void destroyOnClose(final DockFrontend frontend) {
        runOnClose(frontend::kill);
    }


    private class CustomPlaceholderStrategy implements PlaceholderStrategy {
        public void addListener(PlaceholderStrategyListener listener) {
            // ignore
        }

        public Path getPlaceholderFor(Dockable dockable) {
            /* The placeholder for a ColorDockable is the unique identifier used
             * in our DockFrontend */
            if (dockable instanceof DefaultDockable) {
                String titleText = dockable.getTitleText();

                // 处理空标题的情况，使用特殊标识符
                if (titleText == null || titleText.trim().isEmpty()) {
                    // 为主页面板使用特殊标识符
                    if (dockable == mainFrontDockable) {
                        return new Path("MAIN_FRONT_PANEL");
                    }

                    // 尝试获取内部标题（如果是我们自定义的无框Dockable）
                    try {
                        // 首先尝试getInternalTitle方法
                        java.lang.reflect.Method getInternalTitleMethod = dockable.getClass().getMethod("getInternalTitle");
                        String internalTitle = (String) getInternalTitleMethod.invoke(dockable);
                        if (internalTitle != null && !internalTitle.trim().isEmpty()) {
                            return new Path(internalTitle);
                        }
                    } catch (Exception e) {
                        // 如果没有getInternalTitle方法，尝试getInternalIdentifier
                        try {
                            java.lang.reflect.Method getInternalIdentifierMethod = dockable.getClass().getMethod("getInternalIdentifier");
                            String internalId = (String) getInternalIdentifierMethod.invoke(dockable);
                            if (internalId != null && !internalId.trim().isEmpty()) {
                                return new Path(internalId);
                            }
                        } catch (Exception ex) {
                            // 使用默认处理
                        }
                    }

                    // 为其他空标题组件生成唯一标识符
                    return new Path("FRAMELESS_" + System.identityHashCode(dockable));
                }
                return new Path(titleText);
            } else {
                return null;
            }
        }

        public void install(DockStation station) {
            // ignore
        }

        public boolean isValidPlaceholder(Path placeholder) {
            /* Any placeholder is valid, we do not care about old placeholders that
             * are no longer used. */
            return true;
        }

        public void removeListener(PlaceholderStrategyListener listener) {
            // ignore
        }

        public void uninstall(DockStation station) {
            // ignore
        }
    }

    private JButton createButton(final DefaultDockable observed, final DockFrontend frontend) {
        final JButton button = new JButton(SwingUtil.convertToVertical(observed.getTitleText()));
        frontend.addFrontendListener(new DockFrontendAdapter() {
            @Override
            public void shown(DockFrontend frontend, Dockable dockable) {
                if (dockable == observed) {
                    button.setSelected(true);

                    // 特殊处理deviceMgmtDockable和copilotDockable
                    if ((dockable == deviceMgmtDockable || dockable == copilotDockable) &&
                            frontend.isShown(deviceMgmtDockable) && frontend.isShown(copilotDockable)) {
                        // 两者都可见时调整布局
                        arrangeDockables();
                    }
                }
            }

            @Override
            public void hidden(DockFrontend frontend, Dockable dockable) {
                if (dockable == observed) {
                    button.setSelected(false);

                    // 如果其中一个被隐藏，另一个应该占据整个区域
                    if ((dockable == deviceMgmtDockable && frontend.isShown(copilotDockable)) ||
                            (dockable == copilotDockable && frontend.isShown(deviceMgmtDockable))) {
                        // 调整布局
                        arrangeDockables();
                    }
                }
            }
        });
        button.addActionListener(e -> {
            // 保存当前分割位置
            if (button.isSelected()) {
                button.setBackground(null);
                frontend.hide(observed);
            } else {
                button.setBackground(Color.lightGray);
                frontend.show(observed);
            }
        });
        button.setSelected(frontend.isShown(observed));
        return button;
    }

    /**
     * 安排deviceMgmtDockable和copilotDockable的布局
     * 当两者都显示时，上下排列；当只有一个显示时，占据整个区域
     */
    private void arrangeDockables() {
        SplitDockStation station = (SplitDockStation) frontend.getRoot("split");
        SplitDockGrid grid = new SplitDockGrid();
        Layout layout = UIGlobalConfig.getInstance().getUILayout().getLayout();

        // 保存当前分割位置
        final int currentDividerLocation = mainFrontPane.getDividerLocation();

        if (frontend.isShown(deviceMgmtDockable) && frontend.isShown(copilotDockable)) {
            // 两者都显示，上下排列
            double deviceHeight = layout.getLeftTopPane().getHeight() * 0.5;
            double copilotHeight = layout.getLeftTopPane().getHeight() * 0.5;

            grid.addDockable(layout.getLeftTopPane().getX(), layout.getLeftTopPane().getY(),
                    layout.getLeftTopPane().getWidth(), deviceHeight, deviceMgmtDockable);
            grid.addDockable(layout.getLeftTopPane().getX(), layout.getLeftTopPane().getY() + deviceHeight,
                    layout.getLeftTopPane().getWidth(), copilotHeight, copilotDockable);
        } else if (frontend.isShown(deviceMgmtDockable)) {
            // 只显示设备管理
            grid.addDockable(layout.getLeftTopPane().getX(), layout.getLeftTopPane().getY(),
                    layout.getLeftTopPane().getWidth(), layout.getLeftTopPane().getHeight(), deviceMgmtDockable);
        } else if (frontend.isShown(copilotDockable)) {
            // 只显示AI助手
            grid.addDockable(layout.getLeftTopPane().getX(), layout.getLeftTopPane().getY(),
                    layout.getLeftTopPane().getWidth(), layout.getLeftTopPane().getHeight(), copilotDockable);
        }

        // 添加其他已显示的组件到grid
        if (frontend.isShown(actionListDockable)) {
            grid.addDockable(layout.getLeftCenterPane().getX(), layout.getLeftCenterPane().getY(),
                    layout.getLeftCenterPane().getWidth(), layout.getLeftCenterPane().getHeight(), actionListDockable);
        }

        if (frontend.isShown(templateListDockable)) {
            grid.addDockable(layout.getLeftRightPane().getX(), layout.getLeftRightPane().getY(),
                    layout.getLeftRightPane().getWidth(), layout.getLeftRightPane().getHeight(), templateListDockable);
        }

        // 主面板一定要添加
        grid.addDockable(layout.getRightPane().getMainFrontView().getX(), layout.getRightPane().getMainFrontView().getY(),
                layout.getRightPane().getMainFrontView().getWidth(), layout.getRightPane().getMainFrontView().getHeight(),
                mainFrontDockable);

        if (frontend.isShown(systemLogDockable)) {
            grid.addDockable(layout.getRightPane().getMainFrontView().getX(), (double) getHeight() / 2,
                    layout.getRightPane().getMainFrontView().getWidth(), (double) getHeight() / 2, systemLogDockable);
        }

        // 应用布局
        station.dropTree(grid.toTree());

        // 恢复splitPane分割位置
        SwingUtilities.invokeLater(() -> {
            mainFrontPane.setDividerLocation(currentDividerLocation);
        });
    }

    public void hideLog() {
        SwingUtil.invokeLater(() -> getFrontend().hide(systemLogDockable));
    }

    /**
     * 创建真正无标题栏的 Dockable 组件
     * 修正版本：确保正确处理边框
     */
    private DefaultDockable createTrulyFramelessDockable(JComponent component, String title) {
        // 创建包装面板
        JPanel wrapperPanel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                // 不绘制任何背景
            }

            @Override
            public Dimension getPreferredSize() {
                return component.getPreferredSize();
            }
        };

        wrapperPanel.setOpaque(false);
        wrapperPanel.setBorder(null);
        wrapperPanel.add(component, BorderLayout.CENTER);

        // 创建自定义 Dockable
        DefaultDockable dockable = new DefaultDockable(wrapperPanel, "") {
            private String internalIdentifier = title;

            @Override
            public String getTitleText() {
                return null; // 完全不显示标题
            }

            @Override
            public Icon getTitleIcon() {
                return null;
            }

            @Override
            public void setTitleText(String title) {
                // 忽略所有标题设置
            }

            @Override
            public void setTitleIcon(Icon icon) {
                // 忽略所有图标设置
            }

            // 提供内部标识符用于 PlaceholderStrategy
            public String getInternalIdentifier() {
                return internalIdentifier;
            }
        };

        return dockable;
    }



    /**
     * 应用无标题栏配置，通过隐藏标题组件的兄弟组件实现
     * 这是一个更安全、更精确的方法
     */
    private void applyFramelessConfig() {
        SwingUtilities.invokeLater(() -> {
            // 再次延迟以确保UI已经完全布局
            SwingUtilities.invokeLater(() -> {
                if (mainFrontDockable != null && mainFrontDockable.getComponent() != null) {
                    Component content = mainFrontDockable.getComponent();
                    Container parent = content.getParent();

                    if (parent != null) {
                        // 隐藏内容面板的所有兄弟组件（通常是标题栏）
                        for (Component sibling : parent.getComponents()) {
                            if (sibling != content) {
                                sibling.setVisible(false);
                                Dimension zeroSize = new Dimension(0, 0);
                                sibling.setPreferredSize(zeroSize);
                                sibling.setMinimumSize(zeroSize);
                                sibling.setMaximumSize(zeroSize);
                            }
                        }

                        // 容器本身也可能带有边框或边距
                        if (parent instanceof JComponent) {
                            ((JComponent) parent).setBorder(null);
                        }

                        parent.revalidate();
                        parent.repaint();
                    }
                }
            });
        });
    }

    /**
     * 创建直接无框的 Dockable - 最终解决方案
     * 通过自定义实现完全绕过标题栏显示机制
     */
    private DefaultDockable createDirectFramelessDockable(JComponent component) {
        JPanel wrapperPanel = new JPanel(new BorderLayout());
        wrapperPanel.setOpaque(false);
        wrapperPanel.setBorder(null);

        // 确保内容组件也无边框
        if (component != null) {
            component.setBorder(null);
            wrapperPanel.add(component, BorderLayout.CENTER);
        }

        // 返回null以避免创建标题
        return new DefaultDockable(wrapperPanel, "") {
            @Override
            public String getTitleText() {
                return null; // 返回null以避免创建标题
            }

            @Override
            public Icon getTitleIcon() {
                return null;
            }
        };
    }
}
