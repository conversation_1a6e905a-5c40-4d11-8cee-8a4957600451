package ui.model.device;

import sdk.domain.Device;
import ui.base.treelist.AbstractTreeNode;
import ui.model.ModelObserver;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/14 16:01
 * @description :
 * @modified By :
 * @since : 2023/7/14
 **/
public interface DeviceItemMenuObserver extends ModelObserver {
    default void connectDevice(Device device, boolean tryConnect, boolean autoOpenChannel) {
        connectDevice(device, tryConnect, autoOpenChannel, device.isSimulated());
    }

    void connectDevice(Device device, boolean tryConnect, boolean autoOpenChannel, boolean simulated);

    void simulateDevice(Device device);

    void disconnectDevice(Device device);

    void disconnectDevice(String deviceType, String deviceName, String deviceAliasName);

    void editDevice(AbstractTreeNode parentNode, Device device);

    void renameDevice(Device device);

    void removeDevice(Device device);
}
