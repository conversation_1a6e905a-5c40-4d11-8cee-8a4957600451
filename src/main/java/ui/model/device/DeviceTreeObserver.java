package ui.model.device;

import sdk.domain.Device;
import ui.layout.left.display.components.treemenu.devicetree.impl.DeviceNode;
import ui.model.ModelObserver;

public interface DeviceTreeObserver extends ModelObserver {

    void addDevice(DeviceNode deviceNode, Device device, boolean enableAutoRename);

    void connectDevicesByDeviceType(String deviceType);

    void disconnectDevicesByDeviceType(String deviceType);

}
