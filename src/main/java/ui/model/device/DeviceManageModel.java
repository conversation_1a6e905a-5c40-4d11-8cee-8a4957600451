package ui.model.device;

import sdk.domain.Device;
import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-15 15:35
 * @description :
 * @modified By :
 * @since : 2022-6-15
 */
public class DeviceManageModel implements BaseModel, ModelObservable, DeviceManageObserver {

    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        for (ModelObserver observer : modelObservers) {
            ((DeviceManageObserver) observer).deviceConnected(device, autoOpenChannel);
        }
    }

    @Override
    public void deviceChannelConnected(Device device) {
        for (ModelObserver observer : modelObservers) {
            ((DeviceManageObserver) observer).deviceChannelConnected(device);
        }
    }

    @Override
    public void deviceDisconnected(Device device) {
        for (ModelObserver observer : modelObservers) {
            ((DeviceManageObserver) observer).deviceDisconnected(device);
        }
    }

    /**
     * 通知所有观察者设备已被删除，需要释放资源
     * @param device 被删除的设备
     */
    @Override
    public void deviceRemoved(Device device) {
        for (ModelObserver observer : modelObservers) {
            ((DeviceManageObserver) observer).deviceRemoved(device);
        }
    }


}
