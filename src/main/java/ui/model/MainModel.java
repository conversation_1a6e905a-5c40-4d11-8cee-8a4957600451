package ui.model;

import common.utils.DateUtils;
import common.utils.NetworkUtils;
import lombok.Getter;
import lombok.Setter;
import ui.base.AppInfo;
import ui.base.BaseModel;
import ui.base.treelist.TestVersion;
import ui.config.json.AllDeviceConfig;
import ui.layout.left.display.components.container.can.model.CanModel;
import ui.layout.left.display.components.container.electric_relay.ElectricRelayContainer;
import ui.layout.right.components.log.LogModel;
import ui.layout.right.components.taskManagement.TaskModel;
import ui.layout.right.components.testcase.TestStepModel;
import ui.model.app.AppModel;
import ui.model.app.FrameSizeModel;
import ui.model.app.UIModel;
import ui.model.device.*;
import ui.model.operation.OperationGroupModel;
import ui.model.operation.OperationModel;
import ui.model.project.ProjectModel;
import ui.model.report_point.ReportedPointModel;
import ui.model.report_point.ResolutionModel;
import ui.model.testScript.*;
import ui.model.test_executor.TestExecuteStatusModel;
import ui.model.testcase.TestCaseTableModel;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-8 10:24
 * @description :
 * @modified By :
 * @since : 2022-4-8
 */
@Getter
public class MainModel implements BaseModel {

    @Setter
    private AppInfo appInfo = new AppInfo();
    @Setter
    private TestVersion testVersion = new TestVersion();
    private final AllDeviceConfig allDeviceConfig = new AllDeviceConfig();

    private final TestCaseTableModel testCaseTableModel = new TestCaseTableModel();
    private final TestStepModel testStepModel = new TestStepModel();
    private final TaskModel taskModel = new TaskModel();
    private final TestScriptEditorModel testScriptEditorModel = new TestScriptEditorModel();
    private final TestScriptEventModel testScriptEventModel = new TestScriptEventModel();
    private final RemoteOperationModel remoteOperationModel = new RemoteOperationModel();
    private final NotificationScriptStatusModel notificationScriptStatusModel = new NotificationScriptStatusModel();
    private final TestExecuteStatusModel testExecuteStatusModel = new TestExecuteStatusModel();
    private final TestFailModel testFailModel = new TestFailModel();
    private final TestPassModel testPassModel = new TestPassModel();

    private final UIModel uiModel = new UIModel();
    private final ReportedPointModel reportedPointModel = new ReportedPointModel();
    private final ResolutionModel resolutionModel = new ResolutionModel();
    private final FrameSizeModel frameSizeModel = new FrameSizeModel();

    private final AppModel appModel = new AppModel();
    private final ProjectModel projectModel = new ProjectModel();
    private final LogModel logModel = new LogModel();
    private final OperationModel operationModel = new OperationModel();

    private final DeviceManageModel deviceManageModel = new DeviceManageModel();
    private final DeviceReceiveDataModel deviceReceiveDataModel = new DeviceReceiveDataModel();
    private final DeviceSendDataModel deviceSendDataModel = new DeviceSendDataModel();
    private final DeviceTreeModel deviceTreeModel = new DeviceTreeModel();
    private final DeviceItemMenuModel itemMenuModel = new DeviceItemMenuModel();
    private final OperationGroupModel operationGroupModel = new OperationGroupModel();
    private final ClipBoardModel clipBoardModel = new ClipBoardModel();
    private final TestBoxModel testBoxModel = new TestBoxModel();
    private final kikusuiModel kikusuiModel = new kikusuiModel();

    private final CanModel canModel = new CanModel();


    public String getAppDebugInfo() {
        return String.format("%s %s %s", getAppInfo().getAppVersion(), NetworkUtils.getComputerInfo(), DateUtils.getNow());
    }

}
