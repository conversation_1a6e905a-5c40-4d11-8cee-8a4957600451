package ui.model.testScript;

import common.exceptions.OperationException;
import sdk.base.execution.ExecutionResultReport;
import sdk.base.execution.ExecutionSuite;
import sdk.base.operation.OperationGroup;
import sdk.domain.Device;
import sdk.domain.TestScriptFile;
import ui.base.BaseModel;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestResultListener;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestScriptsRunConfig;
import ui.layout.top.menubar.BarStatusListener;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class TestScriptEventModel implements BaseModel, ModelObservable, TestScriptEventObserver {

    private final List<ModelObserver> modelObservers = new ArrayList<>();
    private final List<BarStatusListener> listeners = new ArrayList<>();

    public void addBarStatusListener(BarStatusListener listener) {
        listeners.add(listener);
    }

    public void notifyBarStatusChange(boolean isRunning) {
        for (BarStatusListener listener : listeners) {
            listener.iconStatusChange(isRunning);
        }
    }

    public void notifyPauseStatus(boolean isResume) {
        for (BarStatusListener listener : listeners) {
            listener.pauseStatusChange(isResume);
        }
    }

    public void changeSumTestCycle(int sumTestCycle) {
        for (BarStatusListener listener : listeners) {
            listener.changeSumTestCycle(sumTestCycle);
        }
    }

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void executeOperationGroupStarted(OperationGroup operationGroup) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).executeOperationGroupStarted(operationGroup);
        }
    }

    @Override
    public void executeOperationGroupStopped() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).executeOperationGroupStopped();
        }
    }

    @Override
    public void executeOperationGroupFinished() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).executeOperationGroupFinished();
        }
    }

    @Override
    public void newScript(TestScriptFile testScriptFile) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).newScript(testScriptFile);
        }
    }

    @Override
    public void deleteScript(String testScriptName) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).deleteScript(testScriptName);
        }
    }

    @Override
    public void associateScript() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).associateScript();
        }
    }

    @Override
    public void switchScript(int scriptRow) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).switchScript(scriptRow);
        }
    }

    @Override
    public void selectScript() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).selectScript();
        }
    }

    @Override
    public void switchScript(TestScriptFile testScriptFile) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).switchScript(testScriptFile);
        }
    }

    @Override
    public void executeTestSuite(ExecutionSuite executionSuite, TestResultListener testResultListener) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).executeTestSuite(executionSuite, testResultListener);
        }
    }

    @Override
    public void prepareTestSuite() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).prepareTestSuite();
        }
    }

    @Override
    public void finishAllScripts() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).finishAllScripts();
        }
    }

    @Override
    public void runScript(TestScriptsRunConfig testScriptsRunConfig) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).runScript(testScriptsRunConfig);
        }
    }

    @Override
    public void pauseScript() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).pauseScript();
        }
    }

    @Override
    public void stopScript() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).stopScript();
        }
    }

    @Override
    public void clearScript() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).clearScript();
        }
    }

    @Override
    public void changeTestCycle(int testCycle) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).changeTestCycle(testCycle);
        }
    }

    @Override
    public void refreshTestCycle(int testCycle) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).refreshTestCycle(testCycle);
        }
    }

    @Override
    public void updateResult(TestScriptFile testScriptFile, ExecutionResultReport executionResultReport) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).updateResult(testScriptFile, executionResultReport);
        }
    }

    @Override
    public void changeSelectStatus(List<Integer> selectList) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).changeSelectStatus(selectList);
        }
    }

    @Override
    public void adjustRowIndentation() {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).adjustRowIndentation();
        }
    }

    @Override
    public void updateDeviceUserName(Device device) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).updateDeviceUserName(device);
        }
    }

    @Override
    public void updateCameraParameters(String frameRate, String exposureTime) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).updateCameraParameters(frameRate, exposureTime);
        }
    }

    @Override
    public void addScript(TestScriptFile testScriptFile) throws OperationException {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEventObserver) observer).addScript(testScriptFile);
        }
    }
}
