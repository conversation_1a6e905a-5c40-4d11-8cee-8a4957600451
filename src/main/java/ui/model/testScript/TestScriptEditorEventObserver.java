package ui.model.testScript;

import sdk.base.operation.Operation;
import sdk.domain.complex.PercentTemplateRoi;
import ui.base.picture.Detection;
import ui.model.ModelObserver;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-19 17:10
 * @description :
 * @modified By :
 * @since : 2022-4-19
 */
public interface TestScriptEditorEventObserver extends ModelObserver {

    void operationClicked(Operation operation);

    /**
     * roiRect
     */
    default void roiRect(PercentTemplateRoi roiRect) {

    }

    /**
     * fileName
     */
    default void fileInfo(String fileName) {

    }

    default void detectRoiRect(List<Detection> detections) {

    }

}
