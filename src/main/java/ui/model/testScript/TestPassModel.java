package ui.model.testScript;

import sdk.base.execution.FailLog;
import sdk.base.execution.PassLog;
import sdk.base.operation.OperationResult;
import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/11/4 16:07
 */
public class TestPassModel implements BaseModel, ModelObservable, TestPassObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void savePassLog(PassLog passLog) {
        for (ModelObserver observer : modelObservers) {
            ((TestPassObserver) observer).savePassLog(passLog);
        }
    }

    @Override
    public OperationResult takePhoto(String caseName) {
        OperationResult operationResult = new OperationResult();
        for (ModelObserver observer : modelObservers) {
            operationResult = ((TestPassObserver) observer).takePhoto(caseName);
        }
        return operationResult;
    }

}
