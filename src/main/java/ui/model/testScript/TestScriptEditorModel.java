package ui.model.testScript;

import sdk.base.operation.Operation;
import sdk.domain.complex.PercentTemplateRoi;
import ui.base.BaseModel;
import ui.base.picture.Detection;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-19 16:44
 * @description :
 * @modified By :
 * @since : 2022-4-19
 */
public class TestScriptEditorModel implements BaseModel, ModelObservable, TestScriptEditorEventObserver {

    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    public void operationClicked(Operation operation) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEditorEventObserver) observer).operationClicked(operation);
        }
    }

    @Override
    public void roiRect(PercentTemplateRoi roiRect) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEditorEventObserver) observer).roiRect(roiRect);
        }
    }

    @Override
    public void fileInfo(String fileName) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEditorEventObserver) observer).fileInfo(fileName);
        }
    }

    public void detectRoiRect(List<Detection> detections) {
        for (ModelObserver observer : modelObservers) {
            ((TestScriptEditorEventObserver) observer).detectRoiRect(detections);
        }
    }
}
