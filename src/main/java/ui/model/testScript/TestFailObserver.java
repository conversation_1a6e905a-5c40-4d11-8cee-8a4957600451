package ui.model.testScript;

import sdk.base.execution.FailLog;
import sdk.base.operation.OperationResult;
import ui.model.ModelObserver;

public interface TestFailObserver extends ModelObserver {
    default void saveFailLog(FailLog failLog) {
    }

    default OperationResult failVideo() {
        return new OperationResult().ok();
    }

    default String getFailVideoPath() {
        return "";
    }

    default OperationResult startBacktrackVideo() {
        return new OperationResult().ok();
    }

    default OperationResult stopBacktrackVideo() {
        return new OperationResult().ok();
    }

    default void showProgress(String message) {
    }

    default void hideProgress() {
    }
}
