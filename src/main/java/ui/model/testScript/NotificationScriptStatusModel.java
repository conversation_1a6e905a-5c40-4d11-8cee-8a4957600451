package ui.model.testScript;

import sdk.base.execution.RemoteOperation;
import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class NotificationScriptStatusModel implements BaseModel, ModelObservable, NotificationScriptStatusObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();
    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void notification(RemoteOperation operation) {
        for (ModelObserver observer : modelObservers) {
            ((NotificationScriptStatusObserver)observer).notification(operation);
        }
    }
}
