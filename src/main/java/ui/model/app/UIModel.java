package ui.model.app;

import ui.base.component.TabbedPane.MultifunctionalTabbedPane;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class UIModel implements ModelObservable, UIObserver {

    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void dragStart(MultifunctionalTabbedPane tabbedPane) {
        for (ModelObserver modelObserver : modelObservers) {
            ((UIObserver) modelObserver).dragStart(tabbedPane);
        }
    }

    @Override
    public void dragEnd() {
        for (ModelObserver modelObserver : modelObservers) {
            ((UIObserver) modelObserver).dragEnd();
        }
    }
}
