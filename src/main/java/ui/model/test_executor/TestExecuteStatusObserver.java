package ui.model.test_executor;

import sdk.base.execution.ExecutionNotification;
import sdk.base.operation.OperationResult;
import ui.model.ModelObserver;

public interface TestExecuteStatusObserver extends ModelObserver {

    default void testStarted(String testSuiteName) {

    }

    default void singleCaseCompleted(ExecutionNotification executionNotification) {

    }

    default void testPausing(OperationResult operationResult) {

    }

    default void testResumed() {

    }

    default void testStopped() {

    }

    default void testCompleted() {

    }
}
