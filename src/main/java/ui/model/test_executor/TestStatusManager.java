package ui.model.test_executor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TestStatusManager {
    private static TestStatusManager instance;
    @Getter
    private boolean pausing;
    @Getter
    private boolean testing;
    private final List<TestStatusObserver> observers = new ArrayList<>();

    private TestStatusManager() {
    }

    public static synchronized TestStatusManager getInstance() {
        if (instance == null) {
            instance = new TestStatusManager();
        }
        return instance;
    }

    public void setTesting(boolean testing) {
        boolean oldStatus = this.testing;
        this.testing = testing;
        if (oldStatus != testing) {
            notifyTestingStatusChanged();
        }
    }

    public void setPausing(boolean pausing) {
        boolean oldStatus = this.pausing;
        this.pausing = pausing;
        if (oldStatus != pausing) {
            notifyPausingStatusChanged();
        }
    }

    public void addObserver(TestStatusObserver observer) {
        observers.add(observer);
    }

    public void removeObserver(TestStatusObserver observer) {
        observers.remove(observer);
    }

    private void notifyTestingStatusChanged() {
        for (TestStatusObserver observer : observers) {
            observer.onTestingStatusChanged(testing);
        }
    }

    private void notifyPausingStatusChanged() {
        for (TestStatusObserver observer : observers) {
            observer.onPausingStatusChanged(pausing);
        }
    }

}