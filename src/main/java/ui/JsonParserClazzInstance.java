package ui;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import ui.base.OperationAssembler;

@Data
public class JsonParserClazzInstance {

    private Class<? extends OperationAssembler<?>> assemblerClazz;
    private Class<?> operationObjectClass;
    private OperationAssembler<JSONObject> instance;
    private Object defaultValue;

    public JsonParserClazzInstance(Class<? extends OperationAssembler<?>> assemblerClazz,
                                   Class<?> operationObjectClass) {
        this.assemblerClazz = assemblerClazz;
        this.operationObjectClass = operationObjectClass;
    }

    public JsonParserClazzInstance(Class<? extends OperationAssembler<?>> assemblerClazz,
                                   Class<?> operationObjectClass,
                                   Object defaultValue) {
        this(assemblerClazz, operationObjectClass);
        this.defaultValue = defaultValue;
    }

}