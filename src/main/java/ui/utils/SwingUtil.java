package ui.utils;

import cn.hutool.core.util.ArrayUtil;
import common.constant.ResourceConstant;
import excelcase.tree.SelectedExcelSheetTree;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import ui.base.Filters;
import ui.base.LightweightImageIcon;
import ui.base.dialogs.HexDialog;

import javax.imageio.ImageIO;
import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import javax.swing.filechooser.FileFilter;
import javax.swing.text.AbstractDocument;
import javax.swing.text.DocumentFilter;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemListener;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.prefs.Preferences;

import static common.constant.ResourceConstant.CommonLayout.newToggleOffIconPath;
import static common.constant.ResourceConstant.CommonLayout.newToggleOnIconPath;
import static ui.base.cosntants.TestBoxConstants.BORDER_COLOR;

@Slf4j
public class SwingUtil {

    private static final String LAST_DIRECTORY = "lastDirectory";

    // 检查 JComboBox 上是否已经添加了指定的 ItemListener
    public static boolean hasItemListener(JComboBox<?> comboBox, ItemListener targetListener) {
        // 获取 comboBox 上的所有项目侦听器
        ItemListener[] itemListeners = comboBox.getItemListeners();

        // 遍历所有项目侦听器
        for (ItemListener listener : itemListeners) {
            // 检查是否已经添加了 targetListener
            if (listener.equals(targetListener)) {
                return true;
            }
        }
        return false;
    }

    public static int getModelIndex(JTable table, String columnName) {
        return table.getColumn(columnName).getModelIndex();
    }

    public static int getColumnIndex(JTable table, String columnName) {
        try {
            return table.getColumnModel().getColumnIndex(columnName);
        } catch (IllegalArgumentException e) {
            return -1;
        }
    }

    public static String showHexInputDialog(Component parentComponent) {
        return HexDialog.showHexInputDialog(parentComponent);
    }

    public static JButton addNewScriptButton(String s) {
        return new JButton(s + "(➡)");
    }

    public interface SwingInvoker {
        void invoke();
    }

    public static void invokeLater(SwingInvoker swingInvoker) {
        if (SwingUtilities.isEventDispatchThread()) {
            swingInvoker.invoke();
        } else {
            //非EDT
            SwingUtilities.invokeLater(swingInvoker::invoke);
        }
    }

    public static String convertToVertical(String text) {
        StringBuilder builder = new StringBuilder("<html><div style='text-align: center;'>");
        for (char c : text.toCharArray()) {
            builder.append(c).append("<br/>");
        }
        builder.append("</div></html>");
        return builder.toString();
    }

    public static Color alphaColor(Color color, float alpha) {
        return new Color(
                color.getRed(),
                color.getGreen(),
                color.getBlue(),
                (int) (alpha * 255)
        );
    }

    public static void showInformationDialog(Container parentComponent, Object message) {
        showInformationDialog(parentComponent, message, "信息");
    }

    public static void showInformationDialog(Container parentComponent, Object message, String title) {
        showMessageDialogWithHTML(parentComponent, message, title, null, JOptionPane.INFORMATION_MESSAGE);
    }

    public static void showWebMessageDialog(Container parentComponent, Object message) {
        showWebMessageDialog(parentComponent, message, "提醒", null, JOptionPane.WARNING_MESSAGE);
    }

    public static void showWarningDialog(Object message) {
        showWarningDialog(null, message);
    }

    public static void showWarningDialog(Container parentComponent, Object message) {
        showWarningDialog(parentComponent, message, "提醒");
    }

    public static void showWarningDialog(Container parentComponent, Object message, String title) {
        showWarningDialog(parentComponent, message, title, null);
    }

    public static void showWarningDialog(Container parentComponent, Object message, String title, String prefix) {
        showMessageDialogWithHTML(parentComponent, message, title, prefix, JOptionPane.WARNING_MESSAGE);
    }

    public static void showWebMessageDialog(Container parentComponent, Object message, String title, int messageType) {
        showWebMessageDialog(parentComponent, message, title, null, messageType);
    }

    public static void showWebMessageDialog(Container parentComponent, Object message, String title, String prefix, int messageType) {
//        String htmlMessage = "<html>" + String.valueOf(message).replaceAll("\\t|\\n|\\r\\n", "<br/>") + "</html>";
        String htmlMessage = String.valueOf(message);
        String msg = prefix == null ? htmlMessage : prefix + "\n" + htmlMessage;
        showCustomMessageDialog(null, msg, title == null ? "" : title, messageType);
    }

    public static void showCustomMessageDialog(Component parentComponent, Object message, String title, int messageType) {
        JDialog dialog = new JDialog((parentComponent instanceof Frame) ? (Frame) parentComponent : null, title, true);
        String msg = String.valueOf(message);

        // 创建并配置JTextPane和JScrollPane
        JTextPane pane = new JTextPane();
        pane.setContentType("text/html");
        pane.setEditable(false);
        pane.setText(msg);
        pane.setCaretPosition(0);
        JScrollPane scrollPane = new JScrollPane(pane);

        // 设置对话框的内容面板
        JPanel contentPane = new JPanel(new BorderLayout());
        contentPane.add(scrollPane, BorderLayout.CENTER);

        // 设置对话框的最大尺寸
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        int ratio = msg.length() > 200 ? 2 : 4;
        Dimension maxDialogSize = new Dimension(screenSize.width / ratio, screenSize.height / ratio);
        dialog.setSize(maxDialogSize);
        dialog.setPreferredSize(maxDialogSize);
        dialog.setMaximumSize(maxDialogSize);

        // 设置默认的按钮和操作
        // Handle other types if necessary
        JButton okButton = new JButton("确定");
        okButton.addActionListener(e -> dialog.dispose());
        contentPane.add(okButton, BorderLayout.SOUTH);
        // 可以根据需要添加更多类型的按钮处理
        dialog.getRootPane().setDefaultButton(okButton);
        dialog.setContentPane(contentPane);
        dialog.pack(); // 这里调用pack()是为了让对话框适应其内容，同时不超过最大尺寸
        dialog.setLocationRelativeTo(parentComponent); // 居中显示
        SwingUtilities.invokeLater(okButton::requestFocus);
        dialog.setVisible(true);
    }

    public static String replaceWithHtml(String text) {
        return "<html>" + text.replaceAll("<", "&lt;")
                .replaceAll(">", "&gt;")
                .replaceAll("\\n|\\r\\n", "<br/>") + "</html>";
    }

    public static void showMessageDialogWithHTML(Container parentComponent, Object message, String title, String prefix, int messageType) {
        message = prefix == null ? message : prefix + "\n" + message;
        String htmlMessage = replaceWithHtml(String.valueOf(message));
        showWebMessageDialog(parentComponent,
                htmlMessage,
                title == null ? "" : title, messageType);
    }

    public static void setPanelEnabled(java.awt.Container cont, Boolean isEnabled) {
        cont.setEnabled(isEnabled);
        java.awt.Component[] components = cont.getComponents();

        for (Component component : components) {
            if (component instanceof Container) {
                setPanelEnabled((Container) component, isEnabled);
            }
            component.setEnabled(isEnabled);
        }

    }

    public static JButton getDebugButton(String actionDesc) {
        JButton button = new JButton(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.debugIconPath, 8.0f));
        button.setBorderPainted(false); // 去除边框
        button.setBorderPainted(false);
        button.setDoubleBuffered(true);
        button.setContentAreaFilled(false);
        Insets insets = new Insets(0, 0, 0, 0);
        button.setMargin(insets);
        if (actionDesc != null) {
            button.setText(String.format("[%s]", actionDesc));
        }
        return button;
    }

    public static JButton getDebugButton() {
        return getDebugButton(null);
    }

    public static JButton getAddToScriptButton(String actionDesc) {
        JButton button = new JButton(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.addToScriptIconPath, 8.0f));
        button.setBorderPainted(false);
        button.setDoubleBuffered(true);
        button.setContentAreaFilled(false);
        Insets insets = new Insets(0, 0, 0, 0);
        button.setMargin(insets);
        if (actionDesc != null) {
            button.setText(String.format("[%s]", actionDesc));
        }
        return button;
    }

    public static JButton getAddToScriptButton() {
        return getAddToScriptButton(null);
    }


    public static JButton createButton(String iconPath, float iconSize, String actionDesc) {
        JButton button = new JButton(SwingUtil.getResourceAsImageIcon(iconPath, iconSize));
        button.setBorderPainted(false);
        button.setDoubleBuffered(true);
        button.setContentAreaFilled(false);
        if (actionDesc != null) {
            button.setText(String.format("[%s]", actionDesc));
        }
        return button;
    }

    public static JButton fileButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.fileIconPath, 11.0f, actionDesc);
    }

    public static JButton fileButton() {
        return fileButton(null);
    }

    public static JButton messageButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.messageIconPath, 12.0f, actionDesc);
    }

    public static JButton messageButton() {
        return messageButton(null);
    }

    public static JButton copyButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.copyIconPath, 8.0f, actionDesc);
    }

    public static JButton copyButton() {
        return copyButton(null);
    }

    public static JButton detectButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.detectIconPath, 10.0f, actionDesc);
    }

    public static JButton detectButton() {
        return detectButton(null);
    }

    public static JButton setButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.setIconPath, 11.0f, actionDesc);
    }

    public static JButton setButton() {
        return setButton(null);
    }

    public static JButton triangleButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.triangleIconPath, 11.0f, actionDesc);
    }

    public static JButton triangleButton() {
        return triangleButton(null);
    }

    public static JButton succeedButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.succeedIconPath, 11.0f, actionDesc);
    }

    public static JButton succeedButton() {
        return succeedButton(null);
    }

    public static JButton warningButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.warningIconPath, 11.0f, actionDesc);
    }

    public static JButton warningButton() {
        return warningButton(null);
    }

    public static JButton wrongButton(String actionDesc) {
        return createButton(ResourceConstant.LeftLayout.wrongIconPath, 11.0f, actionDesc);
    }

    public static JButton wrongButton() {
        return wrongButton(null);
    }

    public static JSlider fpsJSlider(int min, int max, int value) {
        JSlider slider = new JSlider(min, max, value);
        Hashtable<Integer, JLabel> labelTable = new Hashtable<>();
        labelTable.put(30, new JLabel("30"));
        labelTable.put(60, new JLabel("60"));
        labelTable.put(120, new JLabel("120"));

        slider.setLabelTable(labelTable);
        slider.setPaintLabels(true);
        slider.setPaintTicks(true);
        slider.setMajorTickSpacing(30);
        return slider;
    }
    public static JSlider createLabeledSlider(int min, int max, int value) {
        JSlider slider = new JSlider(min, max, value);

        Hashtable<Integer, JLabel> labelTable = new Hashtable<>();
        labelTable.put(min, new JLabel("小"));
        labelTable.put((min + max) / 2, new JLabel("中"));
        labelTable.put(max, new JLabel("大"));

        slider.setLabelTable(labelTable);
        slider.setPaintLabels(true);
        slider.setPaintTicks(true);
        slider.setMajorTickSpacing((max - min) / 2);

        return slider;
    }

    public static void setWidth(JComponent component, int width) {
        component.setPreferredSize(new Dimension(width, component.getPreferredSize().height));
    }

    public static void setHeight(JComponent component, int height) {
        component.setPreferredSize(new Dimension(component.getPreferredSize().width, height));
    }

    public static GridBagConstraints getGridBagConstraints(int gridx, int gridy, int weightx, int weighty) {
        return getGridBagConstraints(gridx, gridy, weightx, weighty, true);
    }

    public static GridBagConstraints getGridBagConstraints(int gridx, int gridy, int weightx) {
        return getGridBagConstraints(gridx, gridy, weightx, 0, true);
    }

    public static GridBagConstraints getGridBagConstraints(int gridx, int gridy, int weightx, int weighty, boolean isFill) {
        int fill = isFill ? GridBagConstraints.HORIZONTAL : GridBagConstraints.NONE;
        return getGridBagConstraints(gridx, gridy, weightx, weighty, fill);
    }

    public static GridBagConstraints getGridBagConstraints(int gridx, int gridy, int weightx, int weighty, int fill) {
        GridBagConstraints gridBagConstraints = new GridBagConstraints();
        gridBagConstraints.gridx = gridx;
        gridBagConstraints.gridy = gridy;
        gridBagConstraints.weightx = weightx;
        gridBagConstraints.weighty = weighty;
        gridBagConstraints.fill = fill;
        gridBagConstraints.insets = new Insets(0, 0, 6, 0);
        return gridBagConstraints;
    }

    public static GridBagConstraints getRowGridBagConstraints(int gridWidth, double weightX, double weightY) {
        GridBagConstraints gridBagConstraints = new GridBagConstraints();
        gridBagConstraints.gridwidth = gridWidth; //该方法是设置组件水平所占用的格子数，如果为0，就说明该组件是该行的最后一个
        gridBagConstraints.weightx = weightX; //该方法设置组件水平的拉伸幅度，如果为0就说明不拉伸，不为0就随着窗口增大进行拉伸，0到1之间
        gridBagConstraints.weighty = weightY; //该方法设置组件垂直的拉伸幅度，如果为0就说明不拉伸，不为0就随着窗口增大进行拉伸，0到1之间
        gridBagConstraints.fill = GridBagConstraints.BOTH;
        gridBagConstraints.insets = new Insets(0, 0, 6, 0);
        return gridBagConstraints;
    }

    public static void addRowLayoutUnchanged(JPanel panel, String labelName, JComponent component, int row) {
        addRowLayout(panel, labelName, component, row, GridBagConstraints.NONE, 100);
    }

    public static void addRowLayoutHorizontal(JPanel panel, String labelName, JComponent component, int row, int weightX) {
        addRowLayout(panel, labelName, component, row, GridBagConstraints.HORIZONTAL, weightX);
    }

    private static void addRowLayout(JPanel panel, String labelName, JComponent component, int row, int fill, int weightX) {
        JLabel label = new JLabel(labelName);
        GridBagConstraints labelGridBagConstraints = new GridBagConstraints();
        labelGridBagConstraints.gridx = 0;
        labelGridBagConstraints.gridy = row;
        labelGridBagConstraints.weightx = 3;
        labelGridBagConstraints.insets = new Insets(0, 5, 0, 5);
        labelGridBagConstraints.fill = fill;
        panel.add(label, labelGridBagConstraints);

        GridBagConstraints componentGridBagConstraints = new GridBagConstraints();
        componentGridBagConstraints.gridx = 1;
        componentGridBagConstraints.gridy = row;
        componentGridBagConstraints.weightx = weightX - labelGridBagConstraints.weightx;
        componentGridBagConstraints.fill = GridBagConstraints.HORIZONTAL;
        panel.add(component, componentGridBagConstraints);
    }

    public static InputStream getFileInputStream(String filename) {
        return Thread.currentThread().getContextClassLoader().getResourceAsStream(filename);
    }

    public static ImageIcon getResourceAsImageIcon(String filename) {
        try (InputStream inputStream = getFileInputStream(filename)) { // 使用 try-with-resources
            if (inputStream != null) {
                try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
                    byte[] data = new byte[4096]; // 增大缓冲区减少循环次数
                    int nRead;
                    while ((nRead = inputStream.read(data)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    return new ImageIcon(buffer.toByteArray());
                }
            }
        } catch (IOException e) {
            log.error("加载图片资源失败: {}", filename, e);
        }
        return new ImageIcon(); // 返回空图标代替异常
    }


    //传入filename转换为BufferedImage
    public static BufferedImage getResourceAsBufferedImage(String filename) {
        InputStream inputStream = getFileInputStream(filename);
        if (inputStream != null) {
            try {
                return ImageIO.read(inputStream);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    public static ImageIcon getResourceAsImageIcon(String filename, float ratio) {
        ImageIcon icon = getResourceAsImageIcon(filename);
        icon.setImage(icon.getImage().getScaledInstance((int) (icon.getIconWidth() / ratio), (int) (icon.getIconHeight() / ratio), Image.SCALE_DEFAULT));
        return icon;
    }

    public static Image setImageIconSize(ImageIcon icon, float ratio) {
        return icon.getImage().getScaledInstance((int) (icon.getIconWidth() / ratio), (int) (icon.getIconHeight() / ratio), Image.SCALE_DEFAULT);
    }


    public static JLabel makeImageLabel(String filename, float ratio) {
        ImageIcon icon = getResourceAsImageIcon(filename);
        icon.setImage(setImageIconSize(icon, ratio));
        return new JLabel(icon);

    }


    public static JButton makeImageButton(String filename, float ratio) {
        ImageIcon icon = getResourceAsImageIcon(filename);
        icon.setImage(icon.getImage().getScaledInstance((int) (icon.getIconWidth() / ratio), (int) (icon.getIconHeight() / ratio), Image.SCALE_DEFAULT));
        return new JButton(icon);
    }


    private static final AffineTransform atf = new AffineTransform();

    private static final FontRenderContext frc = new FontRenderContext(atf, true,
            true);

    public static int getStringHeight(String str, Font font) {
        if (str == null || str.isEmpty() || font == null) {
            return 0;
        }
        return (int) font.getStringBounds(str, frc).getWidth();

    }

    public static int getStringWidth(String str, Font font) {
        if (str == null || str.isEmpty() || font == null) {
            return 0;
        }
        return (int) font.getStringBounds(str, frc).getWidth();
    }

    public static void setPreferredWidth(Component component, int width) {
        component.setPreferredSize(new Dimension(width, (int) component.getPreferredSize().getHeight()));
    }

    public static void setFontSize(JComponent component, int size) {
        component.setFont(new Font(component.getFont().getName(), component.getFont().getStyle(), size));
    }

    /**
     * 将形如“#FFFFFF”的颜色转换成Color
     *
     * @param hex 十六进制字符串
     * @return 颜色
     */
    public static Color getColorFromHex(String hex) {
        if (hex == null || hex.length() != 7) {
            try {
                throw new Exception("不能转换这种类型的颜色");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return null;
            }
        }
        int r = Integer.valueOf(hex.substring(1, 3), 16);
        int g = Integer.valueOf(hex.substring(3, 5), 16);
        int b = Integer.valueOf(hex.substring(5), 16);
        return new Color(r, g, b);
    }

    public static Rectangle getAutoAdjustRectangle2(Image image, Container container) {
        Dimension cmpSize = container.getSize();

        // 计算图像宽高比例
        double ratio = 1.0 * image.getWidth(container) / image.getHeight(container);

        // 计算等比例缩放后的区域大小
        int width, height;
        if (ratio * cmpSize.height <= cmpSize.width) {
            // 以高度为基准
            height = cmpSize.height;
            width = (int) (height * ratio);
        } else {
            // 以宽度为基准
            width = cmpSize.width;
            height = (int) (width / ratio);
        }

        // 居中计算
        int x = (cmpSize.width - width) / 2;
        int y = (cmpSize.height - height) / 2;

        return new Rectangle(x, y, width, height);
    }

    public static Rectangle getAutoAdjustRectangle(Image image, Container container) {
        Dimension cmpSize = container.getSize();//获取组件大小
        //计算图像宽高比例
        double ratio = 1.0 * image.getWidth(container) / image.getHeight(container);
        //计算等比例缩放后的区域大小
        int width = (int) Math.min(cmpSize.width, ratio * cmpSize.height);
        int height = (int) (width / ratio);

        int x = (int) (container.getAlignmentX() * (cmpSize.width - width));
        int y = (int) (container.getAlignmentY() * (cmpSize.height - height));

        return new Rectangle(x, y, width, height);
    }


    public static ImageIcon createAutoAdjustIcon(Image image) {
        return createAutoAdjustIcon(image, true);
    }

    public static ImageIcon createAutoAdjustIcon(Image image, boolean constrained) {
        return LightweightImageIcon.createAutoAdjustIcon(image, constrained);
//        return new ImageIcon(image) {
//            @Override
//            public synchronized void paintIcon(Component c, Graphics g, int x, int y) {
//                //初始化参数
//                Point startPoint = new Point(0, 0); //默认绘制起点
//                Dimension cmpSize = c.getSize();//获取组件大小
//                Dimension imgSize = new Dimension(getIconWidth(), getIconHeight());//获取图像大小
//
//                //计算绘制起点和区域
//                if (constrained) { //等比例缩放
//                    //计算图像宽高比例
//                    double ratio = 1.0 * imgSize.width / imgSize.height;
//                    //计算等比例缩放后的区域大小
//                    imgSize.width = (int) Math.min(cmpSize.width, ratio * cmpSize.height);
//                    imgSize.height = (int) (imgSize.width / ratio);
//                    //计算绘制起点
//                    startPoint.x = (int) (c.getAlignmentX() * (cmpSize.width - imgSize.width));
//                    startPoint.y = (int) (c.getAlignmentY() * (cmpSize.height - imgSize.height));
//                } else {
//                    imgSize = cmpSize;
//                }
//                //根据起点和区域大小进行绘制
//                if (getImageObserver() == null) {
//                    g.drawImage(getImage(), startPoint.x, startPoint.y, imgSize.width, imgSize.height, c);
//                } else {
//                    g.drawImage(getImage(), startPoint.x, startPoint.y, imgSize.width, imgSize.height, getImageObserver());
//                }
//            }
//        };
    }

    public static ImageIcon createAutoAdjustIcon(String filename, boolean constrained) {
        return createAutoAdjustIcon(new ImageIcon(filename).getImage(), constrained);
    }

    public static ImageIcon createAutoAdjustIcon(URL url, boolean constrained) {
        return createAutoAdjustIcon(new ImageIcon(url).getImage(), constrained);
    }

    public static void centerInScreen(Component component) {
        centerInScreen(component, true);
    }

    public static void centerInScreen(Component component, boolean layout) {
        // 得到显示器屏幕的宽高
        int width = Toolkit.getDefaultToolkit().getScreenSize().width;
        int height = Toolkit.getDefaultToolkit().getScreenSize().height;
        int windowWidth = layout ? component.getPreferredSize().width : component.getSize().width;
        int widowsHeight = layout ? component.getPreferredSize().height : component.getSize().height;
        component.setBounds((width - windowWidth) / 2,
                (height - widowsHeight) / 2, windowWidth, widowsHeight);
    }

    public static void windowMaximized(Component component) {
        if (component instanceof JFrame) {
            JFrame window = (JFrame) component;
            window.setExtendedState(JFrame.MAXIMIZED_BOTH);
        }
    }


    public static File getFileChooser(Component component, boolean isDirOnly) {
        return getFileChooser(component, null, isDirOnly);
    }

    public static File getFileChooser(Component component, FileNameExtensionFilter filter, boolean isDirOnly) {
        return getFileChooser(component, null, filter, isDirOnly);
    }

    public static File getFileChooser(Component component, String title, FileNameExtensionFilter filter, boolean isDirOnly) {
        File[] files = getFilesChooser(component, title, filter, false, null, isDirOnly);
        if (ArrayUtil.isEmpty(files)) {
            return null;
        }
        return files[0];
    }

    public static File getFileChooser(Component component, String title, FileNameExtensionFilter filter, File currentDirectory, boolean isDirOnly) {
        File[] files = getFilesChooser(component, title, filter, false, currentDirectory, isDirOnly);
        if (ArrayUtil.isEmpty(files)) {
            return null;
        }
        return files[0];
    }

    public static File[] getFilesChooser(Component component, String title, FileNameExtensionFilter filter, File currentDirectory, boolean isDirOnly) {
        return getFilesChooser(component, title, filter, true, currentDirectory, isDirOnly);
    }

    // 在用户确认选择之后保存当前目录
    public static void saveCurrentDirectory(JFileChooser chooser) {
        Preferences prefs = Preferences.userNodeForPackage(SwingUtil.class);
        String path = chooser.getCurrentDirectory().getPath();
        prefs.put(LAST_DIRECTORY, path);
    }

    private static class FileSearchPanel extends JPanel {
        private final JTextField searchField;
        private final JFileChooser fileChooser;
        private String lastSearch = "";

        public FileSearchPanel(JFileChooser chooser) {
            super(new BorderLayout());
            this.fileChooser = chooser;

            // 创建搜索框
            searchField = new JTextField();
            searchField.setToolTipText("输入文件名搜索");

            // 添加搜索实时监听
            searchField.getDocument().addDocumentListener(new DocumentListener() {
                public void changedUpdate(DocumentEvent e) {
                    search();
                }

                public void removeUpdate(DocumentEvent e) {
                    search();
                }

                public void insertUpdate(DocumentEvent e) {
                    search();
                }
            });

            // 创建左侧标签面板
            JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
            leftPanel.add(new JLabel("搜索: "));

            // 创建右侧清除按钮面板
            JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
            JButton clearButton = new JButton("×");
            clearButton.setBorderPainted(false);
            clearButton.setContentAreaFilled(false);
            clearButton.addActionListener(e -> {
                searchField.setText("");
                fileChooser.rescanCurrentDirectory();
            });
            rightPanel.add(clearButton);

            // 布局组件
            add(leftPanel, BorderLayout.WEST);
            add(searchField, BorderLayout.CENTER);
            add(rightPanel, BorderLayout.EAST);

            setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        }

        private void search() {
            String searchText = searchField.getText().toLowerCase();
            if (searchText.equals(lastSearch)) {
                return;
            }
            lastSearch = searchText;

            // 设置文件过滤器
            fileChooser.setFileFilter(new FileFilter() {
                @Override
                public boolean accept(File f) {
                    if (searchText.isEmpty()) return true;
                    if (f.isDirectory()) {
                        // 对目录进行搜索
                        // 1. 检查当前目录名是否匹配
                        if (f.getName().toLowerCase().contains(searchText)) {
                            return true;
                        }
                        // 2. 检查是否为父目录，如果是则显示
                        File currentDir = fileChooser.getCurrentDirectory();
                        if (currentDir != null && f.equals(currentDir.getParentFile())) {
                            return true;
                        }
                        return false;
                    }
                    return f.getName().toLowerCase().contains(searchText);
                }

                @Override
                public String getDescription() {
                    return "搜索结果";
                }
            });

            fileChooser.rescanCurrentDirectory();
        }
    }

    public static File[] getFilesChooser(Component component,
                                         String title,
                                         FileNameExtensionFilter filter,
                                         boolean multiSelectionEnabled,
                                         File currentDirectory,
                                         boolean isDirOnly) {
        // 创建文件选择器
        JFileChooser chooser = new JFileChooser(currentDirectory);
        Preferences prefs = Preferences.userNodeForPackage(SwingUtil.class);
        String lastDirectoryPath = prefs.get(LAST_DIRECTORY, "");
        File lastDirectory = new File(lastDirectoryPath);
        if (lastDirectory.exists() && lastDirectory.isDirectory()) {
            chooser.setCurrentDirectory(lastDirectory);
        }
        chooser.setMultiSelectionEnabled(multiSelectionEnabled);
        chooser.setPreferredSize(new Dimension(800, 450));
        ActionMap actionMap = chooser.getActionMap();
        actionMap.get("viewTypeDetails").actionPerformed(
                new ActionEvent(chooser, ActionEvent.ACTION_PERFORMED, null)
        );
        chooser.setFileSelectionMode(isDirOnly ? JFileChooser.DIRECTORIES_ONLY : JFileChooser.FILES_ONLY);
        if (filter != null) {
            chooser.setFileFilter(filter);
        }

        // 创建搜索面板并添加到JFileChooser
        FileSearchPanel searchPanel = new FileSearchPanel(chooser);
        
        // 创建包含搜索框的包装面板
        JPanel wrapperPanel = new JPanel(new BorderLayout());
        wrapperPanel.add(searchPanel, BorderLayout.NORTH);
        wrapperPanel.add(chooser, BorderLayout.CENTER);
        
        // 添加路径输入监听器
        SwingUtilities.invokeLater(() -> {
            addPathNavigationListener(chooser);
            adjustFileChooserColumnWidths(chooser);
        });

        // 创建自定义对话框
        JDialog dialog = new JDialog((component instanceof Frame) ? (Frame) component : null, 
                                    title != null ? title : (isDirOnly ? "选择文件夹" : "打开Excel测试用例"), true);
        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        dialog.setContentPane(wrapperPanel);
        dialog.setSize(new Dimension(850, 550));
        dialog.setLocationRelativeTo(component);

        // 监听JFileChooser的动作
        final File[] selectedFiles = {null};
        chooser.addActionListener(e -> {
            if (JFileChooser.APPROVE_SELECTION.equals(e.getActionCommand())) {
                selectedFiles[0] = chooser.getSelectedFile();
                saveCurrentDirectory(chooser);
                dialog.dispose();
            } else if (JFileChooser.CANCEL_SELECTION.equals(e.getActionCommand())) {
                dialog.dispose();
            }
        });

        // 显示对话框
        dialog.setVisible(true);

        // 获取用户选择的结果
        if (selectedFiles[0] != null) {
            if (multiSelectionEnabled) {
                return chooser.getSelectedFiles();
            } else {
                return ArrayUtils.toArray(selectedFiles[0]);
            }
        }

        return null;
    }

    // 添加路径导航监听器，支持在文件名输入框中输入路径来导航
    private static void addPathNavigationListener(JFileChooser chooser) {
        // 递归查找文件名输入框
        JTextField fileNameField = findFileNameTextField(chooser);
        if (fileNameField != null) {
            fileNameField.getDocument().addDocumentListener(new DocumentListener() {
                private String lastText = "";
                
                @Override
                public void insertUpdate(DocumentEvent e) {
                    checkAndNavigate();
                }

                @Override
                public void removeUpdate(DocumentEvent e) {
                    checkAndNavigate();
                }

                @Override
                public void changedUpdate(DocumentEvent e) {
                    checkAndNavigate();
                }

                private void checkAndNavigate() {
                    SwingUtilities.invokeLater(() -> {
                        String currentText = fileNameField.getText().trim();
                        if (!currentText.equals(lastText) && !currentText.isEmpty()) {
                            lastText = currentText;
                            
                            // 检查是否为有效路径
                            File path = new File(currentText);
                            if (path.exists() && path.isDirectory()) {
                                try {
                                    chooser.setCurrentDirectory(path);
                                    // 延迟清空输入框，避免递归调用
                                    SwingUtilities.invokeLater(() -> {
                                        fileNameField.setText("");
                                        lastText = "";
                                    });
                                } catch (Exception ex) {
                                    log.debug("无法导航到路径: " + currentText, ex);
                                }
                            } else {
                                // 如果路径不存在，尝试检查是否是部分路径
                                File parentDir = path.getParentFile();
                                if (parentDir != null && parentDir.exists() && parentDir.isDirectory()) {
                                    // 如果父目录存在，导航到父目录
                                    try {
                                        chooser.setCurrentDirectory(parentDir);
                                        // 设置文件名为子路径
                                        SwingUtilities.invokeLater(() -> {
                                            fileNameField.setText(path.getName());
                                            lastText = path.getName();
                                        });
                                    } catch (Exception ex) {
                                        log.debug("无法导航到父路径: " + parentDir.getAbsolutePath(), ex);
                                    }
                                }
                            }
                        }
                    });
                }
            });
        }
    }

    // 递归查找JFileChooser中的文件名输入框
    private static JTextField findFileNameTextField(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JTextField) {
                JTextField textField = (JTextField) component;
                // 通过多种方式判断是否为文件名输入框
                Container parent = textField.getParent();
                if (parent != null) {
                    String parentClassName = parent.getClass().getName();
                    // 检查是否在文件面板中，或者检查工具提示文本
                    if (parentClassName.contains("FilePane") || 
                        parentClassName.contains("MetalFileChooser") ||
                        (textField.getToolTipText() != null && textField.getToolTipText().contains("文件")) ||
                        textField.getName() != null && textField.getName().contains("File")) {
                        return textField;
                    }
                }
                // 如果没有明确标识，检查位置是否在底部附近
                Point location = textField.getLocationOnScreen();
                Container chooserParent = findJFileChooserParent(textField);
                if (chooserParent != null) {
                    Point chooserLocation = chooserParent.getLocationOnScreen();
                    Dimension chooserSize = chooserParent.getSize();
                    // 如果文本框在JFileChooser的下半部分，很可能是文件名输入框
                    if (location.y > chooserLocation.y + chooserSize.height * 0.7) {
                        return textField;
                    }
                }
            } else if (component instanceof Container) {
                JTextField result = findFileNameTextField((Container) component);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    // 查找JTextField的JFileChooser父容器
    private static Container findJFileChooserParent(Component component) {
        Container parent = component.getParent();
        while (parent != null) {
            if (parent instanceof JFileChooser) {
                return parent;
            }
            parent = parent.getParent();
        }
        return null;
    }

    // 调整JFileChooser的列宽
    private static void adjustFileChooserColumnWidths(JFileChooser chooser) {
        try {
            // 查找JFileChooser中的JTable
            JTable table = findJTable(chooser);
            if (table != null) {
                // 设置自动调整模式
                table.setAutoResizeMode(JTable.AUTO_RESIZE_SUBSEQUENT_COLUMNS);
                
                // 获取列模型
                javax.swing.table.TableColumnModel columnModel = table.getColumnModel();
                if (columnModel.getColumnCount() >= 4) {
                    // 通常JFileChooser有以下列：名称、大小、类型、修改日期
                    // 设置名称列为最宽（40%）
                    columnModel.getColumn(0).setPreferredWidth(300);
                    columnModel.getColumn(0).setMinWidth(200);
                    
                    // 设置大小列宽度（15%）
                    columnModel.getColumn(1).setPreferredWidth(100);
                    columnModel.getColumn(1).setMinWidth(80);
                    
                    // 设置类型列宽度（20%）
                    columnModel.getColumn(2).setPreferredWidth(120);
                    columnModel.getColumn(2).setMinWidth(100);
                    
                    // 设置修改日期列宽度（25%）
                    columnModel.getColumn(3).setPreferredWidth(150);
                    columnModel.getColumn(3).setMinWidth(120);
                    
                    // 如果还有其他列，设置合适的宽度
                    for (int i = 4; i < columnModel.getColumnCount(); i++) {
                        columnModel.getColumn(i).setPreferredWidth(100);
                        columnModel.getColumn(i).setMinWidth(80);
                    }
                }
                
                // 重新布局表格
                table.revalidate();
                table.repaint();
            }
        } catch (Exception e) {
            log.debug("调整列宽失败", e);
        }
    }

    // 递归查找JFileChooser中的JTable
    private static JTable findJTable(Container container) {
        for (Component component : container.getComponents()) {
            if (component instanceof JTable) {
                return (JTable) component;
            } else if (component instanceof Container) {
                JTable result = findJTable((Container) component);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    public interface ComponentBuilder {

        JComponent buildComponent();
    }


    public static <T> Map<String, Boolean> showMultiInputDialog(Component parentComponent, String title, int messageType,
                                                                Map<String, Boolean> selectionValues) throws HeadlessException {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setPreferredSize(new Dimension(150, 400));
        SelectedExcelSheetTree tree = new SelectedExcelSheetTree(selectionValues);
        JScrollPane scrollPane = new JScrollPane();
        scrollPane.setViewportView(tree);
        panel.add(scrollPane, BorderLayout.CENTER);
        JOptionPane.showMessageDialog(parentComponent, panel, title, messageType);
        return tree.getSelectedTreeNode();
    }

    public static <T> List<T> showMultiInputDialog(Component parentComponent, String title, int messageType,
                                                   T[] selectionValues, ComponentBuilder componentBuilder)
            throws HeadlessException {
        JPanel panel = new JPanel(new BorderLayout());
        JList<T> list = new JList<>(selectionValues);
        JScrollPane scrollPane = new JScrollPane();
        scrollPane.setViewportView(list);
        panel.add(scrollPane, BorderLayout.NORTH);
        if (componentBuilder != null) {
            JComponent component = componentBuilder.buildComponent();
            if (component != null) {
                panel.add(component, BorderLayout.SOUTH);
            }
        }
        JOptionPane.showMessageDialog(parentComponent, panel, title, messageType);
        return list.getSelectedValuesList();
    }

    public static JSplitPane getSplitPane(JPanel leftPanel, JPanel rightPanel) {
        JSplitPane splitPane = new JSplitPane();
        splitPane.setOneTouchExpandable(true);//让分割线显示出箭头
        splitPane.setContinuousLayout(true);//操作箭头，重绘图形
        splitPane.setOrientation(JSplitPane.HORIZONTAL_SPLIT);//设置分割线方向

        splitPane.setLeftComponent(leftPanel);//布局中添加组件 ，面板1
        splitPane.setRightComponent(rightPanel);//添加面板2
        splitPane.setDividerSize(2);//设置分割线的宽度
        return splitPane;
    }


    public static JSplitPane createVerticalSplitPane(JPanel topPanel, JPanel bottomPane) {
        JSplitPane vSplitPane = new JSplitPane();
        vSplitPane.setOneTouchExpandable(true);//让分割线显示出箭头
        vSplitPane.setContinuousLayout(true);//操作箭头，重绘图形
        vSplitPane.setOrientation(JSplitPane.VERTICAL_SPLIT);//设置分割线方向
        vSplitPane.setDividerSize(2);//设置分割线的宽度
        vSplitPane.setDividerLocation(600);
        vSplitPane.setTopComponent(topPanel);
        vSplitPane.setBottomComponent(bottomPane);
        return vSplitPane;
    }

    public static JToggleButton getToggleButton() {
        JToggleButton relayJToggleBtn = new JToggleButton();
        // 首先设置不绘制按钮边框
        relayJToggleBtn.setBorderPainted(false);
        relayJToggleBtn.setDoubleBuffered(true);
        relayJToggleBtn.setContentAreaFilled(false);
        relayJToggleBtn.setBorder(BorderFactory.createEmptyBorder());
        ImageIcon toggleOnImageIcon = SwingUtil.getResourceAsImageIcon(newToggleOnIconPath);
        ImageIcon toggleOffImageIcon = SwingUtil.getResourceAsImageIcon(newToggleOffIconPath);
        relayJToggleBtn.setBorderPainted(false);
        relayJToggleBtn.setSelectedIcon(toggleOnImageIcon);
        relayJToggleBtn.setIcon(toggleOffImageIcon);
        return relayJToggleBtn;
    }

    public static void setPanelBorder(JPanel rootPanel, String title) {
        TitledBorder titledBorder = new TitledBorder(BorderFactory.createLineBorder(Color.BLUE, 2), title);
        titledBorder.setTitleFont(new Font("微软雅黑", Font.BOLD, 16));
        titledBorder.setTitleColor(BORDER_COLOR);
        rootPanel.setBorder(titledBorder);
    }


    public static GridBagConstraints getGridBagConstraints() {
        GridBagConstraints constraints = new GridBagConstraints();
        // 组件填充显示区域，both为横向纵向都填充满单元格
        constraints.fill = GridBagConstraints.BOTH;
        // 指定组件间距
        constraints.insets = new Insets(1, 1, 1, 1);
        // 指定组件横纵的拉伸比例
        constraints.weightx = 1;
        constraints.weighty = 1;
        // gridwidth置为1，即每个按钮占据一个单元格
        constraints.gridwidth = 1;
        return constraints;
    }


    public static JButton getButton(String command1, String command2) {
        JButton button = new JButton(command1);
        button.setPreferredSize(new Dimension(100, 30));
        button.setBackground(new Color(192, 192, 193));
        button.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 改变按钮字体颜色状态
                if (button.getText().equals(command1)) {
                    button.setText(command2);
                    button.setBackground(Color.GREEN);
                    button.setActionCommand(command2);
                } else {
                    button.setText(command1);
                    button.setBackground(new Color(192, 192, 193));
                    button.setActionCommand(command1);
                }

            }
        });
        return button;
    }

    public static void openAndSelectedFilePath(File file) {
        try {
            Runtime.getRuntime().exec(
                    "rundll32 SHELL32.DLL,ShellExec_RunDLL "
                            + "Explorer.exe /select," + file.getAbsolutePath());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void openFileFolder(File file) {
        try {
            if (file.exists()) {
                Desktop.getDesktop().browse(file.toURI());
            }
        } catch (IOException ex) {
            log.error(ex.getMessage(), ex);
        }

    }

    public static void openFilePath(File file) {
        try {
            Desktop.getDesktop().open(file);
        } catch (IOException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    //十六进制文本框
    public static void changeDocumentFilter(JTextField textField, boolean isHex) {
        if (isHex) {
            ((AbstractDocument) textField.getDocument()).setDocumentFilter(new Filters.BlankHexFilter());
        } else {
            ((AbstractDocument) textField.getDocument()).setDocumentFilter(new DocumentFilter());
        }
    }

    public static byte boolean2Byte(boolean val) {
        return (byte) (val ? 1 : 0);
    }

    //检查JtextField是否为ip地址
    public static boolean checkIp(JTextField jTextField) {
        String ip = jTextField.getText();
        if (ip.matches("(^((2[0-4]\\d.)|(25[0-5].)|(1\\d{2}.)|(\\d{1,2}.))((2[0-5]{2}.)|(1\\d{2}.)|(\\d{1,2}.){2})((1\\d{2})|(2[0-5]{2})|(\\d{1,2})))")) {
            String[] ips = ip.split("\\.");
            for (String ip1 : ips) {
                int i = Integer.parseInt(ip1);
                if (i > 255) {
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }
    }

    public static void showNonModalDialog(Component parent, String message) {
        JOptionPane pane = new JOptionPane(message, JOptionPane.WARNING_MESSAGE);
        JDialog dialog = pane.createDialog(parent, "提示");
        dialog.setModal(false);
        dialog.setVisible(true); // 显示对话框

        new Timer(3000, e -> {
            if(dialog.isVisible()) { // 检查对话框是否仍显示
                dialog.dispose(); // 关闭对话框
            }
            ((Timer)e.getSource()).stop(); // 停止定时器
        }).start(); // 3秒后自动关闭
    }

}