package ui.base.memo;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayDeque;
import java.util.Iterator;
import java.util.Spliterator;
import java.util.function.Consumer;

public class FixedSizeQueue<T> implements Iterable<T> {
    private final int maxSize;
    private final ArrayDeque<T> queue;

    public FixedSizeQueue(int maxSize) {
        this.maxSize = maxSize;
        this.queue = new ArrayDeque<>(maxSize);
    }

    public synchronized void offer(T item) {
        if (queue.size() == maxSize) {
            queue.poll(); // 当队列满时，移除最早添加的元素
        }
        queue.offer(item); // 添加新元素
    }

    public synchronized T peek() {
        return queue.peekLast(); // 查看但不移除队列头部的元素
    }

    public synchronized T poll() {
        return queue.pollLast(); // 移除并返回队列头部的元素
    }

    public synchronized boolean isEmpty() {
        return queue.isEmpty();
    }

    public synchronized int size() {
        return queue.size();
    }

    public synchronized void clear() {
        queue.clear();
    }


    @NotNull
    @Override
    public Iterator<T> iterator() {
        return queue.iterator();
    }

    @Override
    public void forEach(Consumer<? super T> action) {
        queue.forEach(action);
    }

    @Override
    public Spliterator<T> spliterator() {
        return queue.spliterator();
    }
}