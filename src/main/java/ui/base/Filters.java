package ui.base;

import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.Document;
import javax.swing.text.DocumentFilter;

public class Filters {

    public static class HexadecimalDocumentFilter extends DocumentFilter {

        @Override
        public void insertString(FilterBypass fb, int offset, String string, AttributeSet attr) throws BadLocationException {
            if (string != null) {
                string = string.toUpperCase();
                if (isHexadecimal(string)) {
                    super.insertString(fb, offset, string, attr);
                }
            }
        }

        @Override
        public void replace(FilterBypass fb, int offset, int length, String text, AttributeSet attrs) throws BadLocationException {
            if (text != null) {
                text = text.toUpperCase();
                if (isHexadecimal(text)) {
                    super.replace(fb, offset, length, text, attrs);
                }
            }
        }

        @Override
        public void remove(FilterBypass fb, int offset, int length) throws BadLocationException {
            super.remove(fb, offset, length);
        }

        private boolean isHexadecimal(String text) {
            for (char c : text.toCharArray()) {
                if (!Character.isDigit(c) && (c < 'A' || c > 'F')) {
                    return false;
                }
            }
            return true;
        }
    }

    public static class LengthLimitingDocumentFilter extends DocumentFilter {
        private final int maxLength;

        public LengthLimitingDocumentFilter(int maxLength) {
            this.maxLength = maxLength;
        }

        @Override
        public void insertString(FilterBypass fb, int offset, String string, AttributeSet attr) throws BadLocationException {
            if (fb.getDocument().getLength() + string.length() <= maxLength) {
                super.insertString(fb, offset, string, attr);
            }
        }

        @Override
        public void replace(FilterBypass fb, int offset, int length, String text, AttributeSet attrs) throws BadLocationException {
            if (fb.getDocument().getLength() - length + text.length() <= maxLength) {
                super.replace(fb, offset, length, text, attrs);
            }
        }
    }

    public static class BlankHexFilter extends DocumentFilter {

        @Override
        public void replace(FilterBypass fb, int offset, int length, String string, AttributeSet attrs) throws BadLocationException {
            if (string != null && string.length() == 1) {
                if (isHexString(string) || string.equals(" ")) {
                    if (isHexString(string)) {
                        // 如果是十六进制字符
                        boolean shouldAddSpace = (offset + 2) % 3 == 0;
                        // 检查替换操作的结束位置是否有空格
                        int end = offset + length;
                        if (end < fb.getDocument().getLength() && fb.getDocument().getText(end, 1).equals(" ")) {
                            shouldAddSpace = false;
                        }
                        if (shouldAddSpace) {
                            string = string + " ";
                        }
                    } else if (string.equals(" ")) {
                        // 如果是空格，不允许插入空格在非预期位置
                        if ((offset + 1) % 3 != 0) {
                            return;
                        }
                    }
                    super.replace(fb, offset, length, string.toUpperCase(), attrs);
                }
            } else {
                super.replace(fb, offset, length, string != null ? string.toUpperCase() : null, attrs);
            }
        }

        @Override
        public void remove(FilterBypass fb, int offset, int length) throws BadLocationException {
            // 获取当前文档的内容
            Document doc = fb.getDocument();
            String currentText = doc.getText(0, doc.getLength());

            // 计算实际删除的长度，包含可能的空格
            int end = offset + length;
            if (end < currentText.length() && currentText.charAt(end) == ' ') {
                length++;
            }

            // 移除字符
            super.remove(fb, offset, length);
        }

        private boolean isHexString(String string) {
            if (string == null) {
                return false;
            }
            String hexPattern = "^[A-Fa-f0-9]+$";
            return string.matches(hexPattern);
        }
    }

    public static class HexLimiterFilter extends LengthLimitingDocumentFilter {

        public HexLimiterFilter(int maxLength) {
            super(maxLength);
        }

        @Override
        public void replace(FilterBypass fb, int offset, int length, String string, AttributeSet attrs) throws BadLocationException {
            if (string.length() == 1) {
                if (isHexString(string)) {
                    if ((offset + 1) % 3 == 0) {
                        string = " " + string;
                    }
                    super.replace(fb, offset, length, string.toUpperCase(), attrs);
                }
            } else {
                super.replace(fb, offset, length, string.toUpperCase(), attrs);
            }
        }

        private boolean isHexString(String string) {
            if (string == null) {
                return false;
            }
            String hexPattern = "^[A-Fa-f0-9]+$";
            return string.matches(hexPattern);
        }
    }

    public static class HexPasteLimiterFilter extends LengthLimitingDocumentFilter {
        public HexPasteLimiterFilter(int maxLength) {
            super(maxLength);
        }

        @Override
        public void replace(FilterBypass fb, int offset, int length, String string, AttributeSet attrs) throws BadLocationException {
            // 单个字符输入
            if (string.length() == 1) {
                if (isHexString(string)) {
                    if ((offset + 1) % 3 == 0) {
                        string = " " + string;
                    }
                    super.replace(fb, offset, length, string.toUpperCase(), attrs);
                }
            } else {// 一整串字符粘贴

                //①符合16进制但未处理的字符串，格式化后填入
                if (isHexString(string)) {
                    StringBuilder formattedString = new StringBuilder();
                    for (int i = 0; i < string.length(); i += 2) { // 将字符串分割成每两个字符一组，然后在每组后面加上空格
                        formattedString.append(string, i, Math.min(i + 2, string.length())).append(" ");
                    }
                    formattedString.setLength(formattedString.length() - 1); // 如果字符串长度是偶数，去掉最后一个空格
                    super.replace(fb, offset, length, formattedString.toString().toUpperCase(), attrs);
                    // ②符合16进制且已经处理的字符串，直接填入
                } else if (isFormattedHexString(string)) {
                    super.replace(fb, offset, length, string.toUpperCase(), attrs);
                }
                // ③ 不符合16进制的字符串，不给填入
            }
        }

        private boolean isHexString(String string) {
            if (string == null) {
                return false;
            }
            String hexPattern = "^[A-Fa-f0-9]+$";
            return string.matches(hexPattern);
        }

        private boolean isFormattedHexString(String string) {
            if (string == null) {
                return false;
            }
            String hexPattern = "^[A-Fa-f0-9 ]+$";
            return string.matches(hexPattern);
        }
    }

}
