package ui.base.component.TabbedPane;

import ui.model.MainModel;

import java.awt.*;
import java.awt.dnd.*;

/**
 * 投放目标监听器
 */
public class DnDTargetListener implements DropTargetListener {
    MultifunctionalTabbedPane targetTabbedPane;


    private final DnDDragGestureListener dnDDragGestureListener;
    private final MainModel mainModel;
    public DnDTargetListener(MainModel mainModel, DnDDragGestureListener dnDDragGestureListener) {
        this.mainModel = mainModel;
        this.dnDDragGestureListener = dnDDragGestureListener;
    }

    public void dragEnter(DropTargetDragEvent ev) {
        targetAcceptDrag(ev);
    }

    public void dragExit(DropTargetEvent ev) {
    }

    public void dragOver(DropTargetDragEvent ev) {
    }

    public void dropActionChanged(DropTargetDragEvent ev) {
    }

    public void drop(DropTargetDropEvent ev) {
        targetTabbedPane = (MultifunctionalTabbedPane) ((DropTarget) ev.getSource()).getComponent();

//     resolve:   adding container's parent to itself
        if (isSourceTheParentOfTarget()) return;

        if (isAllowMove()) return;

        if (ev.isDataFlavorSupported(TransferableOfTabbedPane.tabbedPaneFlavor)) {
            ev.acceptDrop(DnDConstants.ACTION_COPY_OR_MOVE);
            moveContentFromSourceToTargetTabbedPane(ev);
            return;
        }
        ev.rejectDrop();
    }

    /**
     * Accept the drag
     */
    private void targetAcceptDrag(DropTargetDragEvent ev) {
        ev.acceptDrag(ev.getDropAction());
    }

    private Boolean isAllowMove() {
        MultifunctionalTabbedPane sourceTabbedPane = DnDDragGestureListener.getMultifunctionalTabbedPane();
        return ((targetTabbedPane.getX() == sourceTabbedPane.getX() && targetTabbedPane.getWidth() == sourceTabbedPane.getWidth()) || sourceTabbedPane.getTabCount() == 1);
    }

    private void moveContentFromSourceToTargetTabbedPane(DropTargetDropEvent ev) {
        MultifunctionalTabbedPane sourceTabbedPane = DnDDragGestureListener.getMultifunctionalTabbedPane();
        int index = sourceTabbedPane.getSelectedIndex();
        TabComponent tabComponent = (TabComponent) (sourceTabbedPane.getTabComponentAt(index));

        targetTabbedPane.addTab("", sourceTabbedPane.getComponentAt(index));
        int newIndex = targetTabbedPane.getTabCount() - 1;
        TabComponent newTabComponent = new TabComponent(mainModel, tabComponent.getTitle(), newIndex, targetTabbedPane);
        targetTabbedPane.setTabComponentAt(newIndex, newTabComponent);
        ev.dropComplete(true);
        tabComponent.updateIndexInSourceTabbedPane();
//            for (int i = 0; i < sourceTabbedPane.getTabCount(); i++) {
//                TabComponent tab = (TabComponent) sourceTabbedPane.getTabComponentAt(i);
//                tab.setTabIndex(i);
//                sourceTabbedPane.setSelectedIndex(0);
//            }
        for (int i = 0; i < targetTabbedPane.getTabCount(); i++) {
            targetTabbedPane.setSelectedIndex(newIndex);
        }
    }

    public Boolean isSourceTheParentOfTarget() {
        MultifunctionalTabbedPane sourceTabbedPane = DnDDragGestureListener.getMultifunctionalTabbedPane();
        Component parent = targetTabbedPane.getParent();
        boolean isSourceTheParentOfTarget = false;
        while (parent.getParent() != null) {
            parent = parent.getParent();
            if (parent.getClass().toString().contains("MultifunctionalTabbedPane"))
                if (parent == sourceTabbedPane) {
                    isSourceTheParentOfTarget = true;
                    break;
                }
        }
        return isSourceTheParentOfTarget;
    }
}