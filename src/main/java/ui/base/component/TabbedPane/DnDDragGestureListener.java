package ui.base.component.TabbedPane;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.dnd.DragGestureEvent;
import java.awt.dnd.DragGestureListener;
import java.awt.dnd.InvalidDnDOperationException;

/**
 * 拖动手势的时间侦听器
 */
@Slf4j
public class DnDDragGestureListener implements DragGestureListener {
    private static MultifunctionalTabbedPane multifunctionalTabbedPane;

    public static MultifunctionalTabbedPane getMultifunctionalTabbedPane() {
        return DnDDragGestureListener.multifunctionalTabbedPane;
    }

    private final DnDSourceListener dnDSourceListener;

    public DnDDragGestureListener(DnDSourceListener dnDSourceListener) {
        this.dnDSourceListener = dnDSourceListener;
    }

    public void dragGestureRecognized(DragGestureEvent e) {
        multifunctionalTabbedPane = (MultifunctionalTabbedPane) e.getComponent();
        try {
            e.startDrag(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR), new TransferableOfTabbedPane(multifunctionalTabbedPane),
                    dnDSourceListener);
        } catch (InvalidDnDOperationException ex) {
            log.error(ex.getMessage(), ex);

        }
    }
}