package ui.base.component.TabbedPane;

import ui.model.MainModel;

import java.awt.dnd.DragSourceDragEvent;
import java.awt.dnd.DragSourceDropEvent;
import java.awt.dnd.DragSourceEvent;
import java.awt.dnd.DragSourceListener;

/**
 * 拖拽源监听器
 */
class DnDSourceListener implements DragSourceListener {

    private final MainModel mainModel;
    private final MultifunctionalTabbedPane tabbedPane;

    public DnDSourceListener(MultifunctionalTabbedPane tabbedPane, MainModel mainModel) {
        this.tabbedPane = tabbedPane;
        this.mainModel = mainModel;
    }

    public void dragEnter(DragSourceDragEvent e) {
        mainModel.getUiModel().dragStart(tabbedPane);
    }

    public void dragExit(DragSourceEvent e) {
    }

    public void dragOver(DragSourceDragEvent e) {
    }

    public void dragDropEnd(DragSourceDropEvent e) {
        mainModel.getUiModel().dragEnd();
    }

    public void dropActionChanged(DragSourceDragEvent e) {
        System.out.println("change");
    }
}