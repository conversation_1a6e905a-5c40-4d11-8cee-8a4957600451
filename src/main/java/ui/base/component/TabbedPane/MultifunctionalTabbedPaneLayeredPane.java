package ui.base.component.TabbedPane;

import lombok.Getter;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.UIObserver;

import javax.swing.*;
import java.awt.*;

/**
 * 装有MultifunctionalTabbedPane、具有展示被拖拽的选项卡功能的LayeredPane
 */

public class MultifunctionalTabbedPaneLayeredPane extends JLayeredPane implements UIObserver {
    @Getter
    private final ClientView clientView;
    @Getter
    private final JLabel tabShowLabel = new JLabel();
    private static final float SPLIT_RATIO = 0.6f;
    @Getter
    private JPanel panelContainTabbedPane;
    @Getter
    private final MultifunctionalTabbedPane multifunctionalTabbedPane;

    private DragAnimationWorker dragAnimationWorker;

    public MultifunctionalTabbedPaneLayeredPane(ClientView clientView, MainModel mainModel) {
        this.multifunctionalTabbedPane = new MultifunctionalTabbedPane(mainModel);
        this.clientView = clientView;
        createView();
//        new DragAnimation(this, multifunctionalTabbedPane.getDnDDragGestureListener());
        mainModel.getUiModel().registerObserver(this);
    }

    public void addTabWithCloseBtn(String title, Component component) {
        multifunctionalTabbedPane.addTabWithCloseBtn(title, component);
    }

    public void createView() {
        panelContainTabbedPane = new JPanel();
        panelContainTabbedPane.setLayout(new GridLayout(1, 1, 5, 5));
        panelContainTabbedPane.add(multifunctionalTabbedPane);
        add(panelContainTabbedPane, 10, 0);
        setBoundsView();
    }

    public void setBoundsView() {
        Dimension size = clientView.getWindowsSize();
        panelContainTabbedPane.setBounds(0, 0, (int) (size.getWidth() * SPLIT_RATIO - 15), (int) (size.getHeight() - 130));
    }

    @Override
    public void dragStart(MultifunctionalTabbedPane tabbedPane) {
        dragAnimationWorker = new DragAnimationWorker(this);
        dragAnimationWorker.start();
    }

    @Override
    public void dragEnd() {
        dragAnimationWorker.stop();
    }
}
