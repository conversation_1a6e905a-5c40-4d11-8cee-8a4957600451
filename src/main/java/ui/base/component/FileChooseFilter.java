package ui.base.component;

import javax.swing.filechooser.FileFilter;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class FileChooseFilter extends FileFilter {

    private final List<String> fileFilterList;

    public FileChooseFilter(String filerSuffix) {
        fileFilterList = new ArrayList<>();
        fileFilterList.add(filerSuffix);
    }

    public FileChooseFilter(List<String> filerSuffixList) {
        fileFilterList = filerSuffixList;
    }

    @Override
    public boolean accept(File f) {
        String fileName = f.getName().toLowerCase();
        for (String suffix : fileFilterList) {
            if (fileName.endsWith(suffix)) {
                return true;
            }
        }

        return false; // Reject any other files
    }

    @Override
    public String getDescription() {
        return "Excel文件" + fileFilterList;
    }
}
