package ui.base.component;

import lombok.Getter;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

@Getter
public class CustomDropdownButton extends JPanel {
    private final JButton mainButton;
    private final JButton dropdownButton;
    private final JPopupMenu popupMenu;
    private final List<JButton> dropdownButtons;

    public CustomDropdownButton(JButton... buttons) {
        if (buttons.length == 0) {
            throw new IllegalArgumentException("At least one button must be provided");
        }

        setLayout(new BoxLayout(this, BoxLayout.X_AXIS));

        mainButton = buttons[0];
        dropdownButton = new JButton("▼");
        dropdownButton.setMargin(new Insets(0, 2, 0, 2));

        add(mainButton);
        add(dropdownButton);

        popupMenu = new JPopupMenu();
        popupMenu.setLayout(new BoxLayout(popupMenu, BoxLayout.Y_AXIS));
        dropdownButtons = new ArrayList<>(Arrays.asList(buttons).subList(1, buttons.length));

        for (JButton button : dropdownButtons) {
            button.setAlignmentX(Component.LEFT_ALIGNMENT);
            button.setHorizontalAlignment(SwingConstants.LEFT);
            button.setMaximumSize(new Dimension(Integer.MAX_VALUE, button.getMaximumSize().height));
            popupMenu.add(button);
        }

        dropdownButton.addActionListener(e -> {
            int width = mainButton.getWidth() + dropdownButton.getWidth();
            popupMenu.setPopupSize(width, popupMenu.getPreferredSize().height);
            popupMenu.show(mainButton, 0, mainButton.getHeight());
        });

        // 如果只有一个按钮，隐藏下拉箭头
        dropdownButton.setVisible(buttons.length > 1);

        // 确保所有下拉按钮与主按钮宽度一致
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                int width = mainButton.getWidth() + dropdownButton.getWidth();
                popupMenu.setPopupSize(width, popupMenu.getPreferredSize().height);
                for (JButton button : dropdownButtons) {
                    button.setMaximumSize(new Dimension(width, button.getMaximumSize().height));
                }
            }
        });
    }

    @Override
    public void setEnabled(boolean enabled) {
        mainButton.setEnabled(enabled);
        dropdownButton.setEnabled(enabled);
        for (JButton button : dropdownButtons) {
            button.setEnabled(enabled);
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("Custom Dropdown Button Demo");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

            JButton mainButton = new JButton("开始测试");
            JButton option1 = new JButton("选项 1");
            JButton option2 = new JButton("选项 2");
            JButton option3 = new JButton("选项 3");

            option1.addActionListener(e -> System.out.println("选项 1 被点击"));
            option2.addActionListener(e -> System.out.println("选项 2 被点击"));
            option3.addActionListener(e -> System.out.println("选项 3 被点击"));

            CustomDropdownButton customButton = new CustomDropdownButton(mainButton, option1, option2, option3);
            frame.getContentPane().add(customButton);
            frame.pack();
            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
        });
    }
}