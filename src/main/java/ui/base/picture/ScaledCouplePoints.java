package ui.base.picture;

import lombok.Data;

@Data
public class ScaledCouplePoints {

    private ScaledPoint startPoint;

    private ScaledPoint endPoint;

    public void setStartPoint(double x, double y) {
        if (startPoint == null) {
            startPoint = new ScaledPoint();
        }
        startPoint.set(x, y);
    }

    public void setEndPoint(double x, double y) {
        if (endPoint == null) {
            endPoint = new ScaledPoint();
        }
        endPoint.set(x, y);
    }

    public boolean isEmpty() {
        return startPoint.isEmpty() || endPoint.isEmpty();
    }

    public void clear() {
        startPoint.clear();
        endPoint.clear();
    }

}
