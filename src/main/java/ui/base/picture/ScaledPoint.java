package ui.base.picture;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScaledPoint implements Serializable {
    private static final long serialVersionUID = 984510113709L;
    private double x;
    private double y;

    public void set(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public ScaledPoint(Double x, Double y) {
        this.x = x == null ? 0 : x;
        this.y = y == null ? 0 : y;
    }

    @JSONField(serialize = false)
    public boolean isEmpty() {
        return x == 0 && y == 0;
    }

    public ScaledPoint copyFrom(ScaledPoint scaledPoint) {
        setX(scaledPoint.getX());
        setY(scaledPoint.getY());
        return this;
    }

    public ScaledPoint copy() {
        return new ScaledPoint(x, y);
    }

    public void clear() {
        x = 0;
        y = 0;
    }

}