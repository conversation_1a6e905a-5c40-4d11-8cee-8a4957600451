package ui.base.renderer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.swing.*;
import javax.swing.border.MatteBorder;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

@Getter
@Slf4j
public class MultiLineCellRenderer extends JTextArea implements TableCellRenderer, ColorCellRenderer {

    private final ColorRender colorRender = new ColorRender();

    public MultiLineCellRenderer() {
        setLineWrap(false);    //设置为false才不影响自适应列宽的方法
        setWrapStyleWord(true);
        setOpaque(true);
    }

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
        if (!SwingUtilities.isEventDispatchThread()) {
            log.warn("MultiLineCellRenderer.getTableCellRendererComponent在非EDT线程中被调用: {}, row={}, col={}", 
                    Thread.currentThread().getName(), row, column);
            JLabel label = new JLabel(value == null ? "" : value.toString());
            label.setOpaque(true);
            label.setBackground(isSelected ? table.getSelectionBackground() : table.getBackground());
            label.setForeground(table.getForeground());
            label.setFont(table.getFont());
            return label;
        }

        Color bgColor;
        if (colorRender.getColor(row) != null) {
            bgColor = colorRender.getColor(row);
        } else {
            bgColor = colorRender.getColor(row, column);
        }

        if (isSelected) {
            if (bgColor == null) {
                setBackground(table.getSelectionBackground());
            } else {
                setBackground(table.getSelectionBackground());
            }
        } else {
            setBackground(bgColor);
        }
        setForeground(table.getForeground());
        setFont(table.getFont());
        
        try {
            setText(value == null ? "" : value.toString());
        } catch (Exception e) {
            log.error("设置文本失败 row={}, col={}: {}", row, column, e.getMessage());
            setText("");
        }

        setBorder(new MatteBorder(1, 0, 1, 1, table.getGridColor()));
        
        if (table.getParent() instanceof JViewport) {
            table.setPreferredScrollableViewportSize(new Dimension(Integer.MAX_VALUE, table.getPreferredScrollableViewportSize().height));
            table.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        }
        
        return this;
    }

}
 
