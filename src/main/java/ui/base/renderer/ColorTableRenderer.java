package ui.base.renderer;

import lombok.Getter;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/8/23 14:29
 * @description :
 * @modified By :
 * @since : 2023/8/23
 **/
@Getter
public class ColorTableRenderer extends DefaultTableCellRenderer implements ColorCellRenderer {

    private final ColorRender colorRender = new ColorRender();

    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
        if (isSelected) {
            setForeground(table.getSelectionForeground());
            setBackground(table.getSelectionBackground());
        } else {
            if (colorRender.getColor(row) != null) {
                setBackground(colorRender.getColor(row));
            } else {
                Color cellColor = colorRender.getColor(row, column);
                cellColor = (cellColor == null) ? table.getBackground() : cellColor;
                setBackground(cellColor);
            }
            setForeground(table.getForeground());
            setFont(table.getFont());
        }
        return super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
    }

}