package ui.base.renderer;

import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import java.awt.*;

public class CustomTableHeader extends JTableHeader {

    public CustomTableHeader(TableColumnModel columnModel) {
        super(columnModel);
    }

    @Override
    public Dimension getPreferredSize() {
        Dimension size = super.getPreferredSize();
        // 假设你想将表头高度设置为50像素，仅修改高度
        size.height = 40;
        return size;
    }
}