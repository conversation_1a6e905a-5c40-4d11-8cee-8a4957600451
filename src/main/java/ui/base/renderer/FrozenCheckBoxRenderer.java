package ui.base.renderer;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.border.EmptyBorder;
import javax.swing.border.MatteBorder;
import javax.swing.plaf.UIResource;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

public class FrozenCheckBoxRenderer extends JCheckBox implements TableCellRenderer, UIResource {
    private final Border noFocusBorder = new EmptyBorder(1, 1, 1, 1);

    public FrozenCheckBoxRenderer() {
        super();
        setHorizontalAlignment(JLabel.CENTER);
        setBorderPainted(true);
    }

    public Component getTableCellRendererComponent(JTable table, Object value,
                                                   boolean isSelected, boolean hasFocus, int row, int column) {
        setForeground(table.getForeground());
        setBackground(table.prepareRenderer(table.getCellRenderer(row,0),row,0).getBackground());
        setSelected((value != null && (Boolean) value));
        if (hasFocus) {
            setBorder(UIManager.getBorder("Table.focusCellHighlightBorder"));
        } else {
            setBorder(noFocusBorder);
        }
        setBorder(new MatteBorder(1, 1, 1, 1, table.getGridColor()));
        return this;
    }
}
