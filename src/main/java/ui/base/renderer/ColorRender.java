package ui.base.renderer;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class ColorRender {

    private final Map<Cell, Color> cellRenderColorMap = new HashMap<>();

    public void setColor(Integer row, Integer column, Color color) {
        cellRenderColorMap.put(new Cell(row, column), color);
    }

    public void clearColor() {
        cellRenderColorMap.clear();
    }


    public void clearColor(int row) {
        cellRenderColorMap.keySet().stream()
                .filter(color -> color.getRow().equals(row))
                .collect(Collectors.toList())
                .forEach(cellRenderColorMap::remove);
//        cellRenderColorMap.remove(new Cell(row, null));
    }

    public void clearColor(int row, int column) {
        cellRenderColorMap.remove(new Cell(row, column));
    }

    public Color getColor(int row) {
        return cellRenderColorMap.get(new Cell(row, null));
    }

    public Color getColor(int row, int column) {
        return cellRenderColorMap.get(new Cell(row, column));
    }

}
