package ui.base;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Data
public class AppInfo {
    private static SystemInfo systemInfo;
    private static HardwareAbstractionLayer hardware;
    private static OperatingSystem operatingSystem;

    static {
        initializeSystemInfo();
    }

    /**
     * 初始化系统信息
     */
    private static void initializeSystemInfo() {
        try {
            systemInfo = new SystemInfo();
            hardware = systemInfo.getHardware();
            operatingSystem = systemInfo.getOperatingSystem();
            log.info("系统信息监控初始化成功 - OS: {}, 处理器: {}",
                    operatingSystem.getFamily(),
                    hardware.getProcessor().getProcessorIdentifier().getName());
        } catch (Exception e) {
            log.error("系统信息监控初始化失败", e);
        }
    }

    /**
     * 将指定的电源模式设置为永不休眠
     * @param mode "ac" 或 "dc"
     * @param currentMinutes 当前的休眠分钟数
     */
    private void setPowerSettingToNeverSleep(String mode, int currentMinutes) {
        if (currentMinutes <= 0) {
            return;
        }

        String settingName = "standby-timeout-" + mode;
        try {
            String command = String.format("powercfg /change %s 0", settingName);
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("电源设置已自动修改: {}电源模式 从 {} 分钟 修改为 永不休眠",
                         mode.toUpperCase(), currentMinutes);
            } else {
                log.error("自动修改电源设置 {} 失败, 退出码: {}", settingName, exitCode);
            }
        } catch (Exception e) {
            log.error("自动修改电源设置 {} 失败", settingName, e);
        }
    }

    /**
     * 检查Windows电源设置是否为永不休眠
     *
     * @return 电源设置是否允许休眠（true=会休眠，false=永不休眠）
     */
    private boolean checkWindowsPowerSleepSettings() {
        this.initialSleepMinutes = getWindowsPowerSleepMinutes();
        int acSleepMinutes = initialSleepMinutes[0];
        int dcSleepMinutes = initialSleepMinutes[1];

        if (acSleepMinutes < 0 || dcSleepMinutes < 0) {
            log.warn("获取电源睡眠设置失败，默认不会休眠");
            this.lastReadSleepMinutes = this.initialSleepMinutes;
            return false;
        }

        log.info("当前电源睡眠设置 - AC电源: {} 分钟, DC电源: {} 分钟",
                acSleepMinutes == 0 ? "永不" : acSleepMinutes,
                dcSleepMinutes == 0 ? "永不" : dcSleepMinutes);

        boolean changed = false;
        if (acSleepMinutes > 0) {
            setPowerSettingToNeverSleep("ac", acSleepMinutes);
            changed = true;
        }
        if (dcSleepMinutes > 0) {
            setPowerSettingToNeverSleep("dc", dcSleepMinutes);
            changed = true;
        }

        if (changed) {
            log.info("设置修改后，重新获取电源状态...");
            this.lastReadSleepMinutes = getWindowsPowerSleepMinutes();
            int finalAc = this.lastReadSleepMinutes[0];
            int finalDc = this.lastReadSleepMinutes[1];
            log.info("更新后电源睡眠设置 - AC电源: {} 分钟, DC电源: {} 分钟",
                    finalAc == 0 ? "永不" : finalAc,
                    finalDc == 0 ? "永不" : finalDc);
        } else {
            this.lastReadSleepMinutes = this.initialSleepMinutes;
        }

        // 如果AC或DC任一设置了睡眠时间（非0），则认为会休眠
        return this.lastReadSleepMinutes[0] > 0 || this.lastReadSleepMinutes[1] > 0;
    }

    /**
     * 获取Windows电源计划中的睡眠超时时间
     *
     * @return 包含[AC超时, DC超时]的数组（分钟），-1表示获取失败
     */
    private int[] getWindowsPowerSleepMinutes() {
        int[] sleepMinutes = new int[]{-1, -1}; // {AC, DC}
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (!os.contains("windows")) {
                log.info("非Windows系统，默认返回不会休眠");
                return new int[]{0, 0};
            }
            String sleepCommand = "powercfg /query SCHEME_CURRENT SUB_SLEEP STANDBYIDLE";
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", sleepCommand);
            Process process = pb.start();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream(), "GBK"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("当前交流电源设置索引")) {
                        sleepMinutes[0] = parseHexValueToMinutes(line);
                    } else if (line.contains("当前直流电源设置索引")) {
                        sleepMinutes[1] = parseHexValueToMinutes(line);
                    }
                }
            }
            process.waitFor();

        } catch (Exception e) {
            log.warn("检查Windows电源设置失败: {}", e.getMessage());
            return new int[]{-1, -1};
        }

        // 如果某个值未找到，则默认为0（永不休眠）
        if (sleepMinutes[0] == -1) sleepMinutes[0] = 0;
        if (sleepMinutes[1] == -1) sleepMinutes[1] = 0;

        return sleepMinutes;
    }

    /**
     * 从powercfg输出行中解析十六进制值为分钟
     *
     * @param line 包含设置索引的行
     * @return 分钟数，-1表示解析失败
     */
    private int parseHexValueToMinutes(String line) {
        try {
            String[] parts = line.split(":");
            if (parts.length > 1) {
                // "0x00000000" or "0x..."
                String hexValue = parts[1].trim().split(" ")[0];
                long seconds = Long.parseLong(hexValue.replace("0x", ""), 16);
                return (int) (seconds / 60); // 转换为分钟
            }
        } catch (Exception e) {
            log.warn("解析电源设置行失败: '{}', 错误: {}", line, e.getMessage());
        }
        return -1; // 表示解析失败
    }

    /**
     * 简化的PowerShell检测方法
     *
     * @return 系统是否设置为会休眠
     */
    private boolean checkSleepWithSimplePowerShell() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (!os.contains("windows")) {
                return false;
            }

            // 使用PowerShell获取当前电源计划的睡眠设置
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell.exe",
                    "-Command",
                    "Get-CimInstance -Namespace root\\cimv2\\power -ClassName Win32_PowerSetting | Where-Object {$_.ElementName -like '*sleep*'} | Select-Object ElementName, SettingValue"
            );

            Process process = pb.start();

            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream(), "UTF-8"))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    // 如果输出包含非零的睡眠值，说明设置了睡眠
                    if (line.contains("sleep") && !line.contains("0")) {
                        log.info("检测到睡眠设置: {}", line);
                        return true;
                    }
                }
            }

            process.waitFor();
            return false;

        } catch (Exception e) {
            log.info("PowerShell睡眠设置检测失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检测电源模式是否设置为会休眠
     *
     * @return 电源设置是否为会休眠模式（true=会休眠，false=永不休眠）
     */
    private boolean detectSleepStatus() {
        // 使用电源设置检测方案
        boolean windowsPowerResult = checkWindowsPowerSleepSettings();
        boolean powerShellResult = checkSleepWithSimplePowerShell();

        // 优先使用powercfg命令结果，PowerShell作为备用
        boolean result = windowsPowerResult || powerShellResult;

        if (result) {
            log.info("电源休眠设置检测结果 - PowerCfg: {}, PowerShell: {}",
                    windowsPowerResult, powerShellResult);
        } else {
            log.info("电源设置为永不休眠");
        }

        return result;
    }

    private String serverPid;
    private String client; //客户端名称
    private String appVersion;
    private String secretKey;//密钥
    private int passwordLength;//密码长度
    private String userId; //测试人员uid
    private String userName;//测试人员名字
    private String project; //测试项目
    private Integer projectId; //测试项目Id
    private String testUnit; //测试工位（电脑），格式：电脑名/账户名，如RVC1-01/ICI2 DEQ
    private String clientVersion; //客户端软件版本
    private String ipAddress; //测试电脑IP
    private String machine; //测试机器名
    private String workstation; //测试工位名
    private String buName; //事业单元名
    private Integer platformCode; //测试平台代号
    private String platformName; //测试平台名称
    private Integer statusCode; //测试状态代号
    private String remark; //备注
    private boolean autoLogin; //是否自动登录
    private String email; //邮件
    private String carFactoryName; //车厂名
    private String userIdList; //用户id列表
    private boolean offlineMode;
    private int caseSum; //全功能用例总数
    private int autoTestCaseSum; //自动化测试总数
    private String[] emails; //邮件
    private String[] urls; //机器人url
    private String[] scriptEmails;//脚本测试时要发送的邮件列表
    private int userFontSize;//字体大小
    private String[] multiTableUrls;//多维度表格url
    private boolean pauseWhenTestFailed;//脚本测试失败时是否暂停
    private boolean enableEmailSending;//脚本是否启用邮件发送
    private boolean enableSequenceEmailSending;//动作序列是否启用邮件发送
    private boolean enableSequenceRobotSending;//动作序列是否启用飞书机器人发送
    private boolean enableSequenceCloudDocSending;//动作序列是否启用云文档发送
    private boolean isSmokeTest;//是否是冒烟测试还是功能测试
    private boolean enableUserLog;//是否开启日志按用例保存
    private String userLogPath;//用户日志路径
    private Boolean sleepingStatusCache = null; // 缓存的电源休眠模式设置，null表示未初始化
    private int[] lastReadSleepMinutes = new int[]{-1, -1};
    private int[] initialSleepMinutes = new int[]{-1, -1};

    /**
     * 获取电脑电源休眠模式设置（带缓存功能）
     * 第一次调用时读取电源设置并缓存，后续调用直接返回缓存结果
     *
     * @return 电源是否设置为会休眠模式（true=会休眠，false=永不休眠）
     */
    public boolean getSleepingStatusWithCache() {
        // 如果已有缓存，直接返回
        if (sleepingStatusCache != null) {
            return sleepingStatusCache;
        }

        // 首次调用，读取电源设置并缓存
        try {
            boolean currentSleepingStatus = detectSleepStatus();
            sleepingStatusCache = currentSleepingStatus;
            log.info("初始化电源休眠模式: {}",
                    currentSleepingStatus ? "会休眠" : "永不休眠");

        } catch (Exception e) {
            log.error("检查电源休眠设置时发生异常", e);
            // 异常时缓存false作为默认值（永不休眠）
            sleepingStatusCache = false;
        }

        return sleepingStatusCache;
    }

    /**
     * 刷新电源休眠模式设置缓存
     * 强制重新读取当前电源休眠设置并更新缓存
     *
     * @return 更新后的休眠模式设置
     */
    public boolean refreshSleepingStatus() {
        try {
            boolean currentSleepingStatus = detectSleepStatus();

            // 如果设置发生了变化，记录日志并通知观察者
            if (sleepingStatusCache != null && currentSleepingStatus != sleepingStatusCache) {
                log.info("电源休眠模式变更: {} -> {}",
                        sleepingStatusCache ? "会休眠" : "永不休眠",
                        currentSleepingStatus ? "会休眠" : "永不休眠");
                notifyObservers();
            }

            sleepingStatusCache = currentSleepingStatus;

        } catch (Exception e) {
            log.error("刷新电源休眠设置时发生异常", e);
        }

        return sleepingStatusCache != null ? sleepingStatusCache : false;
    }


    public String getFriendlyString() {
        getSleepingStatusWithCache(); // 确保休眠状态已检查并缓存，同时填充lastReadSleepMinutes
        String sleepStatusString;

        if (initialSleepMinutes[0] != -1) {
            String acStatus;
            if (initialSleepMinutes[0] > 0 && lastReadSleepMinutes[0] == 0) {
                acStatus = String.format("%d分钟->永不休眠", initialSleepMinutes[0]);
            } else {
                acStatus = lastReadSleepMinutes[0] == 0 ? "永不休眠" : lastReadSleepMinutes[0] + " 分钟";
            }

            String dcStatus;
            if (initialSleepMinutes[1] > 0 && lastReadSleepMinutes[1] == 0) {
                dcStatus = String.format("%d分钟->永不休眠", initialSleepMinutes[1]);
            } else {
                dcStatus = lastReadSleepMinutes[1] == 0 ? "永不休眠" : lastReadSleepMinutes[1] + " 分钟";
            }
            sleepStatusString = String.format("|  AC: %s, DC: %s", acStatus, dcStatus);
        } else {
            // 如果无法获取具体时间，则回退到旧的显示方式
            sleepStatusString = getSleepingStatusWithCache() ? " | 电源会休眠" : " | 永不休眠";
        }

        return String.format(" %s  |  %s  |  %s  |  %s  |  %s  |  %s  %s",
                StringUtils.isEmpty(userName) ? userId : userName, carFactoryName, project, buName, testUnit,
                offlineMode ? "离线状态" : "在线状态",
                sleepStatusString);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    private final List<AppInfoObserver> observers = new CopyOnWriteArrayList<>();

    /**
     * 注册 AppInfo 观察者
     *
     * @param observer 观察者实例
     */
    public void registerObserver(AppInfoObserver observer) {
        if (!observers.contains(observer)) {
            observers.add(observer);
        }
    }

    /**
     * 注销 AppInfo 观察者
     *
     * @param observer 观察者实例
     */
    public void unregisterObserver(AppInfoObserver observer) {
        observers.remove(observer);
    }

    /**
     * 通知所有注册的观察者
     */
    private void notifyObservers() {
        for (AppInfoObserver observer : observers) {
            observer.updateAppInfo(this);
        }
    }

    public synchronized void setOfflineMode(boolean offlineMode) {
        this.offlineMode = offlineMode;
        notifyObservers();
    }


    // 实时通知
    public synchronized void setSmokeTest(boolean smokeTest) {
        boolean old = this.isSmokeTest;
        this.isSmokeTest = smokeTest;
        log.info("冒烟测试状态变更: {} -> {}", old, smokeTest); // 新增日志
        notifyObservers();
    }

}
