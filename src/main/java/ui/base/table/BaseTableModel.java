package ui.base.table;

import lombok.Getter;
import lombok.Setter;

import javax.swing.table.DefaultTableModel;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-27 11:32
 * @description :
 * @modified By :
 * @since : 2022-7-27
 */
@Getter
public abstract class BaseTableModel<T> extends DefaultTableModel {

    private final List<String> columnNames = getColumnNameList();
    @Setter
    private List<T> tableList;

    public BaseTableModel() {
        setColumnCount(columnNames.size());
        columnIdentifiers.clear();
        for (String columnName : columnNames) {
            addColumn(columnName);
        }
    }

    public BaseTableModel(List<T> tableList) {
        this.tableList = tableList;
//        forbiddenColumns = new ArrayList<>();
        setColumnCount(columnNames.size());
        columnIdentifiers.clear();
        for (String columnName : columnNames) {
            addColumn(columnName);
        }
    }


    @Override
    public void removeRow(int row) {
        tableList.remove(row);
        super.removeRow(row);
    }

    public void clearColumn(int column) {
        for (int row = 0; row < getRowCount(); row++) {
            setValueAt("", row, column);
        }
    }

    public void clear() {
        getDataVector().clear(); //清除表格数据
        fireTableDataChanged();  //通知模型更新
        tableList.clear();
    }

    protected abstract List<String> getColumnNameList();

    protected abstract void addRow(int row, T rowData);

    protected void insertRow(int row, T rowData) {
        tableList.add(row, rowData);
        addRow(row, rowData);
    }

}
