package ui.base.table;

import javax.swing.*;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.TableColumnModelEvent;
import javax.swing.event.TableColumnModelListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

public class TableColumnModelChangeListener implements TableColumnModelListener {
    private final JTable table;
    private boolean columnWidthChanged;

    public TableColumnModelChangeListener(JTable table) {
        this.table = table;
        table.getTableHeader().addMouseListener(new MouseAdapter() {
            @Override
            public void mouseReleased(MouseEvent e) {
                super.mouseReleased(e);
                if (columnWidthChanged) {
                    columnWidthChanged = false;
                    if (table instanceof DefaultTable) {
                        ((DefaultTable<?>) table).changeColumnWidth();
                    }
                }
            }
        });
    }

    @Override
    public void columnMarginChanged(ChangeEvent e) {
        if (!columnWidthChanged) {
            if (table.getTableHeader().getResizingColumn() != null) {
                columnWidthChanged = true;
            }
        }


    }


    @Override
    public void columnAdded(TableColumnModelEvent e) {
    }

    @Override
    public void columnRemoved(TableColumnModelEvent e) {
    }

    @Override
    public void columnMoved(TableColumnModelEvent e) {
    }

    @Override
    public void columnSelectionChanged(ListSelectionEvent e) {

    }


}