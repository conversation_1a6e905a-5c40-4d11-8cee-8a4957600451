package ui.base.table;

import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import sdk.constants.methods.CommonMethods;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.util.*;

import static ui.base.table.DefaultTable.*;

public class TableRowTransferHandler extends TransferHandler {
    private final DataFlavor localObjectFlavor;
    private final JTable table;
    @Setter
    private JTable rowHeaderTable;
    @Setter
    private JTable sideBarTable;
    private int[] selectedRows;

    private final List<DragRowsEventListener> dragRowsEventListenerList = new ArrayList<>();

    public TableRowTransferHandler(JTable table) {
        this(table, null, null);
    }
    public TableRowTransferHandler(JTable table, JTable rowHeaderTable, JTable sideBarTable) {
        this.table = table;
        this.rowHeaderTable = rowHeaderTable;
        this.sideBarTable = sideBarTable;
        localObjectFlavor = new DataFlavor(int[].class, "Array of integers");
    }

    public interface DragRowsEventListener {
        void dragRowsCompleted(Map<Integer, Integer> map);
    }

    public void addDragRowsEventListener(DragRowsEventListener dragRowsEventListener) {
        dragRowsEventListenerList.add(dragRowsEventListener);
    }


    /**
     * 指定拖动操作的数据类型和数据
     */
    @Override
    protected Transferable createTransferable(JComponent c) {
        selectedRows = table.getSelectedRows();
        return new Transferable() {
            @Override
            public DataFlavor[] getTransferDataFlavors() {
                return new DataFlavor[]{localObjectFlavor};
            }

            @Override
            public boolean isDataFlavorSupported(DataFlavor flavor) {
                return flavor.equals(localObjectFlavor);
            }

            @NotNull
            @Override
            public Object getTransferData(DataFlavor flavor) {
                return selectedRows;
            }
        };
    }

    /**
     * 指定拖动操作是否可以导入到JTable中
     */
    @Override
    public boolean canImport(TransferHandler.TransferSupport info) {
        JComponent component = (JComponent) info.getComponent();
        boolean isTableComponent = component == table || component == rowHeaderTable || component == sideBarTable;
        return isTableComponent && info.isDrop() && info.isDataFlavorSupported(localObjectFlavor);
    }

    @Override
    public int getSourceActions(JComponent c) {
        return TransferHandler.COPY_OR_MOVE;
    }


    /**
     * 处理拖动操作的数据并将其导入到JTable中
     */
    @Override
    public boolean importData(TransferHandler.TransferSupport info) {
        JTable.DropLocation dropLocation = (JTable.DropLocation) info.getDropLocation();
        int index = dropLocation.getRow();
        int[] rows;
        try {
            rows = (int[]) info.getTransferable().getTransferData(localObjectFlavor);
        } catch (Exception e) {
            return false;
        }
        DefaultTableModel model = (DefaultTableModel) table.getModel();
        int firstIndex = rows[0];
        int toIndex = index;
        
        // 保存所有行的行高，以便后续调整
        Map<Integer, Integer> allRowHeights = new HashMap<>();
        for (int i = 0; i < table.getRowCount(); i++) {
            allRowHeights.put(i, table.getRowHeight(i));
        }

        // 保存被移动行的行高
        Map<Integer, Integer> movedRowHeights = new HashMap<>();
        for (int row : rows) {
            movedRowHeights.put(row, table.getRowHeight(row));
        }

        TreeMap<Integer, Integer> map;
        if (firstIndex == index) {
            return false;
        } else if (firstIndex > index) {
            map = new TreeMap<>(); // 默认按key升序排序
            //move up
            for (int i = 0; i < rows.length; i++) {
                int fromIndex = rows[i];
                if (fromIndex > index) {
                    if (i != 0) {
                        toIndex++;
                    }
                }
                model.moveRow(fromIndex, fromIndex, toIndex);
                map.put(fromIndex, toIndex);
                toIndex++;
            }
        } else {
            // 按key从降序排序
            map = new TreeMap<>(Collections.reverseOrder());
            //move down
            for (int i = rows.length - 1; i >= 0; i--) {
                int fromIndex = rows[i];
                if (fromIndex < index) {
                    toIndex--;
                }
                model.moveRow(fromIndex, fromIndex, toIndex);
                map.put(fromIndex, toIndex);
            }
        }

        // 移动完成后，根据映射关系调整所有行的行高
        Map<Integer, Integer> newRowHeights = new HashMap<>();

        // 首先，将未移动的行高保留
        for (int i = 0; i < table.getRowCount(); i++) {
            boolean isMovedRow = false;
            for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                if (entry.getValue() == i) {
                    isMovedRow = true;
                    // 被移动的行，使用原始行高
                    newRowHeights.put(i, movedRowHeights.get(entry.getKey()));
                    break;
                }
            }

            if (!isMovedRow) {
                // 未被移动的行，但可能需要适应新内容 找出这一行原来是哪一行
                int originalRow = i;
                for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                    if (entry.getKey() <= i && entry.getValue() > i) {
                        // 行上移了，当前行应该是原来的后一行
                        originalRow++;
                    } else if (entry.getKey() >= i && entry.getValue() < i) {
                        // 行下移了，当前行应该是原来的前一行
                        originalRow--;
                    }
                }

                if (originalRow >= 0 && originalRow < allRowHeights.size()) {
                    newRowHeights.put(i, allRowHeights.get(originalRow));
                } else {
                    // 如果找不到对应的原始行，使用默认行高
                    newRowHeights.put(i, table.getRowHeight());
                }
            }
        }

        // 应用新的行高
        for (Map.Entry<Integer, Integer> entry : newRowHeights.entrySet()) {
            int row = entry.getKey();
            int height = entry.getValue();
            // 设置主表格行高
            table.setRowHeight(row, height);
            // 设置行头表格行高
            if (rowHeaderTable != null) {
                rowHeaderTable.setRowHeight(row, height);
            }
            // 设置侧边栏表格行高
            if (sideBarTable != null) {
                sideBarTable.setRowHeight(row, height);
            }
        }

        // 判断是否有真正的行移动发生，成功后才会执行dragRowsCompleted调用
        boolean hasMoved = false;
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            if (!entry.getKey().equals(entry.getValue())) {
                hasMoved = true;
                break;
            }
        }

        if (hasMoved) {
            for (DragRowsEventListener dragRowsEventListener : dragRowsEventListenerList) {
                dragRowsEventListener.dragRowsCompleted(map);
            }
            // 拖动调整循环缩进
            adjustRowIndentation((DefaultTableModel) table.getModel());
        }
        return true;
    }

}