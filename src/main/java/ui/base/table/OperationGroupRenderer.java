package ui.base.table;

import sdk.base.operation.OperationGroup;

import javax.swing.*;
import java.awt.*;

public class OperationGroupRenderer extends DefaultListCellRenderer {
    @Override
    public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
        JLabel label = (JLabel) super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

        if (value instanceof OperationGroup) {
            OperationGroup group = (OperationGroup) value;
            label.setText(group.getGroupName()); // 假设还有description属性
        }

        return label;
    }
}
