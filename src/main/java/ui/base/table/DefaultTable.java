package ui.base.table;

import common.constant.KeyStrokeConstants;
import common.constant.UiConstants;
import common.utils.StringUtils;
import excelcase.AutoScrollPane;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import sdk.constants.methods.CommonMethods;
import ui.base.BaseView;
import ui.base.renderer.ColorCellRenderer;
import ui.base.renderer.RowHeaderRenderer;
import ui.base.table.rowHeader.RowHeaderTable;
import ui.base.table.rowHeader.SideBarTable;
import ui.callback.TableMenuCallback;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.dnd.DnDConstants;
import java.awt.dnd.DropTarget;
import java.awt.dnd.DropTargetListener;
import java.awt.event.*;
import java.io.Serializable;
import java.util.List;
import java.util.*;

import static common.constant.KeyStrokeConstants.DOWN_COPY_KEY_STROKE;
import static common.constant.KeyStrokeConstants.PASTE_KEY_STROKE;

/**
 * 表格基类
 *
 * @param <T> 表格模型类
 */
@Slf4j
public abstract class DefaultTable<T extends Serializable> extends JTable implements BaseView, TableMenuCallback<T> {
    @Getter
    protected List<TableRowHeightChangeListener> tableRowHeightChangeListeners;
    @Getter
    private final Set<TableMenuCallback<T>> tableMenuCallbackSet = new HashSet<>();
    @Getter
    @Setter
    private boolean headerSelected = false;
    @Getter
    @Setter
    protected JPopupMenu popupMenu;
    //@Setter
    @Getter
    private RowHeaderTable rowHeaderTable;
    @Getter
    private SideBarTable sideBarTable;
    @Getter
    @Setter
    private JScrollPane scrollPane;
    @Getter
    private final List<T> tableList = new ArrayList<>();
    private final Color defaultHeaderBackground = getTableHeader().getBackground();
    private final Map<Integer, Class<?>> columnClassMap = new HashMap<>();
    private InputMap inputMap;
    private ActionMap actionMap;
    @Getter
    protected TableActionManager<T> tableActionManager;

    @Getter
    protected Point mouseClickPoint;

    public void setRowHeaderTable(RowHeaderTable rowHeaderTable) {
        this.rowHeaderTable = rowHeaderTable;

        // 这一步是让rowHeaderTable与DefaultTable的选择状态同步
        this.rowHeaderTable.setSelectionModel(getSelectionModel());

        // 这一步是让rowHeaderTable获取与DefaultTable一样的功能
        this.rowHeaderTable.setComponentPopupMenu(this.popupMenu);
        this.rowHeaderTable.setInputMap(WHEN_FOCUSED, inputMap);
        this.rowHeaderTable.setActionMap(actionMap);

        //渲染器
        RowHeaderRenderer rowHeaderRenderer = new RowHeaderRenderer();
        this.rowHeaderTable.getColumnModel().getColumn(0).setCellRenderer(rowHeaderRenderer);

        // debug
        //getSelectionModel().addListSelectionListener(new ListSelectionListener() {
        //    @Override
        //    public void valueChanged(ListSelectionEvent e) {
        //        if (!e.getValueIsAdjusting()) {
        //            int[] selectedRows = getSelectedRows();
        //            //int[] selectedRows = {0, 2, 4, 6};
        //            System.out.println("selectRows:" + Arrays.toString(selectedRows));
        //            System.out.println(getSelectionModel());
        //            System.out.println("MainTable:" + Arrays.toString(rowHeaderTable.getSelectedRows()));
        //            System.out.println(rowHeaderTable.getSelectionModel());
        //
        //            //setRowSelectionInterval(selectedRows[0], selectedRows[selectedRows.length - 1]);
        //            //refTable.setRowSelectionInterval(selectedRows[0], selectedRows[selectedRows.length - 1]);
        //            //for (int selectedRow : selectedRows) {
        //            //    refTable.getSelectionModel().addSelectionInterval(selectedRow, selectedRow);
        //            //}
        //        }
        //    }
        //});

    }

    public DefaultTable() {
        initTable(getColumns(), 0);
    }

    public DefaultTable(TableModel tableModel) {
        super(tableModel);
        initTable();
    }

    public DefaultTable(int rowCount) {
        initTable(getColumns(), rowCount);
    }

    public DefaultTable(Object[] columnNames, int rowCount) {
        initTable(columnNames, rowCount);
    }

    private void initTable(Object[] columnNames, int rowCount) {
        setModel(new DefaultTableModel(columnNames, rowCount) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                //TODO: 替换为setColumnClass
                if (columnClassMap.containsKey(columnIndex)) {
                    return columnClassMap.get(columnIndex);
                }
                return Object.class;
            }

//            @Override
//            public int getRowCount() {
//                return tableList.size();
//            }
//
//
//            @Override
//            public Object getValueAt(int rowIndex, int columnIndex) {
//                T row = tableList.get(rowIndex);
//                return convertData(row)[columnIndex];
//            }

        });
        initTable();
    }

    protected void setDropTargetListener(DropTargetListener dropTargetListener) {
        new DropTarget(this, DnDConstants.ACTION_COPY_OR_MOVE, dropTargetListener, true);
    }


    private void initTable() {
        setShowGrid(true);
        tableActionManager = initTableActionManager();
        setDefaultTableHeader();
        TableCellRenderer headerRenderer = initTableHeaderCellRenderer();
        if (headerRenderer != null) {
            getTableHeader().setDefaultRenderer(headerRenderer);
        }
        setFontSize(12);
        setTableFont();
        setRowHeight(UiConstants.DEFAULT_ROW_HEIGHT);   //default row Height
        setTableRenderer();
        setPreferredColumn();
        setTableHeaderConfig();
        setRowSelectionAllowed(true);
        setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        TableCellListener.listen(this, tableActionManager.getCellEditAction());
        //禁止列拖动
        getTableHeader().setReorderingAllowed(false);
        //rowHeaderTable.setListSelectionModel(getSelectionModel());
        tableRowHeightChangeListeners = new ArrayList<>();
    }

    protected abstract void setDefaultTableHeader();

    public abstract void setColumnWidth(int columnWidth);

    @Override
    protected boolean processKeyBinding(KeyStroke ks, KeyEvent e, int condition, boolean pressed) {
        if (e.getKeyCode() == KeyEvent.VK_ESCAPE) {
            return false; // 禁用Esc键编辑行为
        }
        return super.processKeyBinding(ks, e, condition, pressed);
    }

    public void setColumnClass(int column, Class<?> clazz) {
        columnClassMap.put(column, clazz);
    }

    public TransferHandler createTableTransferHandler() {
        return null;
    }

    public JScrollPane addScrollPane() {
        return new JScrollPane(this);
    }

    public JScrollPane addScrollRowHeader() {
        return addScrollRowHeader(1, false);
    }

    public JScrollPane addScrollRowHeader(int rowStartIndex, boolean autoScroll) {
        return addScrollRowHeader(rowStartIndex, autoScroll, null);
    }

    public JScrollPane addAutoScrollRowHeader(int rowStartIndex, Integer width) {
        return addScrollRowHeader(rowStartIndex, true, width);
    }

    public JScrollPane addScrollRowHeader(int rowStartIndex, boolean autoScroll, Integer width) {
        //将table加入JScrollPane
        JScrollPane scrollPane;
        if (autoScroll) {
            scrollPane = new AutoScrollPane(this);
        } else {
            scrollPane = new JScrollPane(this);
        }
        //将rowHeaderTable作为row header加入JScrollPane的RowHeaderView区域
        RowHeaderTable rowHeaderTable = new RowHeaderTable(this, rowStartIndex, width);
        scrollPane.setRowHeaderView(rowHeaderTable);
        setRowHeaderTable(rowHeaderTable);
        setScrollPane(scrollPane);
        return scrollPane;
    }

    public JScrollPane addScrollSideAndRowHeader(int rowStartIndex, boolean autoScroll, Integer width) {
        //将table加入JScrollPane
        JScrollPane scrollPane;
        if (autoScroll) {
            scrollPane = new AutoScrollPane(this);
        } else {
            scrollPane = new JScrollPane(this);
        }
        //将rowHeaderTable作为row header加入JScrollPane的RowHeaderView区域
        RowHeaderTable rowHeaderTable = new RowHeaderTable(this, rowStartIndex);
        this.sideBarTable = new SideBarTable(this, rowStartIndex, width);
        Box box = Box.createHorizontalBox();
        // 侧边栏表格容器（垂直对齐顶部）
        Box sideBarContainer = Box.createVerticalBox();
        sideBarContainer.add(sideBarTable);
        sideBarContainer.add(Box.createVerticalGlue()); // 填充剩余空间
        sideBarContainer.setAlignmentY(Component.TOP_ALIGNMENT); // 关键设置
        // 设置背景
        sideBarContainer.setOpaque(true);
        sideBarContainer.setBackground(Color.WHITE);

        // 行标题表格容器（垂直对齐顶部）
        Box rowHeaderContainer = Box.createVerticalBox();
        rowHeaderContainer.add(rowHeaderTable);
        rowHeaderContainer.add(Box.createVerticalGlue());
        rowHeaderContainer.setAlignmentY(Component.TOP_ALIGNMENT);
        // 设置背景
        rowHeaderContainer.setOpaque(true);
        rowHeaderContainer.setBackground(Color.WHITE);


        box.add(sideBarContainer);
        box.add(rowHeaderContainer);
        scrollPane.setRowHeaderView(box);
        setRowHeaderTable(rowHeaderTable);
        setScrollPane(scrollPane);
        return scrollPane;
    }


    public void scrollToRow(int row) {
        if (scrollPane != null && scrollPane instanceof AutoScrollPane) {
            ((AutoScrollPane) scrollPane).scrollToRow(row);
        }
    }

    /*
     * 添加行表头到滚动面板里
     * @see javax.swing.JTable#configureEnclosingScrollPane()
     */
//    @Override
//    protected void configureEnclosingScrollPane() {
//        super.configureEnclosingScrollPane();
//
//        Container parent = SwingUtilities.getUnwrappedParent(this);
//        if (parent instanceof JViewport) {
//            JViewport port = (JViewport) parent;
//            Container gp = port.getParent();
//            if (gp instanceof JScrollPane) {
//                JScrollPane scrollPane = (JScrollPane) gp;
//                scrollPane.setRowHeaderView(rowHeader);
//                rowHeaderAutoShow = true;
//            }
//        }
//    }

    public String getTableListOfJson() {
        return StringUtils.toJsonString(tableList);
    }

    @Override
    public void createView() {
        createMenu();
        initData();
        afterDataLoaded();
    }


    @Override
    public void createActions() {
        getTableHeader().addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                selectTableHeader();
                super.mouseClicked(e);
            }
        });
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mouseButtonClicked();
                super.mouseClicked(e);
            }

            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    mouseRightButtonClick(e);
                }
                super.mousePressed(e);
            }
        });
    }

    protected void createKeyStroke() {
        inputMap = getInputMap(JTable.WHEN_FOCUSED);
        actionMap = getActionMap();
    }

    protected void setKeyStroke(String actionKey, String keyStrokeString, Action action) {
        inputMap.put(KeyStroke.getKeyStroke(keyStrokeString), actionKey);
        actionMap.put(actionKey, action);
    }

    protected void setKeyStroke(String actionKey, KeyStroke keyStroke, Action action) {
        inputMap.put(keyStroke, actionKey);
        actionMap.put(actionKey, action);
    }

    private void addCopyKeyStroke() {
        setKeyStroke(KeyStrokeConstants.COPY_ACTION_KEY, KeyStrokeConstants.COPY_KEY_STROKE, tableActionManager.getCopyAction());
    }

    private void addPasteKeyStroke() {
        setKeyStroke(KeyStrokeConstants.PASTE_ACTION_KEY, KeyStrokeConstants.PASTE_KEY_STROKE, tableActionManager.getPasteAction());
    }

    // new
    protected void addDownCopyKeyStroke() {
        setKeyStroke("DownCopy", "ctrl D", tableActionManager.getDownCopyAction());
    }

    private void addDeleteKeyStroke() {
        setKeyStroke(KeyStrokeConstants.DELETE_ACTION_KEY, KeyStrokeConstants.DELETE_KEY_STROKE, tableActionManager.getDeleteAction());
    }

    protected void addDelMenu() {
        makePopupMenu("删除", KeyStrokeConstants.DELETE_KEY_STROKE,
                tableActionManager.getDeleteRowActionListener());
        addDeleteKeyStroke();
    }

    protected void addCopyMenu() {
        makePopupMenu("复制", KeyStrokeConstants.COPY_KEY_STROKE,
                tableActionManager.getCopyRowActionListener());
        addCopyKeyStroke();
    }

    protected void addPasteMenu() {
        makePopupMenu("粘贴", PASTE_KEY_STROKE,
                tableActionManager.getPasteRowActionListener());
        addPasteKeyStroke();
    }

    protected void addDownCopyMenu() {
        makePopupMenu("复制到下一行", DOWN_COPY_KEY_STROKE,
                tableActionManager.getDownCopyAction());
        addDownCopyKeyStroke();
    }

    protected void addClearMenu() {
        makePopupMenu("清空表格", null,
                tableActionManager.getClearTableActionListener());
    }


    protected void addLineWrapStroke() {
        setKeyStroke(KeyStrokeConstants.LINE_WRAP_ACTION_KEY, KeyStroke.getKeyStroke(KeyEvent.CTRL_MASK, KeyEvent.ALT_DOWN_MASK), tableActionManager.getLineWrapAction());
    }
//    protected void addCopyCellMenu() {
//        makePopupMenu("复制单元格", null,  tableActionManager.getCopyCellAction());
//    }
//    protected void addPasteCellMenu() {
//        makePopupMenu("粘贴单元格", null,  tableActionManager.getPasteCellAction());
//    }

    protected void makeSubmenu(String menuName, List<JMenuItem> menuItemList) {
        if (popupMenu == null) {
            return;
        }
        JMenu menu = new JMenu(menuName);
        for (JMenuItem item : menuItemList) {
            menu.add(item);
        }
        popupMenu.add(menu);
    }


    protected JMenuItem makeMenuItem(String menuItemName, String keyStroke, ActionListener actionListener) {
        JMenuItem item = new JMenuItem(menuItemName);
        item.addActionListener(actionListener);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(keyStroke)) {
            item.setAccelerator(KeyStroke.getKeyStroke(keyStroke));
        }
        return item;
    }

    protected void makePopupMenu(String menuItemName, ActionListener actionListener) {
        makePopupMenu(menuItemName, null, actionListener);
    }

    protected void makePopupMenu(String menuItemName, String keyStroke, ActionListener actionListener) {
        if (popupMenu == null) {
            return;
        }
        popupMenu.add(makeMenuItem(menuItemName, keyStroke, actionListener));
    }

    protected void addSubMenu(String menuItemName, ActionListener actionListener, JMenu menu) {
        addSubMenu(menuItemName, null, actionListener, menu);
    }

    protected void addSubMenu(String menuItemName, String keyStroke, ActionListener actionListener, JMenu menu) {
        if (menu == null) {
            return;
        }
        menu.add(makeMenuItem(menuItemName, keyStroke, actionListener));
    }

    protected void addMenu(JMenu menu) {
        if (popupMenu == null) {
            return;
        }
        popupMenu.add(menu);
    }

    public void addMenuSeparator() {
        popupMenu.addSeparator();
    }


    protected JMenuItem findMenuItemByName(String name) {
        Component[] components = popupMenu.getComponents();
        for (Component component : components) {
            JMenuItem menuItem = findMenuItem(component, name);
            if (menuItem != null) {
                return menuItem;
            }
        }
        return null;
    }

    protected JMenuItem findMenuItem(Component component, String name) {
        if (component instanceof JMenu) {
            JMenu menu = (JMenu) component;
            Component[] menuComponents = menu.getMenuComponents();
            for (Component menuComponent : menuComponents) {
                if (menuComponent instanceof JMenuItem) {
                    JMenuItem jMenuItem = (JMenuItem) menuComponent;
                    if (name.equals(jMenuItem.getText())) {
                        return jMenuItem;
                    }
                }
            }
        } else if (component instanceof JMenuItem) {
            JMenuItem jMenuItem = (JMenuItem) component;
            if (name.equals(jMenuItem.getText())) {
                return jMenuItem;
            }
        }
        return null;
    }

    protected void createMenu() {
        popupMenu = new JPopupMenu();
        setComponentPopupMenu(popupMenu);
        createKeyStroke();
    }

    protected void mouseButtonClicked() {
        resetTableHeader();
    }

    protected void mouseRightButtonClick(MouseEvent e) {
        //通过点击位置找到点击为表格中的行
        int focusedRowIndex = rowAtPoint(e.getPoint());
        if (focusedRowIndex == -1) {
            return;
        }
        if (getSelectedRows().length <= 1) {
            setRowSelectionInterval(focusedRowIndex, focusedRowIndex);
        }
//        if (popupMenu == null) {
//            createMenu();
//        }
        mouseClickPoint = e.getPoint();
//        popupMenu.show(this, e.getX(), e.getY());
    }

    protected void makeCellCenter() {
        DefaultTableCellRenderer tcr = new DefaultTableCellRenderer();//单元格渲染器
        tcr.setHorizontalAlignment(SwingConstants.CENTER);//居中显示
        setDefaultRenderer(Object.class, tcr);
    }

    public void hiddenColumn(int modelColumnIndex) {
        TableUtil.hideColumn(this, modelColumnIndex);
    }

    protected void showColumn(int modelColumnIndex, int columnWidth) {
        TableUtil.showColumn(this, modelColumnIndex, columnWidth);
    }

    public void selectRow(int row) {
        selectRow(row, true);
    }

    public void selectRow(int row, boolean clearBefore) {
        if (row >= getRowCount()) {
            return;
        }
        if (clearBefore) {
            setRowSelectionInterval(row, row);
        } else {
            addRowSelectionInterval(row, row);
        }
        repaint();
    }

    public void selectLastRow() {
        int lastRow = getRowCount() - 1;
        if (lastRow >= 0) {
            selectRow(lastRow);
        }
    }

    protected void setFontSize(int size) {
        SwingUtil.setFontSize(this, size);
    }

    protected int getNextTableRow() {
        // return isHeaderSelected() ? 0 : getSelectedRow() == -1 ? getRowCount() : getSelectedRow() + 1;
        int result;
        if (isHeaderSelected()) {
            result = 0;
        } else {
            //int selectedRow = getSelectedRow();
            int[] selectedRows = getSelectedRows();
            int selectedRow;
            if (selectedRows.length == 0) { // 未选中任意行，跳到下一个条件
                selectedRow = -1;
            } else {
                selectedRow = selectedRows[selectedRows.length - 1];
            }

            if (selectedRow == -1) {
                result = getRowCount();
            } else {
                result = selectedRow + 1;
            }
        }
        return result;
    }

    public List<T> getSelectedRowsData() {
        int[] rows = getSelectedRows();
        List<T> data = new ArrayList<>();
        for (int row : rows) {
            data.add(tableList.get(row));
        }
        return data;
    }

    // 获取选中行及以后的数据
    public List<T> getSelectedRowsAfterData() {
        int[] rows = getSelectedRows();
        List<T> data = new ArrayList<>();
        if (rows.length > 0) {
            int startIndex = rows[rows.length - 1];
            for (int i = startIndex; i < tableList.size(); i++) {
                data.add(tableList.get(i));
            }
        }
        return data;
    }

    public T getSelectedRowData() {
        int selectedRow = getSelectedRow();
        return tableList.get(selectedRow);
    }

    public List<T> getAllRowsData() {
        List<T> data = new ArrayList<>();
        int rowCount = getRowCount();
        for (int row = 0; row < rowCount; row++) {
            data.add(tableList.get(row));
        }
        return data;
    }

    protected void resetTableHeader() {
        setHeaderSelected(false);
        getTableHeader().setBackground(defaultHeaderBackground);
    }

    protected void selectTableHeader() {
        setHeaderSelected(true);
        getTableHeader().setBackground(getSelectionBackground());
        clearSelection();
    }

    public T getRow(int row) {
        return tableList.get(row);
    }

    public T getRow() {
        int row = getSelectedRow();
        if (row == -1) {
            return null;
        }
        return tableList.get(row);
    }

    /**
     * 替换行数据
     *
     * @param row  行
     * @param data 数据
     */
    public boolean replaceRowData(int row, T data) {
        tableList.set(row, data);
        Object[] objectArray = convertData(data);
        for (int col = 0; col < objectArray.length; col++) {
            setValueAt(objectArray[col], row, col);
        }
        refreshRow(row, true, false);
        return true;
    }

    public int addRowData(T data, boolean adjust, boolean updateRowHeight) {
        return addRowData(data, adjust);
    }

    public int addRowData(T data) {
        return addRowData(data, true);
    }

    public int addRowData(T data, Object[] objectData) {
        return addRowData(data, objectData, true);
    }

    public void addRowData(int row, T data) {
        addRowData(row, data, true);
    }

    public int addRowData(T data, boolean updateRowHeight) {
        return addRowData(data, convertData(data), updateRowHeight);
    }

    public int addRowData(T data, Object[] objectData, boolean updateRowHeight) {
        int row = getNextTableRow();
        return addRowData(row, data, objectData, updateRowHeight);
    }

    public int addRowData(int row, T data, boolean updateRowHeight) {
        return addRowData(row, data, convertData(data), updateRowHeight);
    }

    public int addRowData(int row, T data, Object[] objectData, boolean updateRowHeight) {
        tableList.add(row, data);
        ((DefaultTableModel) getModel()).insertRow(row, objectData);
        refreshRow(row, true, true, updateRowHeight);   //excel渲染行，不需要调整column，只改变行高
        return row;
    }


    protected void refreshRow(int row, boolean updateRowHeight, boolean selected, boolean adjust) {
        if (selected) {
            setRowSelectionInterval(row, row);
        }
        resetTableHeader();
        repaint();
        requestFocusInWindow();
        if (updateRowHeight) {
            //FIXME：耗时
            updateRowHeight(row);
        }
        if (adjust) {
            DefaultTableModel model = (DefaultTableModel) getModel();
            if (model != null) {
                adjustRowIndentation(model);
            }
        }
        // 显式通知表格模型该行数据已更改
        DefaultTableModel model = (DefaultTableModel) getModel();
        if (model != null) {
            model.fireTableRowsUpdated(row, row);
        }
    }


    protected void refreshRow(int row, boolean updateRowHeight, boolean selected) {
        refreshRow(row, updateRowHeight, selected, true);
    }

    //优化避免数组越界
    public static void adjustRowIndentation(DefaultTableModel model) {
        // 检查模型是否为空或没有列
        if (model == null || model.getColumnCount() <= 2) {
            return; // 如果列数不足，直接返回
        }
        Stack<String> scopeStack = new Stack<>();
        // 遍历表格的所有行
        for (int i = 0; i < model.getRowCount(); i++) {
            // 确保第 2 列存在
            if (model.getColumnCount() <= 2) {
                continue; // 如果列数不足，跳过当前行
            }
            // 获取第 2 列的单元格值
            Object value = model.getValueAt(i, 2);
            if (value == null) {
                continue; // 如果单元格值为 null，跳过当前行
            }
            // 转换为字符串并去除首尾空格
            String content = value.toString().trim();
            // 计算新的缩进内容
            String newContent = calculateIndentation(content, scopeStack);
            // 更新表格中的单元格值
            model.setValueAt(newContent, i, 2);
        }
    }

    private static String calculateIndentation(String keyword, Stack<String> stack) {
        // 处理结束标签
        if (keyword.equals(CommonMethods.endLoop.getMethodName()) || keyword.equals(CommonMethods.endIf.getMethodName())) {
            if (!stack.isEmpty()) stack.pop();
        }

        // 计算当前缩进级别
        int indentLevel = stack.size();
        String indent = new String(new char[indentLevel * 2]).replace('\0', ' ');

        // 处理开始标签
        if (keyword.equals(CommonMethods.beginLoop.getMethodName()) || keyword.equals(CommonMethods.ifExpressionSuccess.getMethodName())) {
            indent = new String(new char[stack.size() * 2]).replace('\0', ' ');
            stack.push(keyword.startsWith(CommonMethods.beginLoop.getMethodName()) ? "LOOP" : "IF");
        } else if (keyword.equals(CommonMethods.elseExpression.getMethodName())) {
            indentLevel = Math.max(stack.size() - 1, 0);
            indent = new String(new char[indentLevel * 2]).replace('\0', ' ');
        }
        // 处理结束标签的特殊缩进
        if (keyword.equals(CommonMethods.endLoop.getMethodName()) || keyword.equals(CommonMethods.endIf.getMethodName())) {
            indent = new String(new char[stack.size() * 2]).replace('\0', ' ');
        }
        return indent + keyword;
    }

    private static String createIndentation(int level) {
        StringBuilder indentation = new StringBuilder();
        for (int i = 0; i < level * 4; i++) {
            indentation.append(" ");
        }
        return indentation.toString();
    }

    public int deleteRow(int row) {
        tableList.remove(row);
        ((DefaultTableModel) getModel()).removeRow(row);
        int lastRow;
        if (row <= 0) {
            //删除的是第一行
            lastRow = getRowCount() > 0 ? 0 : -1;
        } else {
            lastRow = row - 1;
        }
        if (lastRow >= 0) {
            selectRow(lastRow);
        }
        return lastRow;
    }

    public void clearTable() {
        tableList.clear();
        DefaultTableModel tableModel = ((DefaultTableModel) getModel());
        tableModel.getDataVector().clear(); //清除表格数据
        tableModel.fireTableDataChanged();  //通知模型更新
    }

    public void setColor(int row, Color color) {
        setColor(row, null, color);
        TableCellRenderer headRenderer = this.rowHeaderTable.getColumnModel().getColumn(0).getCellRenderer();
        ((RowHeaderRenderer) headRenderer).setColorForCell(row, 0, color);
    }

    public void setColor(Integer row, Integer column, Color color) {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().setColor(row, column, color);
        }

    }

    public void clearColor() {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().clearColor();
        }
        TableCellRenderer headRenderer = this.rowHeaderTable.getColumnModel().getColumn(0).getCellRenderer();
        ((RowHeaderRenderer) headRenderer).clearColor();
        this.rowHeaderTable.repaint();
        repaint();
    }

    public void clearColor(int row) {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().clearColor(row);
        }
    }


    public void addRowHeightChangeListener(TableRowHeightChangeListener tableRowHeightChangeListener) {
        if (tableRowHeightChangeListener != null) {
            this.tableRowHeightChangeListeners.add(tableRowHeightChangeListener);
        }
    }

    public void updateRowHeight(int row) {
        int rowHeight = getRowHeight();
        for (int column = 0; column < getColumnCount(); column++) {
            Component comp = prepareRenderer(getCellRenderer(row, column), row, column);
            rowHeight = Math.max(rowHeight, comp.getPreferredSize().height);
        }
        int old = getRowHeight(row);
        if (old != rowHeight) {
            setRowHeight(row, rowHeight);
            if (tableRowHeightChangeListeners != null) {
                for (TableRowHeightChangeListener listener : tableRowHeightChangeListeners){
                    listener.rowHeightChange(row, rowHeight);
                }

            }
        }
    }

    protected void updateRowHeights() {
        for (int row = 0; row < getRowCount(); row++) {
            updateRowHeight(row);
        }
    }

    public <V> List<V> getColumnList(int column, Class<V> clazz) {
        List<V> list = new ArrayList<>();
        for (int row = 0; row < getRowCount(); row++) {
            V value = clazz.cast(getValueAt(row, column));
            if (value != null)
                list.add(value);
        }
        return list;
    }

    public <V> List<V> getTableModelRowData(int row, Class<V> clazz) {
        List<V> list = new ArrayList<>();
        TableModel model = getModel();
        for (int column = 1; column < model.getColumnCount(); column++) {
            V value = clazz.cast(model.getValueAt(row, column));
            list.add(value);
        }
        return list;
    }


    protected void clearColumn(int column) {
        for (int row = 0; row < getRowCount(); row++) {
            setValueAt("", row, column);
        }
    }

    public void fitTableColumnWidth() {
        EventQueue.invokeLater(() -> {
            for (int column = 0; column < getModel().getColumnCount(); column++) {
                TableColumn tableColumn = getColumnModel().getColumn(column);
                tableColumn.setPreferredWidth(getColumnPreferredWidth(column));
            }
        });
    }

    private int getColumnPreferredWidth(int column) {
        int marginRight = 3;
        String columnTitle = getModel().getColumnName(column);
        TableCellRenderer headerRenderer = getColumnModel().getColumn(column).getHeaderRenderer();
        if (headerRenderer == null) {
            headerRenderer = getTableHeader().getDefaultRenderer();
        }
        Component comp = headerRenderer.getTableCellRendererComponent(this, columnTitle, false, false, 0, column);
        int headerWidth = comp.getPreferredSize().width;
        int cellWidth = 0;
        for (int row = 0; row < getRowCount(); row++) {
            //comp = getCellRenderer(j, i).getTableCellRendererComponent(this, this.getValueAt(j, i), false, false, j, i);
            comp = getDefaultRenderer(getModel().getColumnClass(column)).getTableCellRendererComponent(this, getValueAt(row, column), false, false, row, column);
            cellWidth = Math.max(cellWidth, comp.getPreferredSize().width);
        }
        return Math.max(headerWidth, cellWidth) + marginRight;
    }

    /**
     * 以下是回调
     **/

    protected void updateTableActivated(ActionEvent e) {

    }

    @Override
    public void deleteRowsActivated(int[] rows) {
        for (int row : rows) {
            deleteRow(row);
        }
    }

    @Override
    public boolean clearTableActivated() {
        clearTable();
        return true;
    }

    @Override
    public void pasteRowsActivated(List<T> clipBoard) {
        for (T data : clipBoard) {
            addRowData(data);
        }
    }

    /**
     * 结束回调
     **/

    protected void initData() {

    }

    protected abstract Object[] convertData(T data);

    protected abstract String[] getColumns();

    protected void changeColumnWidth() {

    }

    protected void save() {

    }

    protected void swapTableList(Map<Integer, Integer> map) {

    }

    protected void afterDataLoaded() {

    }

    protected void setPreferredColumn() {

    }

    protected void setTableHeaderConfig() {

    }


    protected void setTableFont() {
    }

    protected TableCellRenderer initTableHeaderCellRenderer() {
        return null;
    }

    protected void setTableRenderer() {
    }

    protected TableActionManager<T> initTableActionManager() {
        return new TableActionManager<>(this);
    }

    protected void fireTableDataChanged() {
        ((DefaultTableModel) getModel()).fireTableDataChanged();  //通知模型更新
    }

    public List<String> getRowData(int rowIndex) {
        List<String> rowData = new ArrayList<>();
        DefaultTableModel tableModel = (DefaultTableModel) getModel();
        // 获取列数
        int columnCount = tableModel.getColumnCount();
        // 遍历每一列，获取该行的数据
        for (int col = 1; col < columnCount; col++) {
            Object cellValue = tableModel.getValueAt(rowIndex, col);
            if (cellValue == null) {
                cellValue = "";
            }
            rowData.add(String.valueOf(cellValue));
        }
        return rowData;
    }

}
