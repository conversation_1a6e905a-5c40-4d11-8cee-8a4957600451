package ui.base.dialogs;

import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.ArrayList;
import java.util.List;

public class RowHeightDialog extends JDialog implements ItemListener {
    private final ExcelCaseTable table;
    private ButtonGroup buttonGroups;
    private JRadioButton singleRowRadioButton;
    private JRadioButton checkedRowsRadioButton;
    private JSpinner rowHeightSpinner;
    private JButton okButton;
    private JButton cancelButton;
    private boolean isSingleRow;

    public RowHeightDialog(ExcelCaseTable table) {
        this.table = table;
        createDialog();
        createDialogActions();
    }

    //TODO: 增加可多选的列名
    private void createDialog() {
        setTitle("设置行高");
        setBounds(600, 400, 400, 200);
        getContentPane().setLayout(new BorderLayout());
        rowHeightSpinner = new JSpinner();
        rowHeightSpinner.setValue(100);
        rowHeightSpinner.setModel(new SpinnerNumberModel(100, 0, null, 1));
        rowHeightSpinner.setPreferredSize(new Dimension(100, 30));
        JPanel widthPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        widthPanel.add(new JLabel("行高"));
        widthPanel.add(rowHeightSpinner);
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        okButton = new JButton("确定");
        cancelButton = new JButton("取消");
        buttonPanel.add(okButton);
        buttonPanel.add(cancelButton);
        Box vbox = Box.createVerticalBox();
        vbox.add(createBusTypeBox());
        vbox.add(widthPanel);
        add(vbox, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private Box createBusTypeBox() {
        Box box = Box.createHorizontalBox();
        buttonGroups = new ButtonGroup();
        singleRowRadioButton = new JRadioButton("单行");
        checkedRowsRadioButton = new JRadioButton("选中行");
        buttonGroups.add(singleRowRadioButton);
        buttonGroups.add(checkedRowsRadioButton);
        singleRowRadioButton.addItemListener(this);
        checkedRowsRadioButton.addItemListener(this);
        singleRowRadioButton.setSelected(true);
        box.add(new JLabel("设置行数:"));
        box.add(singleRowRadioButton);
        box.add(checkedRowsRadioButton);
        return box;
    }


    private void createDialogActions() {
        okButton.addActionListener(e -> saveConfig());
        cancelButton.addActionListener(e -> closeDialog());
    }

    private void saveConfig() {
        int rowHeight = (int) rowHeightSpinner.getValue();
        List<Integer> rowList = new ArrayList<>();
        if (isSingleRow) {
            int clickRow = table.getClickRow();
            rowList.add(clickRow);
        } else {
            rowList.addAll(table.getCheckedRows());
        }
        table.setRowHeight(rowList, rowHeight);
        closeDialog();
    }

    private void closeDialog() {
        setModal(false);
        dispose();
    }

    @Override
    public void itemStateChanged(ItemEvent e) {
        if (e.getStateChange() == ItemEvent.SELECTED) {
            // 根据单选按钮的选择状态来赋值
            isSingleRow = e.getSource() == singleRowRadioButton;
        }
    }
}
