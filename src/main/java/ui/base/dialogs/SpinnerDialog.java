package ui.base.dialogs;

import lombok.Getter;

import javax.swing.*;

public class SpinnerDialog<T extends Number> {
    @Getter
    private final JSpinner spinner;
    private final JOptionPane optionPane;
    @Getter
    private final JDialog dialog;

    protected JComponent hookComponent() {
        return null;
    }

    public SpinnerDialog(String title, String message, int initValue) {
        JPanel panel = new JPanel();
        spinner = new JSpinner();
        spinner.setModel(new SpinnerNumberModel(initValue, 1, Integer.MAX_VALUE, 1));
        panel.add(new JLabel(message));
        panel.add(spinner);
        JComponent hookComponent = hookComponent();
        if (hookComponent != null) {
            panel.add(hookComponent);
        }

        optionPane = new JOptionPane();
        optionPane.setMessage(panel);
        optionPane.setMessageType(JOptionPane.INFORMATION_MESSAGE);
        optionPane.setOptionType(JOptionPane.OK_CANCEL_OPTION);

        dialog = optionPane.createDialog(title);
        spinner.requestFocus();
        dialog.setVisible(true);
    }

    protected boolean isValueValid() {
        return optionPane.getValue() != null && ((int) optionPane.getValue() == JOptionPane.OK_OPTION);
    }

    @SuppressWarnings("unchecked")
    protected T getSpinnerValue() {
        return (T) spinner.getValue();
    }

    public T getValue() {
        return isValueValid() ? getSpinnerValue() : null;
    }
}
