package ui.base.dialogs;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import net.miginfocom.swing.MigLayout;

import javax.swing.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class MultiRowDialog<T extends Number> {
    @Getter
    private final List<JSpinner> spinners;
    private final JOptionPane optionPane;
    @Getter
    private final JDialog dialog;

    @Data
    @AllArgsConstructor
    public static class DialogItem {
        private String label;
        private int initValue;
    }

    protected JComponent hookComponent() {
        return null;
    }

    public MultiRowDialog(String title, DialogItem... dialogItems) {
        spinners = new ArrayList<>();
        JPanel panel = new JPanel(new MigLayout());
        for (DialogItem dialogItem : dialogItems) {
            JSpinner spinner = new JSpinner();
            spinner.setModel(new SpinnerNumberModel(dialogItem.getInitValue(), 1, Integer.MAX_VALUE, 1));
            panel.add(new JLabel(dialogItem.getLabel()));
            panel.add(spinner);
            spinner.setName(dialogItem.getLabel());
            spinners.add(spinner);
            spinner.requestFocus();
        }
        JComponent hookComponent = hookComponent();
        if (hookComponent != null) {
            panel.add(hookComponent);
        }

        optionPane = new JOptionPane();
        optionPane.setMessage(panel);
        optionPane.setMessageType(JOptionPane.INFORMATION_MESSAGE);
        optionPane.setOptionType(JOptionPane.OK_CANCEL_OPTION);

        dialog = optionPane.createDialog(title);
        dialog.setVisible(true);
    }

    protected boolean isValueValid() {
        return optionPane.getValue() != null && ((int) optionPane.getValue() == JOptionPane.OK_OPTION);
    }

    @SuppressWarnings("unchecked")
    protected Map<String, T> getSpinnerValueMap() {
        Map<String, T> values = new LinkedHashMap<>();
        for (JSpinner spinner : spinners) {
            values.put(spinner.getName(), (T) spinner.getValue());
        }
        return values;
    }

    public Map<String, T> getValueMap() {
        return isValueValid() ? getSpinnerValueMap() : null;
    }
}
