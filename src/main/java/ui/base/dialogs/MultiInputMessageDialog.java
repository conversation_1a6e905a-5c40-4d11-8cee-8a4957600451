package ui.base.dialogs;

import ui.base.component.CombinedRow;

import javax.swing.*;

/**
 * 多行信息输入对话框
 */
public class MultiInputMessageDialog {
    private final JPanel inputPanel;

    private final Box box;

    private int index = 1;

    private final String label;

    public MultiInputMessageDialog(String label) {
        this.label = label;
        box = Box.createVerticalBox();
        inputPanel = new JPanel();
        inputPanel.add(box);
        addRow();
    }

    private void addRow() {
        CombinedRow combinedRow = new CombinedRow(String.format("%s%d:", label, index++));
        box.add(combinedRow);
        combinedRow.addActionListener(e -> addRow());
        inputPanel.invalidate();
    }

    public void show(String title) {
        JOptionPane.showMessageDialog(null, inputPanel, title, JOptionPane.PLAIN_MESSAGE);
    }

    public static void main(String[] args) {
        new MultiInputMessageDialog("电压").show("触发电压设置");
    }

}
