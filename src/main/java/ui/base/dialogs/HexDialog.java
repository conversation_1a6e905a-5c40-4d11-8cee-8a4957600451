package ui.base.dialogs;

import ui.base.component.FixedHexTextField;

import javax.swing.*;
import java.awt.*;
import java.util.regex.Pattern;

public class HexDialog {

    private static JDialog createDialog(Component parentComponent) {
        JDialog dialog = new JDialog();
        dialog.setTitle("输入十六进制字符串");
        dialog.setModal(true);
        dialog.setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        dialog.setResizable(false);
        return dialog;
    }

    private static JTextField createHexInputTextField() {
        return new FixedHexTextField("HH");
    }

    private static void addComponentsToDialog(JDialog dialog, JTextField textField) {
        JButton okButton = new JButton("确认");
        okButton.addActionListener(e -> dialog.setVisible(false));
        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> {
            textField.setText(null);
            dialog.setVisible(false);
        });

        JPanel panel = new JPanel(new FlowLayout());
        panel.add(new JLabel("0x"));
        panel.add(textField);
        panel.add(okButton);
        panel.add(cancelButton);
        dialog.getRootPane().setDefaultButton(okButton);
        dialog.setContentPane(panel);
        dialog.pack();
        dialog.setLocationRelativeTo(dialog.getParent());
    }

    private static void showDialog(JDialog dialog) {
        dialog.setVisible(true);
    }

    public static String showHexInputDialog(Component parentComponent) {
        JDialog dialog = createDialog(parentComponent);
        JTextField textField = createHexInputTextField();
        addComponentsToDialog(dialog, textField);
        showDialog(dialog);
        return textField.getText();
    }

    public static String showErrorStyleHexDialog(Component parentComponent) {
        // 创建自定义对话框
        JDialog dialog = new JDialog();
        dialog.setTitle("输入十六进制字符串");
        dialog.setModal(true);
        // 创建一个限制为十六进制字符串输入的 JTextField
        JTextField textField = new JTextField();
        textField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            private void checkInput() {
                // 只允许十六进制字符串输入
                String hexPattern = "^[A-Fa-f\\d]*$";
                boolean isValid = Pattern.matches(hexPattern, textField.getText());
                textField.setForeground(isValid ? Color.BLACK : Color.RED);
            }

            @Override
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                checkInput();
            }

            @Override
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                checkInput();
            }

            @Override
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                checkInput();
            }
        });

        // 创建确认和取消按钮
        JButton okButton = new JButton("确认");
        JButton cancelButton = new JButton("取消");
        okButton.addActionListener(e -> dialog.setVisible(false));
        cancelButton.addActionListener(e -> {
            textField.setText(null);
            dialog.setVisible(false);
        });

        //添加组件并显示对话框
        JPanel panel = new JPanel(new FlowLayout());
        panel.add(new JLabel("0x"));
        panel.add(textField);
        panel.add(okButton);
        panel.add(cancelButton);

        dialog.setContentPane(panel);
        dialog.pack();
        dialog.setLocationRelativeTo(parentComponent);
        dialog.setVisible(true);

        return textField.getText();
    }
}
