package ui.service;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.layout.left.display.components.container.can.CanContainer;
import ui.layout.left.display.components.container.can.CanDbcReceiveSettingView;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class CanDbcReceiverControlService {
    private volatile static CanDbcReceiverControlService instance;
    @Getter
    private final Map<String, CanContainer> canContainers = Collections.synchronizedMap(new LinkedHashMap<>());
    @Getter
    private final Map<String, CanDbcReceiveSettingView> canDbcReceiveSettingViews = new ConcurrentHashMap<>();

    /**
     * 获取单例实例
     *
     * @return 服务实例
     */
    public static CanDbcReceiverControlService getInstance() {
        if (instance == null) {
            synchronized (CanDbcReceiverControlService.class) {
                if (instance == null) {
                    instance = new CanDbcReceiverControlService();
                }
            }
        }
        return instance;
    }

    /**
     * 注册CAN容器
     *
     * @param deviceName 设备名称
     * @param container  can容器
     */
    public void registerCanContainer(String deviceName, CanContainer container) {
        canContainers.put(deviceName, container);
        log.info("注册CAN设备容器: {}", deviceName);
    }

    /**
     * 注销CAN容器
     *
     * @param deviceName 设备名称
     */
    public void unregisterCanContainer(String deviceName) {
        canContainers.remove(deviceName);
        log.info("注销CAN设备容器: {}", deviceName);
    }

    public void registerCanDbcSendSettingView(String deviceNameChannel, CanDbcReceiveSettingView canDbcView) {
        canDbcReceiveSettingViews.put(deviceNameChannel, canDbcView);
        log.info("注册canDbcReceiveSettingViews: {}", deviceNameChannel);
    }

}
