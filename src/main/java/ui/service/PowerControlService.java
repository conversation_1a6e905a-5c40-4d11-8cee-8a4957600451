package ui.service;

import ui.layout.left.display.components.container.power.PowerControlPanel;

import java.util.Map;
import java.util.Optional;

/**
 * 电源控制服务类
 * 提供电源电压设置和输出控制功能
 */
public class PowerControlService {

    /**
     * 设置电源输出电压
     *
     * @param value 要设置的电压值(Double类型)
     *              通过PanelManager获取电源控制面板并设置电压
     */
    public void setVoltage(Double value) {
        // 从面板管理器获取所有电源控制面板
        Map<String, PowerControlPanel> powerControls = PanelManager.getInstance().getPowerControls();

        // 获取第一个电源控制面板（如果有的话）
        Optional<PowerControlPanel> firstPanel = powerControls.values().stream().findFirst();

        // 如果存在电源控制面板，则设置电压值
        firstPanel.ifPresent(powerControlPanel -> {
            // 调用电源控制面板的setVoltage方法设置电压
            powerControlPanel.setVoltage(value);
        });
    }

    /**
     * 控制电源输出开关状态
     *
     * @param isOn 电源开关状态(Boolean类型)
     *             true表示开启电源输出
     *             false表示关闭电源输出
     */
    public void controlPowerOutput(Boolean isOn) {
        // 从面板管理器获取所有电源控制面板
        Map<String, PowerControlPanel> powerControls = PanelManager.getInstance().getPowerControls();

        // 获取第一个电源控制面板（如果有的话）
        Optional<PowerControlPanel> firstPanel = powerControls.values().stream().findFirst();

        // 如果存在电源控制面板，则设置输出状态
        firstPanel.ifPresent(powerControlPanel -> {
            // 调用电源控制面板的controlPowerOutput方法设置输出状态
            powerControlPanel.controlPowerOutput(isOn);
        });
    }
}